/**
 * 优惠券数据类型定义
 * 直接使用API接口格式作为组件内部数据结构
 */

// ==================== API接口数据格式 ====================

// 影厅信息
export interface HallInfo {
  hallId: string
  hallName: string
}

// 影院限制信息
export interface CinemaRestriction {
  id?: number
  cinemaId: string
  cinemaName: string
  halls: HallInfo[]
}

// 影片限制信息
export interface FilmRestriction {
  id?: number
  filmId: number
  filmCode: string
  filmName: string
}

// 时段限制信息
export interface PeriodRestriction {
  id?: number
  num: number
  day: number
  start: number
  end: number
}

// 影片版本限制信息
export interface VersionRestriction {
  filmVersion: string
  versionId: string
}

// 票务限制规则（组件主要数据结构）
export interface TicketRestriction {
  couponId?: string
  id?: string
  cinemaRestriction: {
    cinemaScope: number // 影院范围：全部0，指定1，排除2
    cinemas: CinemaRestriction[]
  }
  filmRestriction: {
    filmScope: number // 适用影片：全部影片0，指定影片1，最低价影片2
    films: FilmRestriction[]
  }
  periodRestriction: {
    periodScope: number // 适用时段：全部时段0，指定时段1
    periods: PeriodRestriction[]
  }
  filmVersionRestriction: {
    versionScope: number // 适用影片版本：全部影片版本0，指定影片版本1
    versions: VersionRestriction[]
  }
}

// 完整的优惠券限制规则请求数据
export interface CouponRestrictionRequest {
  couponId?: string
  ticketRestriction?: TicketRestriction
  showRestriction?: any // 演出限制（暂不实现）
  goodsRestriction?: any // 卖品限制（暂不实现）
  exhibitionRestriction?: any // 展览限制（暂不实现）
}

// ==================== 工具函数 ====================

/**
 * 创建默认的票务限制数据
 * @param couponId 优惠券ID
 * @returns 默认的票务限制数据
 */
export function createDefaultTicketRestriction(couponId?: string): TicketRestriction {
  return {
    couponId,
    id: undefined,
    cinemaRestriction: {
      cinemaScope: 0, // 全部影院
      cinemas: []
    },
    filmRestriction: {
      filmScope: 0, // 全部影片
      films: []
    },
    periodRestriction: {
      periodScope: 0, // 全部时段
      periods: []
    },
    filmVersionRestriction: {
      versionScope: 0, // 全部版本
      versions: []
    }
  }
}

/**
 * 创建影院限制项
 * @param cinemaId 影院ID
 * @param cinemaName 影院名称
 * @param halls 影厅列表
 * @returns 影院限制项
 */
export function createCinemaRestriction(
  cinemaId: string,
  cinemaName: string,
  halls: HallInfo[] = []
): CinemaRestriction {
  return {
    cinemaId,
    cinemaName,
    halls
  }
}

/**
 * 创建影片限制项
 * @param filmId 影片ID
 * @param filmCode 影片编码
 * @param filmName 影片名称
 * @returns 影片限制项
 */
export function createFilmRestriction(
  filmId: number,
  filmCode: string,
  filmName: string
): FilmRestriction {
  return {
    filmId,
    filmCode,
    filmName
  }
}

/**
 * 创建时段限制项
 * @param day 星期几
 * @param start 开始时间
 * @param end 结束时间
 * @param num 序号
 * @returns 时段限制项
 */
export function createPeriodRestriction(
  day: number,
  start: number,
  end: number,
  num: number = 1
): PeriodRestriction {
  return {
    num,
    day,
    start,
    end
  }
}

/**
 * 创建版本限制项
 * @param filmVersion 影片版本
 * @param versionId 版本ID
 * @returns 版本限制项
 */
export function createVersionRestriction(
  filmVersion: string,
  versionId: string
): VersionRestriction {
  return {
    filmVersion,
    versionId
  }
}

/**
 * 验证票务限制数据的完整性
 * @param data 票务限制数据
 * @returns 验证结果
 */
export function validateTicketRestriction(data: TicketRestriction): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  try {
    // 验证影院限制
    if (data.cinemaRestriction.cinemaScope > 0 && data.cinemaRestriction.cinemas.length === 0) {
      errors.push('请选择影院')
    }

    // 验证影片限制
    if (data.filmRestriction.filmScope === 1 && data.filmRestriction.films.length === 0) {
      errors.push('请选择指定影片')
    }

    // 验证时段限制
    if (data.periodRestriction.periodScope === 1 && data.periodRestriction.periods.length === 0) {
      errors.push('请设置时段限制')
    }

    // 验证版本限制
    if (data.filmVersionRestriction.versionScope === 1 && data.filmVersionRestriction.versions.length === 0) {
      errors.push('请选择影片版本')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  } catch (error) {
    console.error('验证过程中出现错误:', error)
    return {
      isValid: false,
      errors: ['数据格式错误']
    }
  }
}

/**
 * 构建完整的API请求数据
 * @param ticketRestriction 票务限制数据
 * @param couponId 优惠券ID
 * @returns 完整的API请求数据
 */
export function buildCouponRestrictionRequest(
  ticketRestriction: TicketRestriction,
  couponId?: string
): CouponRestrictionRequest {
  return {
    couponId,
    ticketRestriction: {
      ...ticketRestriction,
      couponId
    }
  }
}

/**
 * 格式化显示文本工具
 */
export const formatDisplayText = {
  cinemaScope: (scope: number): string => {
    const scopeMap = {
      0: '全部影院',
      1: '指定影院',
      2: '排除影院',
    }
    return scopeMap[scope as keyof typeof scopeMap] || '未知'
  },

  filmScope: (scope: number): string => {
    const scopeMap = {
      0: '全部影片',
      1: '指定影片',
      2: '最低价影片',
    }
    return scopeMap[scope as keyof typeof scopeMap] || '未知'
  },

  periodScope: (scope: number): string => {
    const scopeMap = {
      0: '全部时段',
      1: '指定时段',
    }
    return scopeMap[scope as keyof typeof scopeMap] || '未知'
  },

  versionScope: (scope: number): string => {
    const scopeMap = {
      0: '全部版本',
      1: '指定版本',
    }
    return scopeMap[scope as keyof typeof scopeMap] || '未知'
  }
}



/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 比较两个对象是否相等
 * @param obj1 对象1
 * @param obj2 对象2
 * @returns 是否相等
 */
export function isEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) {
    return true
  }

  if (obj1 == null || obj2 == null) {
    return obj1 === obj2
  }

  if (typeof obj1 !== typeof obj2) {
    return false
  }

  if (typeof obj1 !== 'object') {
    return obj1 === obj2
  }

  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false
    }

    if (!isEqual(obj1[key], obj2[key])) {
      return false
    }
  }

  return true
}
