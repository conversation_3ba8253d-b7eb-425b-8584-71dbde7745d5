import type { RouteRecordRaw } from 'vue-router'

const coupons: RouteRecordRaw = {
  path: '/coupons',
  component: () => import('@/layouts/index.vue'),
  redirect: '/coupons',
  name: 'CouponsManagement',
  meta: {
    title: '优惠券管理',
    icon: 'i-ep:ticket',
    // auth: '/adm_coupon',
  },
  children: [
    {
      path: '',
      name: 'CouponsList',
      component: () => import('@/views/market/coupons/list/index.vue'),
      meta: {
        title: '优惠券列表',
        icon: 'ep:list',
        // auth: '/adm_coupon/list',
        breadcrumb: true,
        menu: false,
        activeMenu: '/coupons',
      },
    },

    // {
    //   path: 'old',
    //   name: 'CouponsOld',
    //   component: () => import('@/views/coupon/index.vue') as any,
    //   meta: {
    //     title: '优惠券管理（原版）',
    //     icon: 'ep:ticket',
    //     // auth: '/adm_coupon/query',
    //     keepAlive: true,
    //     activeMenu: '/coupons',
    //   },
    // },
  ],
}

export default coupons
