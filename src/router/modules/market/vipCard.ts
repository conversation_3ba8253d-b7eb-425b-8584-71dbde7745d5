// 市场管理-会员模块
import type { RouteRecordRaw } from 'vue-router'

const vipCard: RouteRecordRaw
  = {
    path: '/market/vipCard',
    name: 'market_vipCard',
    component: () => import('@/layouts/index.vue'),
    meta: {
      title: '会员模块',
      icon: 'i-ep:user',
    },
    children: [
      {
        path: 'gift',
        name: 'market_vipCard_gift',
        component: () => import('@/views/market/vipCard/gift/index.vue'),
        meta: {
          title: '卡赠礼管理',
          icon: 'i-ep:present',
        },
      },
      {
        path: 'policy',
        name: 'market_vipCard_policy',
        component: () => import('@/views/market/vipCard/policy/index.vue'),
        meta: {
          title: '卡政策管理',
          icon: 'i-ep:document',
        },
      },
      {
        path: 'card',
        name: 'market_vipCard_card',
        component: () => import('@/views/market/vipCard/card/index.vue'),
        meta: {
          title: '会员卡管理',
          icon: 'i-ep:credit-card',
        },
      },
    ],
  }

export default vipCard
