/**
 * 活动管理路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

const activitiesRoutes: RouteRecordRaw = {
  path: '/activities',
  component: () => import('@/layouts/index.vue'),
  name: 'Activities',
  // redirect: '/market/activities',
  meta: {
    title: '活动管理',
    icon: 'ep:calendar',
  },
  children: [
    {
      path: '',
      name: 'ActivitiesList',
      component: () => import('@/views/market/activities/index.vue'),
      meta: {
        title: '活动列表',
        icon: 'ep:list',
        menu: false,
        // auth: '/adm_activity/list',
        breadcrumb: false,
        activeMenu: '/activities',
      },
    },
  ],
}

export default activitiesRoutes
