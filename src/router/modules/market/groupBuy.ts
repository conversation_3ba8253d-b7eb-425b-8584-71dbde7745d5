import type { RouteRecordRaw } from 'vue-router'

const groupBuyRoutes: RouteRecordRaw = {
  path: '/market/group-buy',
  name: 'MarketGroupBuy',
  component: () => import('@/layouts/index.vue'),
  meta: {
    title: '团购管理',
    icon: 'i-ep:shopping-bag',
    // auth: '/market/groupBuy',
  },
  children: [
    {
      path: '',
      name: 'MarketGroupBuyList',
      component: () => import('@/views/market/groupBuy/index.vue'),
      meta: {
        title: '团购列表',
        icon: 'i-ep:list',
        // auth: '/market/groupBuy/query',
        breadcrumb: false,
        menu: false,
        activeMenu: '/market/group-buy',
      },
    },
  ],
}

export default groupBuyRoutes
