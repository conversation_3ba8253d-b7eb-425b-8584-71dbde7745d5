import type { RouteRecordRaw } from 'vue-router'

const orders: RouteRecordRaw = {
  path: '/orders',
  component: () => import('@/layouts/index.vue'),
  redirect: '/orders/list',
  name: 'Orders',
  meta: {
    title: '订单管理',
    icon: 'i-ep:shopping-cart-full',
    // auth: '/store/order',
  },
  children: [
    {
      path: 'list',
      name: 'OrdersList',
      component: () => import('@/views/orders/index.vue'),
      meta: {
        title: '订单列表',
        icon: 'i-ep:list',
        auth: '/store/order/query',
        keepAlive: true,
        activeMenu: '/orders',
      },
    },
    {
      path: 'detail/:id',
      name: 'OrdersDetail',
      component: () => import('@/views/orders/detail/[id].vue'),
      meta: {
        title: '订单详情',
        icon: 'i-ep:view',
        auth: '/store/order/detail',
        menu: false,
        activeMenu: '/orders/list',
      },
    },
  ],
}

export default orders
