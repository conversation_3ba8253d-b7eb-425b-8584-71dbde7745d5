/**
 * 商品管理模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

const goodsRoutes: RouteRecordRaw
  = {
    path: '/goods',
    name: 'Goods',
    component: () => import('@/layouts/index.vue'),
    // redirect: '/goods/list',
    meta: {
      title: '商品管理',
      icon: 'ep:goods',
    // order: 3,
    },
    children: [
      {
        path: 'list',
        name: 'GoodsList',
        component: () => import('@/views/goods/index.vue'),
        meta: {
          title: '商品列表',
          icon: 'ep:list',
          keepAlive: true,
          // menu: false,
          activeMenu: '/goods',
        },
      },
      {
        path: 'detail/:id',
        name: 'GoodsDetail',
        component: () => import('@/views/goods/detail/[id].vue'),
        meta: {
          title: '商品详情',
          icon: 'ep:view',
          menu: false,
          activeMenu: '/goods/list',
        },
      },
      {
        path: 'shipping',
        name: 'ShippingTemplate',
        component: () => import('@/views/goods/shipping/index.vue'),
        meta: {
          title: '运费模板',
          icon: 'ep:truck',
          menu: true,
          keepAlive: true,
          activeMenu: '/goods/shipping',
        },
      },
    ],
  }

export default goodsRoutes
