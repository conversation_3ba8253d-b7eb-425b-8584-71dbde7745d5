import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw = {
  path: '/system',
  component: () => import('@/layouts/index.vue'),
  meta: {
    title: '系统管理',
    icon: 'i-ri:settings-3-line',
  },
  children: [
    {
      path: 'menu',
      name: 'systemMenu',
      // redirect: '',
      component: () => import('@/views/system/menu.vue'),
      meta: {
        title: '权限菜单',
        icon: 'i-ri:menu-line',
        auth: '/system/menu/query',
      },
    },
    {
      path: 'roles',
      name: 'systemRoles',
      // redirect: '',
      component: () => import('@/views/system/roles.vue'),
      meta: {
        title: '角色管理',
        icon: 'i-ri:shield-user-line',
        auth: '/system/role/query',
      },
    },
    {
      path: 'department',
      name: 'systemDepartment',
      // redirect: '',
      component: () => import('@/views/system/department.vue'),
      meta: {
        title: '部门管理',
        icon: 'i-ri:building-line',
        // auth: '/system/partment/query',
      },
    },
    {
      path: 'dictionary',
      name: 'systemDictionary',
      // redirect: '',
      component: () => import('@/views/system/dictionary.vue'),
      meta: {
        title: '字典管理',
        icon: 'i-ri:book-line',
        // auth: '/system/dict/query',
      },
    },
    {
      path: 'users',
      name: 'systemUsers',
      component: () => import('@/views/system/users.vue'),
      meta: {
        title: '用户管理',
        icon: 'i-ri:user-line',
        // auth: '/system/account/query',
      },
    },
  ],
}

export default routes
