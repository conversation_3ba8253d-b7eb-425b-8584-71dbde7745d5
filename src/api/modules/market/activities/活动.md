# 文档

## 【后台】活动管理


### 活动列表
接口权限：/market/activity/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_activity/list


描述：活动列表
接口权限：/market/activity/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | 页码 | 0 |
| size | int32 | 否 | - | 每页大小 | 0 |
| name | string | 否 | - | 活动名称 |  |
| status | int32 | 否 | - | 活动状态 | 0 |
| startTimeBegin | int64 | 否 | - | 开始时间查询起点 | 0 |
| startTimeEnd | int64 | 否 | - | 开始时间查询终点 | 0 |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "name": "",
    "status": 0,
    "startTimeBegin": 0,
    "startTimeEnd": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ name | string | 否 | - | 营销活动名称 |  |
|   └ banner | string | 否 | - | 活动banner |  |
|   └ detailImg | string | 否 | - | 活动详情长图 |  |
|   └ detailImgHotArea | string | 否 | - | 热点区域 |  |
|   └ linkType | string | 否 | - | 跳转类型 |  |
|   └ linkParam | string | 否 | - | 跳转信息 |  |
|   └ description | string | 否 | - | 其他说明 |  |
|   └ startTime | int64 | 否 | - | 开始时间 | 0 |
|   └ endTime | int64 | 否 | - | 结束时间 | 0 |
|   └ userType | string | 否 | - | 用户类型 0-全部 1-新用户 2-指定用户 |  |
|   └ limitTimes | int32 | 否 | - | 可参与次数 | 0 |
|   └ welfareType | int64 | 否 | - | 福利类型 0-无福利 1-优惠券 2-积分 | 0 |
|   └ showType | int32 | 否 | - | 展示方式 0-公开展示 1-隐藏展示 | 0 |
|   └ status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
|   └ seq | int64 | 否 | - | 显示顺序 | 0 |
|   └ delFlag | boolean | 否 | - | 是否删除 | true |
|   └ version | string | 否 | - | 版本号 |  |
|   └ activityCoupons | array | 否 |  | 优惠券列表 |  |
|     └ couponId | int64 | 否 | - | 优惠券ID | 0 |
|     └ couponName | string | 否 | - | 优惠券名称 |  |
|     └ couponNum | int64 | 否 | - | 发放优惠券数量 | 0 |
|   └ createTime | int64 | 否 | - | No comments found. | 0 |
|   └ updateTime | int64 | 否 | - | No comments found. | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ name | string | 否 | - | 营销活动名称 |  |
|   └ banner | string | 否 | - | 活动banner |  |
|   └ detailImg | string | 否 | - | 活动详情长图 |  |
|   └ detailImgHotArea | string | 否 | - | 热点区域 |  |
|   └ linkType | string | 否 | - | 跳转类型 |  |
|   └ linkParam | string | 否 | - | 跳转信息 |  |
|   └ description | string | 否 | - | 其他说明 |  |
|   └ startTime | int64 | 否 | - | 开始时间 | 0 |
|   └ endTime | int64 | 否 | - | 结束时间 | 0 |
|   └ userType | string | 否 | - | 用户类型 0-全部 1-新用户 2-指定用户 |  |
|   └ limitTimes | int32 | 否 | - | 可参与次数 | 0 |
|   └ welfareType | int64 | 否 | - | 福利类型 0-无福利 1-优惠券 2-积分 | 0 |
|   └ showType | int32 | 否 | - | 展示方式 0-公开展示 1-隐藏展示 | 0 |
|   └ status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
|   └ seq | int64 | 否 | - | 显示顺序 | 0 |
|   └ delFlag | boolean | 否 | - | 是否删除 | true |
|   └ version | string | 否 | - | 版本号 |  |
|   └ activityCoupons | array | 否 |  | 优惠券列表 |  |
|     └ couponId | int64 | 否 | - | 优惠券ID | 0 |
|     └ couponName | string | 否 | - | 优惠券名称 |  |
|     └ couponNum | int64 | 否 | - | 发放优惠券数量 | 0 |
|   └ createTime | int64 | 否 | - | No comments found. | 0 |
|   └ updateTime | int64 | 否 | - | No comments found. | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "banner": "",
            "detailImg": "",
            "detailImgHotArea": "",
            "linkType": "",
            "linkParam": "",
            "description": "",
            "startTime": 0,
            "endTime": 0,
            "userType": "",
            "limitTimes": 0,
            "welfareType": 0,
            "showType": 0,
            "status": 0,
            "seq": 0,
            "delFlag": true,
            "version": "",
            "activityCoupons": [
                {
                    "couponId": 0,
                    "couponName": "",
                    "couponNum": 0
                }
            ],
            "createTime": 0,
            "updateTime": 0
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "banner": "",
            "detailImg": "",
            "detailImgHotArea": "",
            "linkType": "",
            "linkParam": "",
            "description": "",
            "startTime": 0,
            "endTime": 0,
            "userType": "",
            "limitTimes": 0,
            "welfareType": 0,
            "showType": 0,
            "status": 0,
            "seq": 0,
            "delFlag": true,
            "version": "",
            "activityCoupons": [
                {
                    "couponId": 0,
                    "couponName": "",
                    "couponNum": 0
                }
            ],
            "createTime": 0,
            "updateTime": 0
        }
    ]
}
```

#### 错误码

无

### 创建活动
接口权限：/market/activity/add

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_activity/add


描述：创建活动
接口权限：/market/activity/add

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| name | string | 是 | - | 活动名称 |  |
| banner | string | 否 | - | 活动横幅图 |  |
| detailImg | string | 否 | - | 活动详情图 |  |
| description | string | 否 | - | 活动描述 |  |
| startTime | int64 | 是 | - | 活动开始时间（时间戳） | 0 |
| endTime | int64 | 是 | - | 活动结束时间（时间戳） | 0 |
| limitType | int32 | 否 | - | 限制类型：0-不限, 1-限制次数 | 0 |
| limitCount | int32 | 否 | - | 限制次数（当limitType=1时有效） | 0 |
| coupons | array | 否 |  | 关联优惠券列表 |  |
|   └ couponId | int64 | 否 | - | 优惠券ID | 0 |
|   └ couponName | string | 否 | - | 优惠券名称 |  |
|   └ couponNum | int64 | 否 | - | 发放优惠券数量 | 0 |
| stores | array | 否 | - | 适用门店列表 | 0,0 |
| remark | string | 否 | - | 备注 |  |

#### 请求示例

```
{
    "name": "",
    "banner": "",
    "detailImg": "",
    "description": "",
    "startTime": 0,
    "endTime": 0,
    "limitType": 0,
    "limitCount": 0,
    "coupons": [
        {
            "couponId": 0,
            "couponName": "",
            "couponNum": 0
        }
    ],
    "stores": [
        0,
        0
    ],
    "remark": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 编辑活动
接口权限：/market/activity/save

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_activity/save


描述：编辑活动
接口权限：/market/activity/save

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 活动ID |  |
| name | string | 是 | - | 活动名称 |  |
| banner | string | 否 | - | 活动横幅图 |  |
| detailImg | string | 否 | - | 活动详情图 |  |
| description | string | 否 | - | 活动描述 |  |
| startTime | int64 | 是 | - | 活动开始时间（时间戳） | 0 |
| endTime | int64 | 是 | - | 活动结束时间（时间戳） | 0 |
| limitType | int32 | 否 | - | 限制类型：0-不限, 1-限制次数 | 0 |
| limitCount | int32 | 否 | - | 限制次数（当limitType=1时有效） | 0 |
| coupons | array | 否 |  | 关联优惠券列表 |  |
|   └ couponId | int64 | 否 | - | 优惠券ID | 0 |
|   └ couponName | string | 否 | - | 优惠券名称 |  |
|   └ couponNum | int64 | 否 | - | 发放优惠券数量 | 0 |
| stores | array | 否 | - | 适用门店列表 | 0,0 |
| remark | string | 否 | - | 备注 |  |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "banner": "",
    "detailImg": "",
    "description": "",
    "startTime": 0,
    "endTime": 0,
    "limitType": 0,
    "limitCount": 0,
    "coupons": [
        {
            "couponId": 0,
            "couponName": "",
            "couponNum": 0
        }
    ],
    "stores": [
        0,
        0
    ],
    "remark": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 活动状态变更
接口权限：/market/activity/status

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_activity/status


描述：活动状态变更
接口权限：/market/activity/status

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 活动ID |  |
| useStatus | int32 | 是 | - | 状态值：0-停用, 1-启用 | 0 |

#### 请求示例

```
{
    "id": "",
    "useStatus": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 获取活动详情
接口权限：/market/activity/get

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_activity/get


描述：获取活动详情
接口权限：/market/activity/get

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| activityId | string | 是 | - | 活动ID |  |

#### 请求示例

```
{
    "activityId": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - |  |  |
| name | string | 否 | - | 营销活动名称 |  |
| banner | string | 否 | - | 活动banner |  |
| detailImg | string | 否 | - | 活动详情长图 |  |
| detailImgHotArea | string | 否 | - | 热点区域 |  |
| linkType | string | 否 | - | 跳转类型 |  |
| linkParam | string | 否 | - | 跳转信息 |  |
| description | string | 否 | - | 其他说明 |  |
| startTime | int64 | 否 | - | 开始时间 | 0 |
| endTime | int64 | 否 | - | 结束时间 | 0 |
| userType | string | 否 | - | 用户类型 0-全部 1-新用户 2-指定用户 |  |
| limitTimes | int32 | 否 | - | 可参与次数 | 0 |
| welfareType | int64 | 否 | - | 福利类型 0-无福利 1-优惠券 2-积分 | 0 |
| showType | int32 | 否 | - | 展示方式 0-公开展示 1-隐藏展示 | 0 |
| status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
| seq | int64 | 否 | - | 显示顺序 | 0 |
| delFlag | boolean | 否 | - | 是否删除 | true |
| version | string | 否 | - | 版本号 |  |
| activityCoupons | array | 否 |  | 优惠券列表 |  |
|   └ couponId | int64 | 否 | - | 优惠券ID | 0 |
|   └ couponName | string | 否 | - | 优惠券名称 |  |
|   └ couponNum | int64 | 否 | - | 发放优惠券数量 | 0 |
| createTime | int64 | 否 | - | No comments found. | 0 |
| updateTime | int64 | 否 | - | No comments found. | 0 |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "banner": "",
    "detailImg": "",
    "detailImgHotArea": "",
    "linkType": "",
    "linkParam": "",
    "description": "",
    "startTime": 0,
    "endTime": 0,
    "userType": "",
    "limitTimes": 0,
    "welfareType": 0,
    "showType": 0,
    "status": 0,
    "seq": 0,
    "delFlag": true,
    "version": "",
    "activityCoupons": [
        {
            "couponId": 0,
            "couponName": "",
            "couponNum": 0
        }
    ],
    "createTime": 0,
    "updateTime": 0
}
```

#### 错误码

无
