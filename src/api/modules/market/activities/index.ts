/**
 * 活动管理 API
 */

import type {
  Activity,
  ActivityDetailParams,
  ActivityListParams,
  ActivityListResponse,
  ActivityStatusParams,
  CreateActivityParams,
  UpdateActivityParams,
} from '@/views/market/activities/types'

import api from '@/api'

interface response<T = any> {
  code: number
  data: T
  msg: string
  success: boolean
}

/**
 * 获取活动列表
 */
export function getActivityList(params: ActivityListParams = {}) {
  return api.post<response>(`/adm_activity/list`, params)
}

/**
 * 获取活动详情
 */
export function getActivityDetail(params: ActivityDetailParams) {
  return api.post<Activity>(`/adm_activity/get`, params)
}

/**
 * 创建活动
 */
export function createActivity(data: CreateActivityParams) {
  return api.post(`/adm_activity/add`, data)
}

/**
 * 编辑活动
 */
export function updateActivity(data: UpdateActivityParams) {
  return api.post(`/adm_activity/save`, data)
}

/**
 * 删除活动
 */
export function deleteActivity(activityId: string) {
  // return http.request({
  //   url: `${API_BASE}/delete`,
  //   method: 'post',
  //   data: { activityId },
  // })
  return api.post(`/adm_activity/delete`, { activityId })
}

/**
 * 修改活动状态
 */
export function updateActivityStatus(data: ActivityStatusParams) {
  // return http.request({
  //   url: `${API_BASE}/status`,
  //   method: 'post',
  //   data,
  // })
  return api.post(`/adm_activity/status`, data)
}

/**
 * 批量删除活动
 */
export function batchDeleteActivities(activityIds: string[]) {
  // return http.request({
  //   url: `${API_BASE}/batch/delete`,
  //   method: 'post',
  //   data: { activityIds },
  // })
  return api.post(`/adm_activity/batch/delete`, { activityIds })
}

/**
 * 获取活动统计信息
 */
export function getActivityStats() {
  // return http.request({
  //   url: `${API_BASE}/stats`,
  //   method: 'get',
  // })
  return api.post(`/adm_activity/stats`)
}

/**
 * 导出活动列表
 */
export function exportActivityList(params: ActivityListParams = {}) {
  // return http.request({
  //   url: `${API_BASE}/export`,
  //   method: 'get',
  //   params,
  //   responseType: 'blob',
  // })
  return api.post(`/adm_activity/export`, params, {
    responseType: 'blob',
  })
}

// 模拟数据（开发阶段使用）
export const mockActivityList: Activity[] = [
  {
    id: '1',
    name: '新用户专享优惠',
    banner: 'https://example.com/banner1.jpg',
    detailImg: 'https://example.com/detail1.jpg',
    description: '新用户注册即可获得专享优惠券',
    startTime: Date.now(),
    endTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
    userType: '1',
    limitTimes: 1,
    welfareType: 1,
    showType: 0,
    status: 1,
    seq: 1,
    delFlag: false,
    version: '1.0',
    activityCoupons: [
      {
        couponId: 1,
        couponName: '新用户专享券',
        couponNum: 100,
      },
    ],
    createTime: Date.now() - 24 * 60 * 60 * 1000,
    updateTime: Date.now(),
  },
  {
    id: '2',
    name: '周末观影特惠',
    banner: 'https://example.com/banner2.jpg',
    detailImg: 'https://example.com/detail2.jpg',
    description: '周末观影享受特别优惠',
    startTime: Date.now() + 24 * 60 * 60 * 1000,
    endTime: Date.now() + 14 * 24 * 60 * 60 * 1000,
    userType: '0',
    limitTimes: 2,
    welfareType: 1,
    showType: 0,
    status: 1,
    seq: 2,
    delFlag: false,
    version: '1.0',
    activityCoupons: [
      {
        couponId: 2,
        couponName: '周末特惠券',
        couponNum: 200,
      },
    ],
    createTime: Date.now() - 12 * 60 * 60 * 1000,
    updateTime: Date.now(),
  },
  {
    id: '3',
    name: '会员积分兑换',
    banner: 'https://example.com/banner3.jpg',
    detailImg: 'https://example.com/detail3.jpg',
    description: '会员积分兑换精美礼品',
    startTime: Date.now() - 7 * 24 * 60 * 60 * 1000,
    endTime: Date.now() + 30 * 24 * 60 * 60 * 1000,
    userType: '2',
    limitTimes: 0,
    welfareType: 2,
    showType: 0,
    status: 0,
    seq: 3,
    delFlag: false,
    version: '1.0',
    activityCoupons: [],
    createTime: Date.now() - 8 * 24 * 60 * 60 * 1000,
    updateTime: Date.now() - 24 * 60 * 60 * 1000,
  },
]

// 模拟 API 响应
export function mockGetActivityList(params: ActivityListParams = {}): Promise<ActivityListResponse> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredList = [...mockActivityList]

      // 按名称筛选
      if (params.name) {
        filteredList = filteredList.filter(item =>
          item.name.toLowerCase().includes(params.name!.toLowerCase()),
        )
      }

      // 按状态筛选
      if (params.status !== undefined) {
        filteredList = filteredList.filter(item => item.status === params.status)
      }

      // 按时间筛选
      if (params.startTimeBegin) {
        filteredList = filteredList.filter(item => item.startTime >= params.startTimeBegin!)
      }
      if (params.startTimeEnd) {
        filteredList = filteredList.filter(item => item.startTime <= params.startTimeEnd!)
      }

      // 分页
      const page = params.page || 1
      const size = params.size || 20
      const start = (page - 1) * size
      const end = start + size
      const content = filteredList.slice(start, end)

      resolve({
        total: filteredList.length,
        content,
        result: content,
      })
    }, 500)
  })
}

// 模拟获取活动详情
export function mockGetActivityDetail(activityId: string): Promise<Activity> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const activity = mockActivityList.find(item => item.id === activityId)
      if (activity) {
        resolve(activity)
      }
      else {
        reject(new Error('活动不存在'))
      }
    }, 300)
  })
}
