# 文档

## 【后台】团购管理


### 团购列表
接口权限：/market/groupBuy/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_groupBuy/list


描述：团购列表
接口权限：/market/groupBuy/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| name | string | 否 | - | 团购名称 |  |
| startTime | string | 否 | - | 售卖日期 开始时间 |  |
| endTime | string | 否 | - | 售卖日期 结束时间 |  |
| useStatus | int32 | 否 | - | 使用状态，0关闭，1开启 | 0 |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "name": "",
    "startTime": "",
    "endTime": "",
    "useStatus": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | MongoDB自动生成的ID |  |
|   └ name | string | 否 | - | 团购名称 |  |
|   └ banner | string | 否 | - | 封面图 |  |
|   └ detailImg | string | 否 | - | 长图 |  |
|   └ originPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|   └ sellPrice | int64 | 否 | - | 售价（单位：分） | 0 |
|   └ stockCount | int32 | 否 | - | 库存数量 | 0 |
|   └ useScope | int32 | 否 | - | 可使用门店：自定义0 跟随物品1 | 0 |
|   └ startTime | int64 | 否 | - | 销售开始时间 | 0 |
|   └ createTime | int64 | 否 | - | No comments found. | 0 |
|   └ updateTime | int64 | 否 | - | No comments found. | 0 |
|   └ endTime | int64 | 否 | - | 销售结束时间 | 0 |
|   └ limitType | int32 | 否 | - | 限购：不限购0 每个用户限购1 | 0 |
|   └ limitCount | int32 | 否 | - | 总量限制可优惠票数 张 | 0 |
|   └ refundModel | int32 | 否 | - | 退款条件: 不可退0 过期退1 | 0 |
|   └ remark | string | 否 | - | 购买须知 |  |
|   └ description | string | 否 | - | 说明 |  |
|   └ goods | array | 否 |  | 售卖物品 |  |
|     └ goodsType | int32 | 否 | - | 物品类型 0:优惠券 | 0 |
|     └ relationId | int64 | 否 | - | 关联ID（物品类型：优惠券：coupon.id） | 0 |
|     └ goodsNum | int32 | 否 | - | 数量 | 0 |
|     └ amount | int32 | 否 | - | 金额 | 0 |
|   └ stores | array | 否 |  | 门店 |  |
|     └ provinceCode | string | 否 | - | 省份编码 |  |
|     └ cityCode | string | 否 | - | 城市编码 |  |
|     └ storeName | string | 否 | - | 门店名称 |  |
|   └ status | int32 | 否 | - | No comments found. | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | MongoDB自动生成的ID |  |
|   └ name | string | 否 | - | 团购名称 |  |
|   └ banner | string | 否 | - | 封面图 |  |
|   └ detailImg | string | 否 | - | 长图 |  |
|   └ originPrice | int64 | 否 | - | 原价（单位：分） | 0 |
|   └ sellPrice | int64 | 否 | - | 售价（单位：分） | 0 |
|   └ stockCount | int32 | 否 | - | 库存数量 | 0 |
|   └ useScope | int32 | 否 | - | 可使用门店：自定义0 跟随物品1 | 0 |
|   └ startTime | int64 | 否 | - | 销售开始时间 | 0 |
|   └ createTime | int64 | 否 | - | No comments found. | 0 |
|   └ updateTime | int64 | 否 | - | No comments found. | 0 |
|   └ endTime | int64 | 否 | - | 销售结束时间 | 0 |
|   └ limitType | int32 | 否 | - | 限购：不限购0 每个用户限购1 | 0 |
|   └ limitCount | int32 | 否 | - | 总量限制可优惠票数 张 | 0 |
|   └ refundModel | int32 | 否 | - | 退款条件: 不可退0 过期退1 | 0 |
|   └ remark | string | 否 | - | 购买须知 |  |
|   └ description | string | 否 | - | 说明 |  |
|   └ goods | array | 否 |  | 售卖物品 |  |
|     └ goodsType | int32 | 否 | - | 物品类型 0:优惠券 | 0 |
|     └ relationId | int64 | 否 | - | 关联ID（物品类型：优惠券：coupon.id） | 0 |
|     └ goodsNum | int32 | 否 | - | 数量 | 0 |
|     └ amount | int32 | 否 | - | 金额 | 0 |
|   └ stores | array | 否 |  | 门店 |  |
|     └ provinceCode | string | 否 | - | 省份编码 |  |
|     └ cityCode | string | 否 | - | 城市编码 |  |
|     └ storeName | string | 否 | - | 门店名称 |  |
|   └ status | int32 | 否 | - | No comments found. | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "banner": "",
            "detailImg": "",
            "originPrice": 0,
            "sellPrice": 0,
            "stockCount": 0,
            "useScope": 0,
            "startTime": 0,
            "createTime": 0,
            "updateTime": 0,
            "endTime": 0,
            "limitType": 0,
            "limitCount": 0,
            "refundModel": 0,
            "remark": "",
            "description": "",
            "goods": [
                {
                    "goodsType": 0,
                    "relationId": 0,
                    "goodsNum": 0,
                    "amount": 0
                }
            ],
            "stores": [
                {
                    "provinceCode": "",
                    "cityCode": "",
                    "storeName": ""
                }
            ],
            "status": 0
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "banner": "",
            "detailImg": "",
            "originPrice": 0,
            "sellPrice": 0,
            "stockCount": 0,
            "useScope": 0,
            "startTime": 0,
            "createTime": 0,
            "updateTime": 0,
            "endTime": 0,
            "limitType": 0,
            "limitCount": 0,
            "refundModel": 0,
            "remark": "",
            "description": "",
            "goods": [
                {
                    "goodsType": 0,
                    "relationId": 0,
                    "goodsNum": 0,
                    "amount": 0
                }
            ],
            "stores": [
                {
                    "provinceCode": "",
                    "cityCode": "",
                    "storeName": ""
                }
            ],
            "status": 0
        }
    ]
}
```

#### 错误码

无

### 创建团购
接口权限：/market/groupBuy/add

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_groupBuy/add


描述：创建团购
接口权限：/market/groupBuy/add

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| name | string | 否 | - | 团购名称 |  |
| banner | string | 否 | - | 封面图 |  |
| detailImg | string | 否 | - | 长图 |  |
| originPrice | int64 | 否 | - | 原价（单位：分） | 0 |
| sellPrice | int64 | 否 | - | 售价（单位：分） | 0 |
| stockCount | int32 | 否 | - | 库存数量 | 0 |
| useScope | int32 | 否 | - | 可使用门店：自定义0 跟随物品1 | 0 |
| startTime | int64 | 否 | - | 销售开始时间 | 0 |
| endTime | int64 | 否 | - | 销售结束时间 | 0 |
| limitType | int32 | 否 | - | 限购：不限购0 每个用户限购1 | 0 |
| limitCount | int32 | 否 | - | 总量限制可优惠票数 张 | 0 |
| refundModel | int32 | 否 | - | 退款条件: 不可退0 过期退1 | 0 |
| remark | string | 否 | - | 购买须知 |  |
| description | string | 否 | - | 说明 |  |
| goods | array | 否 |  | 售卖物品 |  |
|   └ goodsType | int32 | 否 | - | 物品类型 0:优惠券 | 0 |
|   └ relationId | int64 | 否 | - | 关联ID（物品类型：优惠券：coupon.id） | 0 |
|   └ goodsNum | int32 | 否 | - | 数量 | 0 |
|   └ amount | int32 | 否 | - | 金额 | 0 |
| stores | array | 否 |  | 门店 |  |
|   └ provinceCode | string | 否 | - | 省份编码 |  |
|   └ cityCode | string | 否 | - | 城市编码 |  |
|   └ storeName | string | 否 | - | 门店名称 |  |

#### 请求示例

```
{
    "name": "",
    "banner": "",
    "detailImg": "",
    "originPrice": 0,
    "sellPrice": 0,
    "stockCount": 0,
    "useScope": 0,
    "startTime": 0,
    "endTime": 0,
    "limitType": 0,
    "limitCount": 0,
    "refundModel": 0,
    "remark": "",
    "description": "",
    "goods": [
        {
            "goodsType": 0,
            "relationId": 0,
            "goodsNum": 0,
            "amount": 0
        }
    ],
    "stores": [
        {
            "provinceCode": "",
            "cityCode": "",
            "storeName": ""
        }
    ]
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 编辑团购
接口权限：/market/groupBuy/save

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_groupBuy/save


描述：编辑团购
接口权限：/market/groupBuy/save

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 团购ID |  |
| name | string | 否 | - | 团购名称 |  |
| banner | string | 否 | - | 封面图 |  |
| detailImg | string | 否 | - | 长图 |  |
| originPrice | int64 | 否 | - | 原价（单位：分） | 0 |
| sellPrice | int64 | 否 | - | 售价（单位：分） | 0 |
| stockCount | int32 | 否 | - | 库存数量 | 0 |
| useScope | int32 | 否 | - | 可使用门店：自定义0 跟随物品1 | 0 |
| startTime | int64 | 否 | - | 销售开始时间 | 0 |
| endTime | int64 | 否 | - | 销售结束时间 | 0 |
| limitType | int32 | 否 | - | 限购：不限购0 每个用户限购1 | 0 |
| limitCount | int32 | 否 | - | 总量限制可优惠票数 张 | 0 |
| refundModel | int32 | 否 | - | 退款条件: 不可退0 过期退1 | 0 |
| remark | string | 否 | - | 购买须知 |  |
| description | string | 否 | - | 说明 |  |
| goods | array | 否 |  | 售卖物品 |  |
|   └ goodsType | int32 | 否 | - | 物品类型 0:优惠券 | 0 |
|   └ relationId | int64 | 否 | - | 关联ID（物品类型：优惠券：coupon.id） | 0 |
|   └ goodsNum | int32 | 否 | - | 数量 | 0 |
|   └ amount | int32 | 否 | - | 金额 | 0 |
| stores | array | 否 |  | 门店 |  |
|   └ provinceCode | string | 否 | - | 省份编码 |  |
|   └ cityCode | string | 否 | - | 城市编码 |  |
|   └ storeName | string | 否 | - | 门店名称 |  |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "banner": "",
    "detailImg": "",
    "originPrice": 0,
    "sellPrice": 0,
    "stockCount": 0,
    "useScope": 0,
    "startTime": 0,
    "endTime": 0,
    "limitType": 0,
    "limitCount": 0,
    "refundModel": 0,
    "remark": "",
    "description": "",
    "goods": [
        {
            "goodsType": 0,
            "relationId": 0,
            "goodsNum": 0,
            "amount": 0
        }
    ],
    "stores": [
        {
            "provinceCode": "",
            "cityCode": "",
            "storeName": ""
        }
    ]
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 团购 状态变更 显示或者隐藏
接口权限：/market/groupBuy/status

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_groupBuy/status


描述：团购 状态变更 显示或者隐藏
接口权限：/market/groupBuy/status

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 是 | - | 团购 ID不能为空 |  |
| useStatus | int32 | 是 | - | 使用状态，0关闭，1开启 不能为空 | 0 |

#### 请求示例

```
{
    "id": "",
    "useStatus": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 获取团购详情
接口权限：/market/groupBuy/get

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_groupBuy/groupBuy


描述：获取团购详情
接口权限：/market/groupBuy/get

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | ID |  |

#### 请求示例

```
{
    "id": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | MongoDB自动生成的ID |  |
| name | string | 否 | - | 团购名称 |  |
| banner | string | 否 | - | 封面图 |  |
| detailImg | string | 否 | - | 长图 |  |
| originPrice | int64 | 否 | - | 原价（单位：分） | 0 |
| sellPrice | int64 | 否 | - | 售价（单位：分） | 0 |
| stockCount | int32 | 否 | - | 库存数量 | 0 |
| useScope | int32 | 否 | - | 可使用门店：自定义0 跟随物品1 | 0 |
| startTime | int64 | 否 | - | 销售开始时间 | 0 |
| createTime | int64 | 否 | - | No comments found. | 0 |
| updateTime | int64 | 否 | - | No comments found. | 0 |
| endTime | int64 | 否 | - | 销售结束时间 | 0 |
| limitType | int32 | 否 | - | 限购：不限购0 每个用户限购1 | 0 |
| limitCount | int32 | 否 | - | 总量限制可优惠票数 张 | 0 |
| refundModel | int32 | 否 | - | 退款条件: 不可退0 过期退1 | 0 |
| remark | string | 否 | - | 购买须知 |  |
| description | string | 否 | - | 说明 |  |
| goods | array | 否 |  | 售卖物品 |  |
|   └ goodsType | int32 | 否 | - | 物品类型 0:优惠券 | 0 |
|   └ relationId | int64 | 否 | - | 关联ID（物品类型：优惠券：coupon.id） | 0 |
|   └ goodsNum | int32 | 否 | - | 数量 | 0 |
|   └ amount | int32 | 否 | - | 金额 | 0 |
| stores | array | 否 |  | 门店 |  |
|   └ provinceCode | string | 否 | - | 省份编码 |  |
|   └ cityCode | string | 否 | - | 城市编码 |  |
|   └ storeName | string | 否 | - | 门店名称 |  |
| status | int32 | 否 | - | No comments found. | 0 |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "banner": "",
    "detailImg": "",
    "originPrice": 0,
    "sellPrice": 0,
    "stockCount": 0,
    "useScope": 0,
    "startTime": 0,
    "createTime": 0,
    "updateTime": 0,
    "endTime": 0,
    "limitType": 0,
    "limitCount": 0,
    "refundModel": 0,
    "remark": "",
    "description": "",
    "goods": [
        {
            "goodsType": 0,
            "relationId": 0,
            "goodsNum": 0,
            "amount": 0
        }
    ],
    "stores": [
        {
            "provinceCode": "",
            "cityCode": "",
            "storeName": ""
        }
    ],
    "status": 0
}
```

#### 错误码

无
