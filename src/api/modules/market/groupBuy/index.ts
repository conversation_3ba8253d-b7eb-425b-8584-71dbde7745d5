/**
 * 团购管理API服务
 * 基于接口文档实现团购相关的API调用
 */
import type {
  CreateGroupBuyParams,
  GetGroupBuyParams,
  GroupBuy,
  GroupBuyListParams,
  GroupBuyListResponse,
  GroupBuyStatusParams,
  UpdateGroupBuyParams,
} from '@/views/market/groupBuy/types'
import api from '@/api'

interface ResponseBase<T = any> {
  code: number
  data: T
  msg: string
  success: boolean
}

// ==================== 团购管理接口 ====================

/**
 * 获取团购列表
 * @param params 查询参数
 * @returns 团购列表响应
 */
export function getGroupBuyList(params: GroupBuyListParams) {
  return api.post<ResponseBase<GroupBuyListResponse>>('/adm_groupBuy/list', params)
}

/**
 * 创建团购
 * @param params 团购参数
 * @returns 操作结果
 */
export function createGroupBuy(params: CreateGroupBuyParams) {
  return api.post<ResponseBase<void>>('/adm_groupBuy/add', params)
}

/**
 * 编辑团购
 * @param params 团购参数
 * @returns 操作结果
 */
export function updateGroupBuy(params: UpdateGroupBuyParams) {
  return api.post<ResponseBase<void>>('/adm_groupBuy/save', params)
}

/**
 * 团购状态变更
 * @param params 状态变更参数
 * @returns 操作结果
 */
export function updateGroupBuyStatus(params: GroupBuyStatusParams) {
  return api.post<ResponseBase<void>>('/adm_groupBuy/status', params)
}

/**
 * 获取团购详情
 * @param params 查询参数
 * @returns 团购详情
 */
export function getGroupBuyDetail(params: GetGroupBuyParams) {
  return api.post<ResponseBase<GroupBuy>>('/adm_groupBuy/groupBuy', params)
}
