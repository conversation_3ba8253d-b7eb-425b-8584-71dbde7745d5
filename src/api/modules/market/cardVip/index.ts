import type {
  VipCardActivityDetailParams,
  VipCardActivityEditParams,
  VipCardActivityListParams,
  VipCardActivityStatusParams,
  VipCardPolicyDetailParams,
  VipCardPolicyEditParams,
  VipCardPolicyStatusParams,
  VipCardUserDetailParams,
  VipCardUserEditParams,
  VipCardUserListParams,
  VipCardUserStatusParams,
} from './types'
import api from '@/api'

// 卡赠礼管理 API
/**
 * 卡赠礼列表(分页)
 * @param data 查询参数
 */
export function getVipCardActivityList(data: VipCardActivityListParams) {
  return api({
    url: '/market/vipCardActivity/search',
    method: 'post',
    data,
  })
}

/**
 * 卡赠礼列表
 */
export function getVipCardActivityAll() {
  return api({
    url: '/market/vipCardActivity/list',
    method: 'post',
  })
}

/**
 * 卡赠礼详情
 * @param data 查询参数
 */
export function getVipCardActivityDetail(data: VipCardActivityDetailParams) {
  return api({
    url: '/market/vipCardActivity/detail',
    method: 'post',
    data,
  })
}

/**
 * 修改卡赠礼
 * @param data 卡赠礼信息
 */
export function editVipCardActivity(data: VipCardActivityEditParams) {
  return api({
    url: '/market/vipCardActivity/edit',
    method: 'post',
    data,
  })
}

/**
 * 修改卡赠礼状态
 * @param data 状态信息
 */
export function editVipCardActivityStatus(data: VipCardActivityStatusParams) {
  return api({
    url: '/market/vipCardActivity/editStatus',
    method: 'post',
    data,
  })
}

// 卡政策管理 API
/**
 * 卡政策列表(分页)
 * @param data 查询参数
 */
export function getVipCardPolicyList(data: {
  page: number
  size: number
  name?: string
  status?: number | null
  // dateRange?: UnwrapRef<VipCardPolicyListParams["dateRange"]>;
  id?: string
  startTime?: string
  endTime?: string
}) {
  return api({
    url: '/data/vipCardInfo/search',
    method: 'post',
    data,
  })
}

/**
 * 查看卡政策详情
 * @param data 查询参数
 */
export function getVipCardPolicyDetail(data: VipCardPolicyDetailParams) {
  return api({
    url: '/data/vipCardInfo/detail',
    method: 'post',
    data,
  })
}

/**
 * 修改卡政策
 * @param data 卡政策信息
 */
export function editVipCardPolicy(data: VipCardPolicyEditParams) {
  return api({
    url: '/market/vipCardInfo/edit',
    method: 'post',
    data,
  })
}

/**
 * 修改卡政策状态
 * @param data 状态信息
 */
export function editVipCardPolicyStatus(data: VipCardPolicyStatusParams) {
  return api({
    url: '/market/vipCardInfo/editStatus',
    method: 'post',
    data,
  })
}

// 会员卡管理 API
/**
 * 会员卡列表(分页)
 * @param data 查询参数
 */
export function getVipCardUserList(data: VipCardUserListParams) {
  return api({
    url: '/market/vipCardUser/search',
    method: 'post',
    data,
  })
}

/**
 * 会员卡列表
 */
export function getVipCardUserAll() {
  return api({
    url: '/market/vipCardUser/list',
    method: 'post',
  })
}

/**
 * 查看会员卡详情
 * @param data 查询参数
 */
export function getVipCardUserDetail(data: VipCardUserDetailParams) {
  return api({
    url: '/market/vipCardUser/detail',
    method: 'post',
    data,
  })
}

/**
 * 修改会员卡
 * @param data 会员卡信息
 */
export function editVipCardUser(data: VipCardUserEditParams) {
  return api({
    url: '/market/vipCardUser/edit',
    method: 'post',
    data,
  })
}

/**
 * 修改会员卡状态
 * @param data 状态信息
 */
export function editVipCardUserStatus(data: VipCardUserStatusParams) {
  return api({
    url: '/market/vipCardUser/editStatus',
    method: 'post',
    data,
  })
}
