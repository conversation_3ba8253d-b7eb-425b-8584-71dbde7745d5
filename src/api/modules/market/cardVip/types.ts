// 卡赠礼管理类型定义
export interface VipCardActivityListParams {
  page?: number
  size?: number
  name?: string
  internalVipCardId?: string
  status?: number
  startTime?: number
  endTime?: number
}

export interface VipCardActivityDetailParams {
  cardId?: string
}

export interface VipCardActivityEditParams {
  id?: string
  internalVipCardId?: string
  internalVipCardName?: string
  name?: string
  type?: number
  startTime?: number
  endTime?: number
  status?: number
  createBy?: string
  updateTime?: string
  gifts?: VipCardActivityGift[]
}

export interface VipCardActivityGift {
  internalVipCardId?: string
  internalVipCardActivityId?: string
  internalVipCardActivityMoneyId?: string
  type?: number
  giftType?: number
  amount?: number
  coupons?: VipCardActivityCoupon[]
}

export interface VipCardActivityCoupon {
  couponId?: number
  couponName?: string
  couponNum?: number
}

export interface VipCardActivityStatusParams {
  cardId?: string
  status?: number
}

// 卡政策管理类型定义
export interface VipCardPolicyListParams {
  page?: number
  size?: number
  name?: string
  id?: string
  status?: number
  startTime?: number
  endTime?: number
}

export interface VipCardPolicyDetailParams {
  cardId?: string
}

export interface VipCardPolicyEditParams {
  id?: string
  name?: string
  description?: string
  cardType?: number
  status?: number
  sort?: number
  displayDiscountPrice?: number
  displaySort?: number
  effectiveDays?: number
  vipFee?: number
  cardCoverText?: string
  discountText1?: string
  discountText2?: string
  discountText3?: string
  cinemas?: number[]
  monies?: VipCardPolicyMoney[]
  createBy?: string
  updateTime?: number
  rules?: VipCardPolicyRule[]
}

export interface VipCardPolicyMoney {
  id?: string
  internalVipCardId?: number
  amount?: number
}

export interface VipCardPolicyRule {
  id?: number
  internalVipCardId?: number
  canUseCinemaType?: number
  ticketDiscountType?: number
  generalSetting?: number
  excludeFilm?: number
  includeFilm?: number
  generalEachSchedule?: number
  generalEachScheduleNum?: number
  generalEachFilm?: number
  generalEachFilmNum?: number
  generalEachDay?: number
  generalEachDayNum?: number
  includeFilmEachSchedule?: number
  includeFilmEachScheduleNum?: number
  includeFilmEachFilm?: number
  includeFilmEachFilmNum?: number
  includeFilmEachDay?: number
  includeFilmEachDayNum?: number
  servicePriceReduction?: number
  servicePriceAmount?: number
  goodsDiscountSetting?: number
  goodsDiscountType?: number
  hallType?: VipCardPolicyRuleHallType
  cinemas?: VipCardPolicyRuleCinema[]
  tickets?: VipCardPolicyRuleTicket[]
  hallFilms?: VipCardPolicyRuleHallFilm[]
  films?: VipCardPolicyRuleFilm[]
  goodsTypes?: VipCardPolicyRuleGoodsType[]
}

export interface VipCardPolicyRuleHallType {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  hallTypeDiscount?: number
  filmTypeDiscount?: number
  hallTypeSelect?: VipCardPolicyRuleSelect
  filmTypeSelect?: VipCardPolicyRuleSelect
}

export interface VipCardPolicyRuleSelect {
  isSelectAll?: boolean
  types?: VipCardPolicyRuleSelectType[]
}

export interface VipCardPolicyRuleSelectType {
  typeKey?: string
  typeName?: string
}

export interface VipCardPolicyRuleCinema {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  cinemaId?: number
  includeCinema?: number
  cinemaName?: string
}

export interface VipCardPolicyRuleTicket {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  discountType?: number
  discountValue?: number
  discountTimeInterval?: VipCardPolicyRuleTicketTimeInterval
}

export interface VipCardPolicyRuleTicketTimeInterval {
  days?: number[]
  timeIntervals?: VipCardPolicyRuleTimeInterval[]
}

export interface VipCardPolicyRuleTimeInterval {
  startTime?: string
  endTime?: string
}

export interface VipCardPolicyRuleHallFilm {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  hallType?: string
  filmType?: string
  discountType?: number
  discountValue?: number
  groupIndex?: number
  discountTimeInterval?: VipCardPolicyRuleHallFilmTimeInterval[]
}

export interface VipCardPolicyRuleHallFilmTimeInterval {
  days?: number[]
  timeIntervals?: VipCardPolicyRuleTimeInterval[]
}

export interface VipCardPolicyRuleFilm {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  filmId?: number
  includeFilm?: number
  filmName?: string
}

export interface VipCardPolicyRuleGoodsType {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  goodsTypeId?: number
  discountType?: number
  discountValue?: number
  goodsTypeName?: string
}

export interface VipCardPolicyStatusParams {
  cardId?: string
  status?: number
}

// 会员卡管理类型定义
export interface VipCardUserListParams {
  page?: number
  size?: number
  cardNumber?: string
  internalVipCardId?: string
  cardType?: string
  cinemaName?: string
  phoneNumber?: string
  status?: number
  startTime?: number
  endTime?: number
}

export interface VipCardUserDetailParams {
  cardId?: string
}

export interface VipCardUserEditParams {
  id?: string
  internalVipCardId?: string
  batchId?: string
  cardNumber?: string
  name?: string
  cardType?: string
  cardTypeName?: string
  cinemaId?: number
  cinemaName?: string
  userId?: number
  nickName?: string
  phoneNumber?: string
  cardAmount?: number
  vipFee?: number
  startTime?: number
  endTime?: number
  effective?: boolean
  displayDiscountPrice?: number
  canUserCurrentCinema?: boolean
  sort?: number
  status?: number
  cardStatus?: number
  acquisitionMethod?: number
  cardCreationDate?: number
}

export interface VipCardUserStatusParams {
  cardId?: string
  status?: number
}