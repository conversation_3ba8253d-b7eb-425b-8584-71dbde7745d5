# 文档

## 【后台】卡赠礼管理


### 卡赠礼列表(分页)
接口权限：/market/vipCardActivity/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardActivity/search


描述：卡赠礼列表(分页)
接口权限：/market/vipCardActivity/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | - | No comments found. | 0 |
| name | string | 否 | - | 卡赠礼名称 |  |
| internalVipCardId | string | 否 | - | 影城卡ID |  |
| status | int32 | 否 | - | 状态 | 0 |
| startTime | int64 | 否 | - | 创建时间-开始时间 | 0 |
| endTime | int64 | 否 | - | 创建时间-结束时间 | 0 |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "name": "",
    "internalVipCardId": "",
    "status": 0,
    "startTime": 0,
    "endTime": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ internalVipCardName | string | 否 | - | 内部会员卡名称 |  |
|   └ name | string | 否 | - | 活动名称 |  |
|   └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|   └ startTime | int64 | 否 | - | 活动开始日期 | 0 |
|   └ endTime | int64 | 否 | - | 活动结束日期 | 0 |
|   └ status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
|   └ createBy | string | 否 | - | 创建者 |  |
|   └ updateTime | string | 否 | - | 更新时间 | yyyy-MM-dd HH:mm:ss |
|   └ gifts | array | 否 |  | 活动方案 |  |
|     └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|     └ internalVipCardActivityId | string | 否 | - | 内部会员卡充值活动id |  |
|     └ internalVipCardActivityMoneyId | string | 否 | - | 内部会员卡开卡充值金额档位id |  |
|     └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|     └ giftType | int32 | 否 | - | 赠礼类型 0:优惠券 1:金额 2:其他 | 0 |
|     └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|     └ coupons | array | 否 |  | 优惠券 |  |
|       └ couponId | int64 | 否 | - | 优惠券id | 0 |
|       └ couponName | string | 否 | - | 优惠券名称 |  |
|       └ couponNum | int32 | 否 | - | 优惠券数量 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ internalVipCardName | string | 否 | - | 内部会员卡名称 |  |
|   └ name | string | 否 | - | 活动名称 |  |
|   └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|   └ startTime | int64 | 否 | - | 活动开始日期 | 0 |
|   └ endTime | int64 | 否 | - | 活动结束日期 | 0 |
|   └ status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
|   └ createBy | string | 否 | - | 创建者 |  |
|   └ updateTime | string | 否 | - | 更新时间 | yyyy-MM-dd HH:mm:ss |
|   └ gifts | array | 否 |  | 活动方案 |  |
|     └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|     └ internalVipCardActivityId | string | 否 | - | 内部会员卡充值活动id |  |
|     └ internalVipCardActivityMoneyId | string | 否 | - | 内部会员卡开卡充值金额档位id |  |
|     └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|     └ giftType | int32 | 否 | - | 赠礼类型 0:优惠券 1:金额 2:其他 | 0 |
|     └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|     └ coupons | array | 否 |  | 优惠券 |  |
|       └ couponId | int64 | 否 | - | 优惠券id | 0 |
|       └ couponName | string | 否 | - | 优惠券名称 |  |
|       └ couponNum | int32 | 否 | - | 优惠券数量 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "internalVipCardId": "",
            "internalVipCardName": "",
            "name": "",
            "type": 0,
            "startTime": 0,
            "endTime": 0,
            "status": 0,
            "createBy": "",
            "updateTime": "yyyy-MM-dd HH:mm:ss",
            "gifts": [
                {
                    "internalVipCardId": "",
                    "internalVipCardActivityId": "",
                    "internalVipCardActivityMoneyId": "",
                    "type": 0,
                    "giftType": 0,
                    "amount": 0,
                    "coupons": [
                        {
                            "couponId": 0,
                            "couponName": "",
                            "couponNum": 0
                        }
                    ]
                }
            ]
        }
    ],
    "result": [
        {
            "id": "",
            "internalVipCardId": "",
            "internalVipCardName": "",
            "name": "",
            "type": 0,
            "startTime": 0,
            "endTime": 0,
            "status": 0,
            "createBy": "",
            "updateTime": "yyyy-MM-dd HH:mm:ss",
            "gifts": [
                {
                    "internalVipCardId": "",
                    "internalVipCardActivityId": "",
                    "internalVipCardActivityMoneyId": "",
                    "type": 0,
                    "giftType": 0,
                    "amount": 0,
                    "coupons": [
                        {
                            "couponId": 0,
                            "couponName": "",
                            "couponNum": 0
                        }
                    ]
                }
            ]
        }
    ]
}
```

#### 错误码

无

### 卡赠礼列表
接口权限：/market/vipCardActivity/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardActivity/list


描述：卡赠礼列表
接口权限：/market/vipCardActivity/query

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | id |  |
| internalVipCardId | string | 否 | - | 内部会员卡id |  |
| internalVipCardName | string | 否 | - | 内部会员卡名称 |  |
| name | string | 否 | - | 活动名称 |  |
| type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
| startTime | int64 | 否 | - | 活动开始日期 | 0 |
| endTime | int64 | 否 | - | 活动结束日期 | 0 |
| status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
| createBy | string | 否 | - | 创建者 |  |
| updateTime | string | 否 | - | 更新时间 | yyyy-MM-dd HH:mm:ss |
| gifts | array | 否 |  | 活动方案 |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ internalVipCardActivityId | string | 否 | - | 内部会员卡充值活动id |  |
|   └ internalVipCardActivityMoneyId | string | 否 | - | 内部会员卡开卡充值金额档位id |  |
|   └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|   └ giftType | int32 | 否 | - | 赠礼类型 0:优惠券 1:金额 2:其他 | 0 |
|   └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|   └ coupons | array | 否 |  | 优惠券 |  |
|     └ couponId | int64 | 否 | - | 优惠券id | 0 |
|     └ couponName | string | 否 | - | 优惠券名称 |  |
|     └ couponNum | int32 | 否 | - | 优惠券数量 | 0 |

#### 响应示例

```
{
    "id": "",
    "internalVipCardId": "",
    "internalVipCardName": "",
    "name": "",
    "type": 0,
    "startTime": 0,
    "endTime": 0,
    "status": 0,
    "createBy": "",
    "updateTime": "yyyy-MM-dd HH:mm:ss",
    "gifts": [
        {
            "internalVipCardId": "",
            "internalVipCardActivityId": "",
            "internalVipCardActivityMoneyId": "",
            "type": 0,
            "giftType": 0,
            "amount": 0,
            "coupons": [
                {
                    "couponId": 0,
                    "couponName": "",
                    "couponNum": 0
                }
            ]
        }
    ]
}
```

#### 错误码

无

### 卡赠礼详情
接口权限：/market/vipCardActivity/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardActivity/detail


描述：卡赠礼详情
接口权限：/market/vipCardActivity/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cardId | string | 否 | - | 卡id |  |

#### 请求示例

```
{
    "cardId": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | id |  |
| internalVipCardId | string | 否 | - | 内部会员卡id |  |
| internalVipCardName | string | 否 | - | 内部会员卡名称 |  |
| name | string | 否 | - | 活动名称 |  |
| type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
| startTime | int64 | 否 | - | 活动开始日期 | 0 |
| endTime | int64 | 否 | - | 活动结束日期 | 0 |
| status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
| createBy | string | 否 | - | 创建者 |  |
| updateTime | string | 否 | - | 更新时间 | yyyy-MM-dd HH:mm:ss |
| gifts | array | 否 |  | 活动方案 |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ internalVipCardActivityId | string | 否 | - | 内部会员卡充值活动id |  |
|   └ internalVipCardActivityMoneyId | string | 否 | - | 内部会员卡开卡充值金额档位id |  |
|   └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|   └ giftType | int32 | 否 | - | 赠礼类型 0:优惠券 1:金额 2:其他 | 0 |
|   └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|   └ coupons | array | 否 |  | 优惠券 |  |
|     └ couponId | int64 | 否 | - | 优惠券id | 0 |
|     └ couponName | string | 否 | - | 优惠券名称 |  |
|     └ couponNum | int32 | 否 | - | 优惠券数量 | 0 |

#### 响应示例

```
{
    "id": "",
    "internalVipCardId": "",
    "internalVipCardName": "",
    "name": "",
    "type": 0,
    "startTime": 0,
    "endTime": 0,
    "status": 0,
    "createBy": "",
    "updateTime": "yyyy-MM-dd HH:mm:ss",
    "gifts": [
        {
            "internalVipCardId": "",
            "internalVipCardActivityId": "",
            "internalVipCardActivityMoneyId": "",
            "type": 0,
            "giftType": 0,
            "amount": 0,
            "coupons": [
                {
                    "couponId": 0,
                    "couponName": "",
                    "couponNum": 0
                }
            ]
        }
    ]
}
```

#### 错误码

无

### 修改卡赠礼
接口权限：/market/vipCardActivity/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardActivity/edit


描述：修改卡赠礼
接口权限：/market/vipCardActivity/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | id |  |
| internalVipCardId | string | 否 | - | 内部会员卡id |  |
| internalVipCardName | string | 否 | - | 内部会员卡名称 |  |
| name | string | 否 | - | 活动名称 |  |
| type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
| startTime | int64 | 否 | - | 活动开始日期 | 0 |
| endTime | int64 | 否 | - | 活动结束日期 | 0 |
| status | int32 | 否 | - | 使用状态 0-关闭 1-开启 | 0 |
| createBy | string | 否 | - | 创建者 |  |
| updateTime | string | 否 | - | 更新时间 | yyyy-MM-dd HH:mm:ss |
| gifts | array | 否 |  | 活动方案 |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ internalVipCardActivityId | string | 否 | - | 内部会员卡充值活动id |  |
|   └ internalVipCardActivityMoneyId | string | 否 | - | 内部会员卡开卡充值金额档位id |  |
|   └ type | int32 | 否 | - | 活动类型 开卡赠礼0 充值赠礼1 | 0 |
|   └ giftType | int32 | 否 | - | 赠礼类型 0:优惠券 1:金额 2:其他 | 0 |
|   └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|   └ coupons | array | 否 |  | 优惠券 |  |
|     └ couponId | int64 | 否 | - | 优惠券id | 0 |
|     └ couponName | string | 否 | - | 优惠券名称 |  |
|     └ couponNum | int32 | 否 | - | 优惠券数量 | 0 |

#### 请求示例

```
{
    "id": "",
    "internalVipCardId": "",
    "internalVipCardName": "",
    "name": "",
    "type": 0,
    "startTime": 0,
    "endTime": 0,
    "status": 0,
    "createBy": "",
    "updateTime": "yyyy-MM-dd HH:mm:ss",
    "gifts": [
        {
            "internalVipCardId": "",
            "internalVipCardActivityId": "",
            "internalVipCardActivityMoneyId": "",
            "type": 0,
            "giftType": 0,
            "amount": 0,
            "coupons": [
                {
                    "couponId": 0,
                    "couponName": "",
                    "couponNum": 0
                }
            ]
        }
    ]
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 修改卡赠礼状态
接口权限：/market/vipCardActivity/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardActivity/editStatus


描述：修改卡赠礼状态
接口权限：/market/vipCardActivity/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cardId | string | 否 | - | 卡id |  |
| status | int32 | 否 | - | 卡状态 | 0 |

#### 请求示例

```
{
    "cardId": "",
    "status": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
## 【后台】卡政策管理


### 卡政策列表(分页)
接口权限：/market/vipCardInfo/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardInfo/search


描述：卡政策列表(分页)
接口权限：/market/vipCardInfo/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | - | No comments found. | 0 |
| name | string | 否 | - | 影城卡名称 |  |
| id | string | 否 | - | 影城卡ID |  |
| status | int32 | 否 | - | 状态 | 0 |
| startTime | int64 | 否 | - | 创建时间-开始时间 | 0 |
| endTime | int64 | 否 | - | 创建时间-结束时间 | 0 |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "name": "",
    "id": "",
    "status": 0,
    "startTime": 0,
    "endTime": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ name | string | 否 | - | 影城卡名称 |  |
|   └ description | string | 否 | - | 影城卡详情介绍 |  |
|   └ cardType | int32 | 否 | - | 卡类型：储值卡0 | 0 |
|   └ status | int32 | 否 | - | 状态: 下架0 上架1 草稿2 | 0 |
|   └ sort | int32 | 否 | - | 推荐排序 | 0 |
|   └ displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
|   └ displaySort | int32 | 否 | - | 展示排序 | 0 |
|   └ effectiveDays | int32 | 否 | - | 开卡时间起365天有效（默认） | 0 |
|   └ vipFee | int64 | 否 | - | 会费 分 | 0 |
|   └ cardCoverText | string | 否 | - | 卡片封面文案 |  |
|   └ discountText1 | string | 否 | - | 影城卡权益1 |  |
|   └ discountText2 | string | 否 | - | 影城卡权益2 |  |
|   └ discountText3 | string | 否 | - | 影城卡权益3 |  |
|   └ cinemas | array | 否 | - | 适用影院 | , |
|   └ monies | array | 否 |  | 充值档位 |  |
|     └ id | string | 否 | - | id |  |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|   └ createBy | string | 否 | - | 创建人 |  |
|   └ updateTime | int64 | 否 | - | 最后修改时间 | 0 |
|   └ rules | array | 否 |  | 权益规则 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ canUseCinemaType | int32 | 否 | - | 适用影院：全部影院0 指定影院1 | 0 |
|     └ ticketDiscountType | int32 | 否 | - | 影票优惠类型：统一规则设置0 按类型设置优惠1 | 0 |
|     └ generalSetting | int32 | 否 | - | 影片购票优惠限制-通用设置：不勾选0 勾选1 | 0 |
|     └ excludeFilm | int32 | 否 | - | 影片购票优惠限制-排除影片：不勾选0 勾选1 | 0 |
|     └ includeFilm | int32 | 否 | - | 影片购票优惠限制-指定影片：不勾选0 勾选1 | 0 |
|     └ generalEachSchedule | int32 | 否 | - | 通用设置-每场可优惠票数：不勾选0 勾选1 | 0 |
|     └ generalEachScheduleNum | int32 | 否 | - | 通用设置-每场可优惠票数 张 | 0 |
|     └ generalEachFilm | int32 | 否 | - | 通用设置-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|     └ generalEachFilmNum | int32 | 否 | - | 通用设置-每部影片可优惠票数 张 | 0 |
|     └ generalEachDay | int32 | 否 | - | 通用设置-每天可优惠票数：不勾选0 勾选1 | 0 |
|     └ generalEachDayNum | int32 | 否 | - | 通用设置-每天可优惠票数 张 | 0 |
|     └ includeFilmEachSchedule | int32 | 否 | - | 指定影片-每场可优惠票数：不勾选0 勾选1 | 0 |
|     └ includeFilmEachScheduleNum | int32 | 否 | - | 指定影片-每场可优惠票数 张 | 0 |
|     └ includeFilmEachFilm | int32 | 否 | - | 指定影片-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|     └ includeFilmEachFilmNum | int32 | 否 | - | 指定影片-每部影片可优惠票数 张 | 0 |
|     └ includeFilmEachDay | int32 | 否 | - | 指定影片-每天可优惠票数：不勾选0 勾选1 | 0 |
|     └ includeFilmEachDayNum | int32 | 否 | - | 指定影片-每天可优惠票数 张 | 0 |
|     └ servicePriceReduction | int32 | 否 | - | 服务费减免：减免0 不减免1 | 0 |
|     └ servicePriceAmount | int64 | 否 | - | 自定义服务费金额 | 0 |
|     └ goodsDiscountSetting | int32 | 否 | - | 卖品优惠设置：无优惠0 优惠1 | 0 |
|     └ goodsDiscountType | int32 | 否 | - | 卖品优惠设置-优惠：全部卖品分类0 指定卖品分类可用1 指定卖品分类不可用2 | 0 |
|     └ hallType | object | 否 |  | 影票优惠类型 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ hallTypeDiscount | int32 | 否 | - | 按影厅类型优惠：不勾选0 勾选1 | 0 |
|       └ filmTypeDiscount | int32 | 否 | - | 按影片类型优惠：不勾选0 勾选1 | 0 |
|       └ hallTypeSelect | object | 否 |  | 勾选的影厅类型 json格式 |  |
|         └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|         └ types | array | 否 |  | 选项 |  |
|           └ typeKey | string | 否 | - | 选项key |  |
|           └ typeName | string | 否 | - | 选项名称 |  |
|       └ filmTypeSelect | object | 否 |  | 勾选的影片类型 json格式 |  |
|         └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|         └ types | array | 否 |  | 选项 |  |
|           └ typeKey | string | 否 | - | 选项key |  |
|           └ typeName | string | 否 | - | 选项名称 |  |
|     └ cinemas | array | 否 |  | 适用影院 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ cinemaId | int64 | 否 | - | 影院id | 0 |
|       └ includeCinema | int32 | 否 | - | 是否包括影院：排除影院0 指定影院1 | 0 |
|       └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ tickets | array | 否 |  | 影票优惠设置-统一设置 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|       └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|       └ discountTimeInterval | object | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"endTime":"23:59:59","startTime":"00:00:00"}] |  |
|         └ days | array | 否 | - | 星期 | 0,0 |
|         └ timeIntervals | array | 否 |  | No comments found. |  |
|           └ startTime | string | 否 | - | 开始时间 |  |
|           └ endTime | string | 否 | - | 结束时间 |  |
|     └ hallFilms | array | 否 |  | 影票优惠设置-按类型 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ hallType | string | 否 | - | 影厅类型 |  |
|       └ filmType | string | 否 | - | 影片类型 |  |
|       └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|       └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|       └ groupIndex | int32 | 否 | - | 分组排序 | 0 |
|       └ discountTimeInterval | array | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"effectiveDate":[{"endTime":"23:59:59","startTime":"00:00:00"}]}] |  |
|         └ days | array | 否 | - | 星期 | 0,0 |
|         └ timeIntervals | array | 否 |  | No comments found. |  |
|           └ startTime | string | 否 | - | 开始时间 |  |
|           └ endTime | string | 否 | - | 结束时间 |  |
|     └ films | array | 否 |  | 影片优惠限制 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ filmId | int64 | 否 | - | 影片id | 0 |
|       └ includeFilm | int32 | 否 | - | 是否包括影片：指定影片1 排除影片2 | 0 |
|       └ filmName | string | 否 | - | 影片名称 |  |
|     └ goodsTypes | array | 否 |  | 卖品优惠设置 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ goodsTypeId | int64 | 否 | - | 卖品分类id | 0 |
|       └ discountType | int32 | 否 | - | 折扣类型：折扣% 0 | 0 |
|       └ discountValue | int32 | 否 | - | 折扣数值 | 0 |
|       └ goodsTypeName | string | 否 | - | 卖品分类名称 |  |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - |  |  |
|   └ name | string | 否 | - | 影城卡名称 |  |
|   └ description | string | 否 | - | 影城卡详情介绍 |  |
|   └ cardType | int32 | 否 | - | 卡类型：储值卡0 | 0 |
|   └ status | int32 | 否 | - | 状态: 下架0 上架1 草稿2 | 0 |
|   └ sort | int32 | 否 | - | 推荐排序 | 0 |
|   └ displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
|   └ displaySort | int32 | 否 | - | 展示排序 | 0 |
|   └ effectiveDays | int32 | 否 | - | 开卡时间起365天有效（默认） | 0 |
|   └ vipFee | int64 | 否 | - | 会费 分 | 0 |
|   └ cardCoverText | string | 否 | - | 卡片封面文案 |  |
|   └ discountText1 | string | 否 | - | 影城卡权益1 |  |
|   └ discountText2 | string | 否 | - | 影城卡权益2 |  |
|   └ discountText3 | string | 否 | - | 影城卡权益3 |  |
|   └ cinemas | array | 否 | - | 适用影院 | , |
|   └ monies | array | 否 |  | 充值档位 |  |
|     └ id | string | 否 | - | id |  |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
|   └ createBy | string | 否 | - | 创建人 |  |
|   └ updateTime | int64 | 否 | - | 最后修改时间 | 0 |
|   └ rules | array | 否 |  | 权益规则 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ canUseCinemaType | int32 | 否 | - | 适用影院：全部影院0 指定影院1 | 0 |
|     └ ticketDiscountType | int32 | 否 | - | 影票优惠类型：统一规则设置0 按类型设置优惠1 | 0 |
|     └ generalSetting | int32 | 否 | - | 影片购票优惠限制-通用设置：不勾选0 勾选1 | 0 |
|     └ excludeFilm | int32 | 否 | - | 影片购票优惠限制-排除影片：不勾选0 勾选1 | 0 |
|     └ includeFilm | int32 | 否 | - | 影片购票优惠限制-指定影片：不勾选0 勾选1 | 0 |
|     └ generalEachSchedule | int32 | 否 | - | 通用设置-每场可优惠票数：不勾选0 勾选1 | 0 |
|     └ generalEachScheduleNum | int32 | 否 | - | 通用设置-每场可优惠票数 张 | 0 |
|     └ generalEachFilm | int32 | 否 | - | 通用设置-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|     └ generalEachFilmNum | int32 | 否 | - | 通用设置-每部影片可优惠票数 张 | 0 |
|     └ generalEachDay | int32 | 否 | - | 通用设置-每天可优惠票数：不勾选0 勾选1 | 0 |
|     └ generalEachDayNum | int32 | 否 | - | 通用设置-每天可优惠票数 张 | 0 |
|     └ includeFilmEachSchedule | int32 | 否 | - | 指定影片-每场可优惠票数：不勾选0 勾选1 | 0 |
|     └ includeFilmEachScheduleNum | int32 | 否 | - | 指定影片-每场可优惠票数 张 | 0 |
|     └ includeFilmEachFilm | int32 | 否 | - | 指定影片-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|     └ includeFilmEachFilmNum | int32 | 否 | - | 指定影片-每部影片可优惠票数 张 | 0 |
|     └ includeFilmEachDay | int32 | 否 | - | 指定影片-每天可优惠票数：不勾选0 勾选1 | 0 |
|     └ includeFilmEachDayNum | int32 | 否 | - | 指定影片-每天可优惠票数 张 | 0 |
|     └ servicePriceReduction | int32 | 否 | - | 服务费减免：减免0 不减免1 | 0 |
|     └ servicePriceAmount | int64 | 否 | - | 自定义服务费金额 | 0 |
|     └ goodsDiscountSetting | int32 | 否 | - | 卖品优惠设置：无优惠0 优惠1 | 0 |
|     └ goodsDiscountType | int32 | 否 | - | 卖品优惠设置-优惠：全部卖品分类0 指定卖品分类可用1 指定卖品分类不可用2 | 0 |
|     └ hallType | object | 否 |  | 影票优惠类型 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ hallTypeDiscount | int32 | 否 | - | 按影厅类型优惠：不勾选0 勾选1 | 0 |
|       └ filmTypeDiscount | int32 | 否 | - | 按影片类型优惠：不勾选0 勾选1 | 0 |
|       └ hallTypeSelect | object | 否 |  | 勾选的影厅类型 json格式 |  |
|         └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|         └ types | array | 否 |  | 选项 |  |
|           └ typeKey | string | 否 | - | 选项key |  |
|           └ typeName | string | 否 | - | 选项名称 |  |
|       └ filmTypeSelect | object | 否 |  | 勾选的影片类型 json格式 |  |
|         └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|         └ types | array | 否 |  | 选项 |  |
|           └ typeKey | string | 否 | - | 选项key |  |
|           └ typeName | string | 否 | - | 选项名称 |  |
|     └ cinemas | array | 否 |  | 适用影院 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ cinemaId | int64 | 否 | - | 影院id | 0 |
|       └ includeCinema | int32 | 否 | - | 是否包括影院：排除影院0 指定影院1 | 0 |
|       └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ tickets | array | 否 |  | 影票优惠设置-统一设置 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|       └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|       └ discountTimeInterval | object | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"endTime":"23:59:59","startTime":"00:00:00"}] |  |
|         └ days | array | 否 | - | 星期 | 0,0 |
|         └ timeIntervals | array | 否 |  | No comments found. |  |
|           └ startTime | string | 否 | - | 开始时间 |  |
|           └ endTime | string | 否 | - | 结束时间 |  |
|     └ hallFilms | array | 否 |  | 影票优惠设置-按类型 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ hallType | string | 否 | - | 影厅类型 |  |
|       └ filmType | string | 否 | - | 影片类型 |  |
|       └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|       └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|       └ groupIndex | int32 | 否 | - | 分组排序 | 0 |
|       └ discountTimeInterval | array | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"effectiveDate":[{"endTime":"23:59:59","startTime":"00:00:00"}]}] |  |
|         └ days | array | 否 | - | 星期 | 0,0 |
|         └ timeIntervals | array | 否 |  | No comments found. |  |
|           └ startTime | string | 否 | - | 开始时间 |  |
|           └ endTime | string | 否 | - | 结束时间 |  |
|     └ films | array | 否 |  | 影片优惠限制 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ filmId | int64 | 否 | - | 影片id | 0 |
|       └ includeFilm | int32 | 否 | - | 是否包括影片：指定影片1 排除影片2 | 0 |
|       └ filmName | string | 否 | - | 影片名称 |  |
|     └ goodsTypes | array | 否 |  | 卖品优惠设置 |  |
|       └ id | int64 | 否 | - |  | 0 |
|       └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|       └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|       └ goodsTypeId | int64 | 否 | - | 卖品分类id | 0 |
|       └ discountType | int32 | 否 | - | 折扣类型：折扣% 0 | 0 |
|       └ discountValue | int32 | 否 | - | 折扣数值 | 0 |
|       └ goodsTypeName | string | 否 | - | 卖品分类名称 |  |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "name": "",
            "description": "",
            "cardType": 0,
            "status": 0,
            "sort": 0,
            "displayDiscountPrice": 0,
            "displaySort": 0,
            "effectiveDays": 0,
            "vipFee": 0,
            "cardCoverText": "",
            "discountText1": "",
            "discountText2": "",
            "discountText3": "",
            "cinemas": [
                0,
                0
            ],
            "monies": [
                {
                    "id": "",
                    "internalVipCardId": 0,
                    "amount": 0
                }
            ],
            "createBy": "",
            "updateTime": 0,
            "rules": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "canUseCinemaType": 0,
                    "ticketDiscountType": 0,
                    "generalSetting": 0,
                    "excludeFilm": 0,
                    "includeFilm": 0,
                    "generalEachSchedule": 0,
                    "generalEachScheduleNum": 0,
                    "generalEachFilm": 0,
                    "generalEachFilmNum": 0,
                    "generalEachDay": 0,
                    "generalEachDayNum": 0,
                    "includeFilmEachSchedule": 0,
                    "includeFilmEachScheduleNum": 0,
                    "includeFilmEachFilm": 0,
                    "includeFilmEachFilmNum": 0,
                    "includeFilmEachDay": 0,
                    "includeFilmEachDayNum": 0,
                    "servicePriceReduction": 0,
                    "servicePriceAmount": 0,
                    "goodsDiscountSetting": 0,
                    "goodsDiscountType": 0,
                    "hallType": {
                        "id": 0,
                        "internalVipCardId": 0,
                        "internalVipCardDiscountRuleId": 0,
                        "hallTypeDiscount": 0,
                        "filmTypeDiscount": 0,
                        "hallTypeSelect": {
                            "isSelectAll": true,
                            "types": [
                                {
                                    "typeKey": "",
                                    "typeName": ""
                                }
                            ]
                        },
                        "filmTypeSelect": {
                            "isSelectAll": true,
                            "types": [
                                {
                                    "typeKey": "",
                                    "typeName": ""
                                }
                            ]
                        }
                    },
                    "cinemas": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "cinemaId": 0,
                            "includeCinema": 0,
                            "cinemaName": ""
                        }
                    ],
                    "tickets": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "discountType": 0,
                            "discountValue": 0,
                            "discountTimeInterval": {
                                "days": [
                                    0,
                                    0
                                ],
                                "timeIntervals": [
                                    {
                                        "startTime": "",
                                        "endTime": ""
                                    }
                                ]
                            }
                        }
                    ],
                    "hallFilms": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "hallType": "",
                            "filmType": "",
                            "discountType": 0,
                            "discountValue": 0,
                            "groupIndex": 0,
                            "discountTimeInterval": [
                                {
                                    "days": [
                                        0,
                                        0
                                    ],
                                    "timeIntervals": [
                                        {
                                            "startTime": "",
                                            "endTime": ""
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    "films": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "filmId": 0,
                            "includeFilm": 0,
                            "filmName": ""
                        }
                    ],
                    "goodsTypes": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "goodsTypeId": 0,
                            "discountType": 0,
                            "discountValue": 0,
                            "goodsTypeName": ""
                        }
                    ]
                }
            ]
        }
    ],
    "result": [
        {
            "id": "",
            "name": "",
            "description": "",
            "cardType": 0,
            "status": 0,
            "sort": 0,
            "displayDiscountPrice": 0,
            "displaySort": 0,
            "effectiveDays": 0,
            "vipFee": 0,
            "cardCoverText": "",
            "discountText1": "",
            "discountText2": "",
            "discountText3": "",
            "cinemas": [
                0,
                0
            ],
            "monies": [
                {
                    "id": "",
                    "internalVipCardId": 0,
                    "amount": 0
                }
            ],
            "createBy": "",
            "updateTime": 0,
            "rules": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "canUseCinemaType": 0,
                    "ticketDiscountType": 0,
                    "generalSetting": 0,
                    "excludeFilm": 0,
                    "includeFilm": 0,
                    "generalEachSchedule": 0,
                    "generalEachScheduleNum": 0,
                    "generalEachFilm": 0,
                    "generalEachFilmNum": 0,
                    "generalEachDay": 0,
                    "generalEachDayNum": 0,
                    "includeFilmEachSchedule": 0,
                    "includeFilmEachScheduleNum": 0,
                    "includeFilmEachFilm": 0,
                    "includeFilmEachFilmNum": 0,
                    "includeFilmEachDay": 0,
                    "includeFilmEachDayNum": 0,
                    "servicePriceReduction": 0,
                    "servicePriceAmount": 0,
                    "goodsDiscountSetting": 0,
                    "goodsDiscountType": 0,
                    "hallType": {
                        "id": 0,
                        "internalVipCardId": 0,
                        "internalVipCardDiscountRuleId": 0,
                        "hallTypeDiscount": 0,
                        "filmTypeDiscount": 0,
                        "hallTypeSelect": {
                            "isSelectAll": true,
                            "types": [
                                {
                                    "typeKey": "",
                                    "typeName": ""
                                }
                            ]
                        },
                        "filmTypeSelect": {
                            "isSelectAll": true,
                            "types": [
                                {
                                    "typeKey": "",
                                    "typeName": ""
                                }
                            ]
                        }
                    },
                    "cinemas": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "cinemaId": 0,
                            "includeCinema": 0,
                            "cinemaName": ""
                        }
                    ],
                    "tickets": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "discountType": 0,
                            "discountValue": 0,
                            "discountTimeInterval": {
                                "days": [
                                    0,
                                    0
                                ],
                                "timeIntervals": [
                                    {
                                        "startTime": "",
                                        "endTime": ""
                                    }
                                ]
                            }
                        }
                    ],
                    "hallFilms": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "hallType": "",
                            "filmType": "",
                            "discountType": 0,
                            "discountValue": 0,
                            "groupIndex": 0,
                            "discountTimeInterval": [
                                {
                                    "days": [
                                        0,
                                        0
                                    ],
                                    "timeIntervals": [
                                        {
                                            "startTime": "",
                                            "endTime": ""
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    "films": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "filmId": 0,
                            "includeFilm": 0,
                            "filmName": ""
                        }
                    ],
                    "goodsTypes": [
                        {
                            "id": 0,
                            "internalVipCardId": 0,
                            "internalVipCardDiscountRuleId": 0,
                            "goodsTypeId": 0,
                            "discountType": 0,
                            "discountValue": 0,
                            "goodsTypeName": ""
                        }
                    ]
                }
            ]
        }
    ]
}
```

#### 错误码

无

### 卡政策列表
接口权限：/market/vipCardInfo/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardInfo/list


描述：卡政策列表
接口权限：/market/vipCardInfo/query

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - |  |  |
| name | string | 否 | - | 影城卡名称 |  |
| description | string | 否 | - | 影城卡详情介绍 |  |
| cardType | int32 | 否 | - | 卡类型：储值卡0 | 0 |
| status | int32 | 否 | - | 状态: 下架0 上架1 草稿2 | 0 |
| sort | int32 | 否 | - | 推荐排序 | 0 |
| displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
| displaySort | int32 | 否 | - | 展示排序 | 0 |
| effectiveDays | int32 | 否 | - | 开卡时间起365天有效（默认） | 0 |
| vipFee | int64 | 否 | - | 会费 分 | 0 |
| cardCoverText | string | 否 | - | 卡片封面文案 |  |
| discountText1 | string | 否 | - | 影城卡权益1 |  |
| discountText2 | string | 否 | - | 影城卡权益2 |  |
| discountText3 | string | 否 | - | 影城卡权益3 |  |
| cinemas | array | 否 | - | 适用影院 | , |
| monies | array | 否 |  | 充值档位 |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|   └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
| createBy | string | 否 | - | 创建人 |  |
| updateTime | int64 | 否 | - | 最后修改时间 | 0 |
| rules | array | 否 |  | 权益规则 |  |
|   └ id | int64 | 否 | - |  | 0 |
|   └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|   └ canUseCinemaType | int32 | 否 | - | 适用影院：全部影院0 指定影院1 | 0 |
|   └ ticketDiscountType | int32 | 否 | - | 影票优惠类型：统一规则设置0 按类型设置优惠1 | 0 |
|   └ generalSetting | int32 | 否 | - | 影片购票优惠限制-通用设置：不勾选0 勾选1 | 0 |
|   └ excludeFilm | int32 | 否 | - | 影片购票优惠限制-排除影片：不勾选0 勾选1 | 0 |
|   └ includeFilm | int32 | 否 | - | 影片购票优惠限制-指定影片：不勾选0 勾选1 | 0 |
|   └ generalEachSchedule | int32 | 否 | - | 通用设置-每场可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachScheduleNum | int32 | 否 | - | 通用设置-每场可优惠票数 张 | 0 |
|   └ generalEachFilm | int32 | 否 | - | 通用设置-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachFilmNum | int32 | 否 | - | 通用设置-每部影片可优惠票数 张 | 0 |
|   └ generalEachDay | int32 | 否 | - | 通用设置-每天可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachDayNum | int32 | 否 | - | 通用设置-每天可优惠票数 张 | 0 |
|   └ includeFilmEachSchedule | int32 | 否 | - | 指定影片-每场可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachScheduleNum | int32 | 否 | - | 指定影片-每场可优惠票数 张 | 0 |
|   └ includeFilmEachFilm | int32 | 否 | - | 指定影片-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachFilmNum | int32 | 否 | - | 指定影片-每部影片可优惠票数 张 | 0 |
|   └ includeFilmEachDay | int32 | 否 | - | 指定影片-每天可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachDayNum | int32 | 否 | - | 指定影片-每天可优惠票数 张 | 0 |
|   └ servicePriceReduction | int32 | 否 | - | 服务费减免：减免0 不减免1 | 0 |
|   └ servicePriceAmount | int64 | 否 | - | 自定义服务费金额 | 0 |
|   └ goodsDiscountSetting | int32 | 否 | - | 卖品优惠设置：无优惠0 优惠1 | 0 |
|   └ goodsDiscountType | int32 | 否 | - | 卖品优惠设置-优惠：全部卖品分类0 指定卖品分类可用1 指定卖品分类不可用2 | 0 |
|   └ hallType | object | 否 |  | 影票优惠类型 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ hallTypeDiscount | int32 | 否 | - | 按影厅类型优惠：不勾选0 勾选1 | 0 |
|     └ filmTypeDiscount | int32 | 否 | - | 按影片类型优惠：不勾选0 勾选1 | 0 |
|     └ hallTypeSelect | object | 否 |  | 勾选的影厅类型 json格式 |  |
|       └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|       └ types | array | 否 |  | 选项 |  |
|         └ typeKey | string | 否 | - | 选项key |  |
|         └ typeName | string | 否 | - | 选项名称 |  |
|     └ filmTypeSelect | object | 否 |  | 勾选的影片类型 json格式 |  |
|       └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|       └ types | array | 否 |  | 选项 |  |
|         └ typeKey | string | 否 | - | 选项key |  |
|         └ typeName | string | 否 | - | 选项名称 |  |
|   └ cinemas | array | 否 |  | 适用影院 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ cinemaId | int64 | 否 | - | 影院id | 0 |
|     └ includeCinema | int32 | 否 | - | 是否包括影院：排除影院0 指定影院1 | 0 |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|   └ tickets | array | 否 |  | 影票优惠设置-统一设置 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|     └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|     └ discountTimeInterval | object | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"endTime":"23:59:59","startTime":"00:00:00"}] |  |
|       └ days | array | 否 | - | 星期 | 0,0 |
|       └ timeIntervals | array | 否 |  | No comments found. |  |
|         └ startTime | string | 否 | - | 开始时间 |  |
|         └ endTime | string | 否 | - | 结束时间 |  |
|   └ hallFilms | array | 否 |  | 影票优惠设置-按类型 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ hallType | string | 否 | - | 影厅类型 |  |
|     └ filmType | string | 否 | - | 影片类型 |  |
|     └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|     └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|     └ groupIndex | int32 | 否 | - | 分组排序 | 0 |
|     └ discountTimeInterval | array | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"effectiveDate":[{"endTime":"23:59:59","startTime":"00:00:00"}]}] |  |
|       └ days | array | 否 | - | 星期 | 0,0 |
|       └ timeIntervals | array | 否 |  | No comments found. |  |
|         └ startTime | string | 否 | - | 开始时间 |  |
|         └ endTime | string | 否 | - | 结束时间 |  |
|   └ films | array | 否 |  | 影片优惠限制 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ filmId | int64 | 否 | - | 影片id | 0 |
|     └ includeFilm | int32 | 否 | - | 是否包括影片：指定影片1 排除影片2 | 0 |
|     └ filmName | string | 否 | - | 影片名称 |  |
|   └ goodsTypes | array | 否 |  | 卖品优惠设置 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ goodsTypeId | int64 | 否 | - | 卖品分类id | 0 |
|     └ discountType | int32 | 否 | - | 折扣类型：折扣% 0 | 0 |
|     └ discountValue | int32 | 否 | - | 折扣数值 | 0 |
|     └ goodsTypeName | string | 否 | - | 卖品分类名称 |  |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "description": "",
    "cardType": 0,
    "status": 0,
    "sort": 0,
    "displayDiscountPrice": 0,
    "displaySort": 0,
    "effectiveDays": 0,
    "vipFee": 0,
    "cardCoverText": "",
    "discountText1": "",
    "discountText2": "",
    "discountText3": "",
    "cinemas": [
        0,
        0
    ],
    "monies": [
        {
            "id": "",
            "internalVipCardId": 0,
            "amount": 0
        }
    ],
    "createBy": "",
    "updateTime": 0,
    "rules": [
        {
            "id": 0,
            "internalVipCardId": 0,
            "canUseCinemaType": 0,
            "ticketDiscountType": 0,
            "generalSetting": 0,
            "excludeFilm": 0,
            "includeFilm": 0,
            "generalEachSchedule": 0,
            "generalEachScheduleNum": 0,
            "generalEachFilm": 0,
            "generalEachFilmNum": 0,
            "generalEachDay": 0,
            "generalEachDayNum": 0,
            "includeFilmEachSchedule": 0,
            "includeFilmEachScheduleNum": 0,
            "includeFilmEachFilm": 0,
            "includeFilmEachFilmNum": 0,
            "includeFilmEachDay": 0,
            "includeFilmEachDayNum": 0,
            "servicePriceReduction": 0,
            "servicePriceAmount": 0,
            "goodsDiscountSetting": 0,
            "goodsDiscountType": 0,
            "hallType": {
                "id": 0,
                "internalVipCardId": 0,
                "internalVipCardDiscountRuleId": 0,
                "hallTypeDiscount": 0,
                "filmTypeDiscount": 0,
                "hallTypeSelect": {
                    "isSelectAll": true,
                    "types": [
                        {
                            "typeKey": "",
                            "typeName": ""
                        }
                    ]
                },
                "filmTypeSelect": {
                    "isSelectAll": true,
                    "types": [
                        {
                            "typeKey": "",
                            "typeName": ""
                        }
                    ]
                }
            },
            "cinemas": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "cinemaId": 0,
                    "includeCinema": 0,
                    "cinemaName": ""
                }
            ],
            "tickets": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "discountType": 0,
                    "discountValue": 0,
                    "discountTimeInterval": {
                        "days": [
                            0,
                            0
                        ],
                        "timeIntervals": [
                            {
                                "startTime": "",
                                "endTime": ""
                            }
                        ]
                    }
                }
            ],
            "hallFilms": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "hallType": "",
                    "filmType": "",
                    "discountType": 0,
                    "discountValue": 0,
                    "groupIndex": 0,
                    "discountTimeInterval": [
                        {
                            "days": [
                                0,
                                0
                            ],
                            "timeIntervals": [
                                {
                                    "startTime": "",
                                    "endTime": ""
                                }
                            ]
                        }
                    ]
                }
            ],
            "films": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "filmId": 0,
                    "includeFilm": 0,
                    "filmName": ""
                }
            ],
            "goodsTypes": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "goodsTypeId": 0,
                    "discountType": 0,
                    "discountValue": 0,
                    "goodsTypeName": ""
                }
            ]
        }
    ]
}
```

#### 错误码

无

### 查看卡政策详情
接口权限：/market/vipCardInfo/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardInfo/detail


描述：查看卡政策详情
接口权限：/market/vipCardInfo/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cardId | string | 否 | - | 卡id |  |

#### 请求示例

```
{
    "cardId": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - |  |  |
| name | string | 否 | - | 影城卡名称 |  |
| description | string | 否 | - | 影城卡详情介绍 |  |
| cardType | int32 | 否 | - | 卡类型：储值卡0 | 0 |
| status | int32 | 否 | - | 状态: 下架0 上架1 草稿2 | 0 |
| sort | int32 | 否 | - | 推荐排序 | 0 |
| displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
| displaySort | int32 | 否 | - | 展示排序 | 0 |
| effectiveDays | int32 | 否 | - | 开卡时间起365天有效（默认） | 0 |
| vipFee | int64 | 否 | - | 会费 分 | 0 |
| cardCoverText | string | 否 | - | 卡片封面文案 |  |
| discountText1 | string | 否 | - | 影城卡权益1 |  |
| discountText2 | string | 否 | - | 影城卡权益2 |  |
| discountText3 | string | 否 | - | 影城卡权益3 |  |
| cinemas | array | 否 | - | 适用影院 | , |
| monies | array | 否 |  | 充值档位 |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|   └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
| createBy | string | 否 | - | 创建人 |  |
| updateTime | int64 | 否 | - | 最后修改时间 | 0 |
| rules | array | 否 |  | 权益规则 |  |
|   └ id | int64 | 否 | - |  | 0 |
|   └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|   └ canUseCinemaType | int32 | 否 | - | 适用影院：全部影院0 指定影院1 | 0 |
|   └ ticketDiscountType | int32 | 否 | - | 影票优惠类型：统一规则设置0 按类型设置优惠1 | 0 |
|   └ generalSetting | int32 | 否 | - | 影片购票优惠限制-通用设置：不勾选0 勾选1 | 0 |
|   └ excludeFilm | int32 | 否 | - | 影片购票优惠限制-排除影片：不勾选0 勾选1 | 0 |
|   └ includeFilm | int32 | 否 | - | 影片购票优惠限制-指定影片：不勾选0 勾选1 | 0 |
|   └ generalEachSchedule | int32 | 否 | - | 通用设置-每场可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachScheduleNum | int32 | 否 | - | 通用设置-每场可优惠票数 张 | 0 |
|   └ generalEachFilm | int32 | 否 | - | 通用设置-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachFilmNum | int32 | 否 | - | 通用设置-每部影片可优惠票数 张 | 0 |
|   └ generalEachDay | int32 | 否 | - | 通用设置-每天可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachDayNum | int32 | 否 | - | 通用设置-每天可优惠票数 张 | 0 |
|   └ includeFilmEachSchedule | int32 | 否 | - | 指定影片-每场可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachScheduleNum | int32 | 否 | - | 指定影片-每场可优惠票数 张 | 0 |
|   └ includeFilmEachFilm | int32 | 否 | - | 指定影片-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachFilmNum | int32 | 否 | - | 指定影片-每部影片可优惠票数 张 | 0 |
|   └ includeFilmEachDay | int32 | 否 | - | 指定影片-每天可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachDayNum | int32 | 否 | - | 指定影片-每天可优惠票数 张 | 0 |
|   └ servicePriceReduction | int32 | 否 | - | 服务费减免：减免0 不减免1 | 0 |
|   └ servicePriceAmount | int64 | 否 | - | 自定义服务费金额 | 0 |
|   └ goodsDiscountSetting | int32 | 否 | - | 卖品优惠设置：无优惠0 优惠1 | 0 |
|   └ goodsDiscountType | int32 | 否 | - | 卖品优惠设置-优惠：全部卖品分类0 指定卖品分类可用1 指定卖品分类不可用2 | 0 |
|   └ hallType | object | 否 |  | 影票优惠类型 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ hallTypeDiscount | int32 | 否 | - | 按影厅类型优惠：不勾选0 勾选1 | 0 |
|     └ filmTypeDiscount | int32 | 否 | - | 按影片类型优惠：不勾选0 勾选1 | 0 |
|     └ hallTypeSelect | object | 否 |  | 勾选的影厅类型 json格式 |  |
|       └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|       └ types | array | 否 |  | 选项 |  |
|         └ typeKey | string | 否 | - | 选项key |  |
|         └ typeName | string | 否 | - | 选项名称 |  |
|     └ filmTypeSelect | object | 否 |  | 勾选的影片类型 json格式 |  |
|       └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|       └ types | array | 否 |  | 选项 |  |
|         └ typeKey | string | 否 | - | 选项key |  |
|         └ typeName | string | 否 | - | 选项名称 |  |
|   └ cinemas | array | 否 |  | 适用影院 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ cinemaId | int64 | 否 | - | 影院id | 0 |
|     └ includeCinema | int32 | 否 | - | 是否包括影院：排除影院0 指定影院1 | 0 |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|   └ tickets | array | 否 |  | 影票优惠设置-统一设置 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|     └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|     └ discountTimeInterval | object | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"endTime":"23:59:59","startTime":"00:00:00"}] |  |
|       └ days | array | 否 | - | 星期 | 0,0 |
|       └ timeIntervals | array | 否 |  | No comments found. |  |
|         └ startTime | string | 否 | - | 开始时间 |  |
|         └ endTime | string | 否 | - | 结束时间 |  |
|   └ hallFilms | array | 否 |  | 影票优惠设置-按类型 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ hallType | string | 否 | - | 影厅类型 |  |
|     └ filmType | string | 否 | - | 影片类型 |  |
|     └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|     └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|     └ groupIndex | int32 | 否 | - | 分组排序 | 0 |
|     └ discountTimeInterval | array | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"effectiveDate":[{"endTime":"23:59:59","startTime":"00:00:00"}]}] |  |
|       └ days | array | 否 | - | 星期 | 0,0 |
|       └ timeIntervals | array | 否 |  | No comments found. |  |
|         └ startTime | string | 否 | - | 开始时间 |  |
|         └ endTime | string | 否 | - | 结束时间 |  |
|   └ films | array | 否 |  | 影片优惠限制 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ filmId | int64 | 否 | - | 影片id | 0 |
|     └ includeFilm | int32 | 否 | - | 是否包括影片：指定影片1 排除影片2 | 0 |
|     └ filmName | string | 否 | - | 影片名称 |  |
|   └ goodsTypes | array | 否 |  | 卖品优惠设置 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ goodsTypeId | int64 | 否 | - | 卖品分类id | 0 |
|     └ discountType | int32 | 否 | - | 折扣类型：折扣% 0 | 0 |
|     └ discountValue | int32 | 否 | - | 折扣数值 | 0 |
|     └ goodsTypeName | string | 否 | - | 卖品分类名称 |  |

#### 响应示例

```
{
    "id": "",
    "name": "",
    "description": "",
    "cardType": 0,
    "status": 0,
    "sort": 0,
    "displayDiscountPrice": 0,
    "displaySort": 0,
    "effectiveDays": 0,
    "vipFee": 0,
    "cardCoverText": "",
    "discountText1": "",
    "discountText2": "",
    "discountText3": "",
    "cinemas": [
        0,
        0
    ],
    "monies": [
        {
            "id": "",
            "internalVipCardId": 0,
            "amount": 0
        }
    ],
    "createBy": "",
    "updateTime": 0,
    "rules": [
        {
            "id": 0,
            "internalVipCardId": 0,
            "canUseCinemaType": 0,
            "ticketDiscountType": 0,
            "generalSetting": 0,
            "excludeFilm": 0,
            "includeFilm": 0,
            "generalEachSchedule": 0,
            "generalEachScheduleNum": 0,
            "generalEachFilm": 0,
            "generalEachFilmNum": 0,
            "generalEachDay": 0,
            "generalEachDayNum": 0,
            "includeFilmEachSchedule": 0,
            "includeFilmEachScheduleNum": 0,
            "includeFilmEachFilm": 0,
            "includeFilmEachFilmNum": 0,
            "includeFilmEachDay": 0,
            "includeFilmEachDayNum": 0,
            "servicePriceReduction": 0,
            "servicePriceAmount": 0,
            "goodsDiscountSetting": 0,
            "goodsDiscountType": 0,
            "hallType": {
                "id": 0,
                "internalVipCardId": 0,
                "internalVipCardDiscountRuleId": 0,
                "hallTypeDiscount": 0,
                "filmTypeDiscount": 0,
                "hallTypeSelect": {
                    "isSelectAll": true,
                    "types": [
                        {
                            "typeKey": "",
                            "typeName": ""
                        }
                    ]
                },
                "filmTypeSelect": {
                    "isSelectAll": true,
                    "types": [
                        {
                            "typeKey": "",
                            "typeName": ""
                        }
                    ]
                }
            },
            "cinemas": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "cinemaId": 0,
                    "includeCinema": 0,
                    "cinemaName": ""
                }
            ],
            "tickets": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "discountType": 0,
                    "discountValue": 0,
                    "discountTimeInterval": {
                        "days": [
                            0,
                            0
                        ],
                        "timeIntervals": [
                            {
                                "startTime": "",
                                "endTime": ""
                            }
                        ]
                    }
                }
            ],
            "hallFilms": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "hallType": "",
                    "filmType": "",
                    "discountType": 0,
                    "discountValue": 0,
                    "groupIndex": 0,
                    "discountTimeInterval": [
                        {
                            "days": [
                                0,
                                0
                            ],
                            "timeIntervals": [
                                {
                                    "startTime": "",
                                    "endTime": ""
                                }
                            ]
                        }
                    ]
                }
            ],
            "films": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "filmId": 0,
                    "includeFilm": 0,
                    "filmName": ""
                }
            ],
            "goodsTypes": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "goodsTypeId": 0,
                    "discountType": 0,
                    "discountValue": 0,
                    "goodsTypeName": ""
                }
            ]
        }
    ]
}
```

#### 错误码

无

### 修改卡政策
接口权限：/data/vipCardInfo/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardInfo/edit


描述：修改卡政策
接口权限：/data/vipCardInfo/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - |  |  |
| name | string | 否 | - | 影城卡名称 |  |
| description | string | 否 | - | 影城卡详情介绍 |  |
| cardType | int32 | 否 | - | 卡类型：储值卡0 | 0 |
| status | int32 | 否 | - | 状态: 下架0 上架1 草稿2 | 0 |
| sort | int32 | 否 | - | 推荐排序 | 0 |
| displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
| displaySort | int32 | 否 | - | 展示排序 | 0 |
| effectiveDays | int32 | 否 | - | 开卡时间起365天有效（默认） | 0 |
| vipFee | int64 | 否 | - | 会费 分 | 0 |
| cardCoverText | string | 否 | - | 卡片封面文案 |  |
| discountText1 | string | 否 | - | 影城卡权益1 |  |
| discountText2 | string | 否 | - | 影城卡权益2 |  |
| discountText3 | string | 否 | - | 影城卡权益3 |  |
| cinemas | array | 否 | - | 适用影院 | , |
| monies | array | 否 |  | 充值档位 |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|   └ amount | int64 | 否 | - | 金额 单位：分 | 0 |
| createBy | string | 否 | - | 创建人 |  |
| updateTime | int64 | 否 | - | 最后修改时间 | 0 |
| rules | array | 否 |  | 权益规则 |  |
|   └ id | int64 | 否 | - |  | 0 |
|   └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|   └ canUseCinemaType | int32 | 否 | - | 适用影院：全部影院0 指定影院1 | 0 |
|   └ ticketDiscountType | int32 | 否 | - | 影票优惠类型：统一规则设置0 按类型设置优惠1 | 0 |
|   └ generalSetting | int32 | 否 | - | 影片购票优惠限制-通用设置：不勾选0 勾选1 | 0 |
|   └ excludeFilm | int32 | 否 | - | 影片购票优惠限制-排除影片：不勾选0 勾选1 | 0 |
|   └ includeFilm | int32 | 否 | - | 影片购票优惠限制-指定影片：不勾选0 勾选1 | 0 |
|   └ generalEachSchedule | int32 | 否 | - | 通用设置-每场可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachScheduleNum | int32 | 否 | - | 通用设置-每场可优惠票数 张 | 0 |
|   └ generalEachFilm | int32 | 否 | - | 通用设置-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachFilmNum | int32 | 否 | - | 通用设置-每部影片可优惠票数 张 | 0 |
|   └ generalEachDay | int32 | 否 | - | 通用设置-每天可优惠票数：不勾选0 勾选1 | 0 |
|   └ generalEachDayNum | int32 | 否 | - | 通用设置-每天可优惠票数 张 | 0 |
|   └ includeFilmEachSchedule | int32 | 否 | - | 指定影片-每场可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachScheduleNum | int32 | 否 | - | 指定影片-每场可优惠票数 张 | 0 |
|   └ includeFilmEachFilm | int32 | 否 | - | 指定影片-每部影片可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachFilmNum | int32 | 否 | - | 指定影片-每部影片可优惠票数 张 | 0 |
|   └ includeFilmEachDay | int32 | 否 | - | 指定影片-每天可优惠票数：不勾选0 勾选1 | 0 |
|   └ includeFilmEachDayNum | int32 | 否 | - | 指定影片-每天可优惠票数 张 | 0 |
|   └ servicePriceReduction | int32 | 否 | - | 服务费减免：减免0 不减免1 | 0 |
|   └ servicePriceAmount | int64 | 否 | - | 自定义服务费金额 | 0 |
|   └ goodsDiscountSetting | int32 | 否 | - | 卖品优惠设置：无优惠0 优惠1 | 0 |
|   └ goodsDiscountType | int32 | 否 | - | 卖品优惠设置-优惠：全部卖品分类0 指定卖品分类可用1 指定卖品分类不可用2 | 0 |
|   └ hallType | object | 否 |  | 影票优惠类型 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ hallTypeDiscount | int32 | 否 | - | 按影厅类型优惠：不勾选0 勾选1 | 0 |
|     └ filmTypeDiscount | int32 | 否 | - | 按影片类型优惠：不勾选0 勾选1 | 0 |
|     └ hallTypeSelect | object | 否 |  | 勾选的影厅类型 json格式 |  |
|       └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|       └ types | array | 否 |  | 选项 |  |
|         └ typeKey | string | 否 | - | 选项key |  |
|         └ typeName | string | 否 | - | 选项名称 |  |
|     └ filmTypeSelect | object | 否 |  | 勾选的影片类型 json格式 |  |
|       └ isSelectAll | boolean | 否 | - | 是否全选 | true |
|       └ types | array | 否 |  | 选项 |  |
|         └ typeKey | string | 否 | - | 选项key |  |
|         └ typeName | string | 否 | - | 选项名称 |  |
|   └ cinemas | array | 否 |  | 适用影院 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ cinemaId | int64 | 否 | - | 影院id | 0 |
|     └ includeCinema | int32 | 否 | - | 是否包括影院：排除影院0 指定影院1 | 0 |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|   └ tickets | array | 否 |  | 影票优惠设置-统一设置 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|     └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|     └ discountTimeInterval | object | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"endTime":"23:59:59","startTime":"00:00:00"}] |  |
|       └ days | array | 否 | - | 星期 | 0,0 |
|       └ timeIntervals | array | 否 |  | No comments found. |  |
|         └ startTime | string | 否 | - | 开始时间 |  |
|         └ endTime | string | 否 | - | 结束时间 |  |
|   └ hallFilms | array | 否 |  | 影票优惠设置-按类型 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ hallType | string | 否 | - | 影厅类型 |  |
|     └ filmType | string | 否 | - | 影片类型 |  |
|     └ discountType | int32 | 否 | - | 折扣类型：固定价0 | 0 |
|     └ discountValue | int64 | 否 | - | 折扣数值 | 0 |
|     └ groupIndex | int32 | 否 | - | 分组排序 | 0 |
|     └ discountTimeInterval | array | 否 |  | 影票优惠设置-折扣时间区间 json格式样例[{"days":[1,2,3,4,5,6,7],"effectiveDate":[{"endTime":"23:59:59","startTime":"00:00:00"}]}] |  |
|       └ days | array | 否 | - | 星期 | 0,0 |
|       └ timeIntervals | array | 否 |  | No comments found. |  |
|         └ startTime | string | 否 | - | 开始时间 |  |
|         └ endTime | string | 否 | - | 结束时间 |  |
|   └ films | array | 否 |  | 影片优惠限制 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ filmId | int64 | 否 | - | 影片id | 0 |
|     └ includeFilm | int32 | 否 | - | 是否包括影片：指定影片1 排除影片2 | 0 |
|     └ filmName | string | 否 | - | 影片名称 |  |
|   └ goodsTypes | array | 否 |  | 卖品优惠设置 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ internalVipCardId | int64 | 否 | - | 内部会员卡id | 0 |
|     └ internalVipCardDiscountRuleId | int64 | 否 | - | 权益规则id | 0 |
|     └ goodsTypeId | int64 | 否 | - | 卖品分类id | 0 |
|     └ discountType | int32 | 否 | - | 折扣类型：折扣% 0 | 0 |
|     └ discountValue | int32 | 否 | - | 折扣数值 | 0 |
|     └ goodsTypeName | string | 否 | - | 卖品分类名称 |  |

#### 请求示例

```
{
    "id": "",
    "name": "",
    "description": "",
    "cardType": 0,
    "status": 0,
    "sort": 0,
    "displayDiscountPrice": 0,
    "displaySort": 0,
    "effectiveDays": 0,
    "vipFee": 0,
    "cardCoverText": "",
    "discountText1": "",
    "discountText2": "",
    "discountText3": "",
    "cinemas": [
        0,
        0
    ],
    "monies": [
        {
            "id": "",
            "internalVipCardId": 0,
            "amount": 0
        }
    ],
    "createBy": "",
    "updateTime": 0,
    "rules": [
        {
            "id": 0,
            "internalVipCardId": 0,
            "canUseCinemaType": 0,
            "ticketDiscountType": 0,
            "generalSetting": 0,
            "excludeFilm": 0,
            "includeFilm": 0,
            "generalEachSchedule": 0,
            "generalEachScheduleNum": 0,
            "generalEachFilm": 0,
            "generalEachFilmNum": 0,
            "generalEachDay": 0,
            "generalEachDayNum": 0,
            "includeFilmEachSchedule": 0,
            "includeFilmEachScheduleNum": 0,
            "includeFilmEachFilm": 0,
            "includeFilmEachFilmNum": 0,
            "includeFilmEachDay": 0,
            "includeFilmEachDayNum": 0,
            "servicePriceReduction": 0,
            "servicePriceAmount": 0,
            "goodsDiscountSetting": 0,
            "goodsDiscountType": 0,
            "hallType": {
                "id": 0,
                "internalVipCardId": 0,
                "internalVipCardDiscountRuleId": 0,
                "hallTypeDiscount": 0,
                "filmTypeDiscount": 0,
                "hallTypeSelect": {
                    "isSelectAll": true,
                    "types": [
                        {
                            "typeKey": "",
                            "typeName": ""
                        }
                    ]
                },
                "filmTypeSelect": {
                    "isSelectAll": true,
                    "types": [
                        {
                            "typeKey": "",
                            "typeName": ""
                        }
                    ]
                }
            },
            "cinemas": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "cinemaId": 0,
                    "includeCinema": 0,
                    "cinemaName": ""
                }
            ],
            "tickets": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "discountType": 0,
                    "discountValue": 0,
                    "discountTimeInterval": {
                        "days": [
                            0,
                            0
                        ],
                        "timeIntervals": [
                            {
                                "startTime": "",
                                "endTime": ""
                            }
                        ]
                    }
                }
            ],
            "hallFilms": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "hallType": "",
                    "filmType": "",
                    "discountType": 0,
                    "discountValue": 0,
                    "groupIndex": 0,
                    "discountTimeInterval": [
                        {
                            "days": [
                                0,
                                0
                            ],
                            "timeIntervals": [
                                {
                                    "startTime": "",
                                    "endTime": ""
                                }
                            ]
                        }
                    ]
                }
            ],
            "films": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "filmId": 0,
                    "includeFilm": 0,
                    "filmName": ""
                }
            ],
            "goodsTypes": [
                {
                    "id": 0,
                    "internalVipCardId": 0,
                    "internalVipCardDiscountRuleId": 0,
                    "goodsTypeId": 0,
                    "discountType": 0,
                    "discountValue": 0,
                    "goodsTypeName": ""
                }
            ]
        }
    ]
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 修改卡政策
接口权限：/data/vipCardInfo/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardInfo/editStatus


描述：修改卡政策
接口权限：/data/vipCardInfo/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cardId | string | 否 | - | 卡id |  |
| status | int32 | 否 | - | 卡状态 | 0 |

#### 请求示例

```
{
    "cardId": "",
    "status": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
## 【后台】会员卡管理


### 会员卡列表(分页)
接口权限：/market/vipCardUser/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardUser/search


描述：会员卡列表(分页)
接口权限：/market/vipCardUser/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | - | No comments found. | 0 |
| cardNumber | string | 否 | - | 卡号 |  |
| internalVipCardId | string | 否 | - | 卡政策ID |  |
| cardType | string | 否 | - | 卡类型 |  |
| cinemaName | string | 否 | - | 发卡影院 |  |
| phoneNumber | string | 否 | - | 手机号码 |  |
| status | int32 | 否 | - | 状态 | 0 |
| startTime | int64 | 否 | - | 有效日期-开始时间 | 0 |
| endTime | int64 | 否 | - | 有效日期-结束时间 | 0 |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "cardNumber": "",
    "internalVipCardId": "",
    "cardType": "",
    "cinemaName": "",
    "phoneNumber": "",
    "status": 0,
    "startTime": 0,
    "endTime": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ batchId | string | 否 | - | 批次号 生成规则8位数字 自增 |  |
|   └ cardNumber | string | 否 | - | 内部会员卡卡号 |  |
|   └ name | string | 否 | - | 影城卡名称 |  |
|   └ cardType | string | 否 | - | 影城卡类型 |  |
|   └ cardTypeName | string | 否 | - | 影城卡类型名称 |  |
|   └ cinemaId | int64 | 否 | - | 影城id | 0 |
|   └ cinemaName | string | 否 | - | 影城名称 |  |
|   └ userId | int64 | 否 | - | 用户id | 0 |
|   └ nickName | string | 否 | - | 会员名称 |  |
|   └ phoneNumber | string | 否 | - | 手机号码 |  |
|   └ cardAmount | int32 | 否 | - | 卡内余额 | 0 |
|   └ vipFee | int32 | 否 | - | 会费，单位：分 | 0 |
|   └ startTime | int64 | 否 | - | 有效期开始时间 | 0 |
|   └ endTime | int64 | 否 | - | 有效期结束时间 | 0 |
|   └ effective | boolean | 否 | - | 是否有效 true：有效，false：无效 | true |
|   └ displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
|   └ canUserCurrentCinema | boolean | 否 | - | 是否支持当前影院使用 | true |
|   └ sort | int32 | 否 | - | 推荐排序 | 0 |
|   └ status | int32 | 否 | - | 卡政策状态: 下架0 上架1 草稿2 | 0 |
|   └ cardStatus | int32 | 否 | - | 卡状态：初始化-1 未激活 0 正常1 冻结2 过期3 作废 4 | 0 |
|   └ acquisitionMethod | int32 | 否 | - | 获得方式：云智老卡0 个人购买电子卡1 | 0 |
|   └ cardCreationDate | int64 | 否 | - | 制卡日期 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | id |  |
|   └ internalVipCardId | string | 否 | - | 内部会员卡id |  |
|   └ batchId | string | 否 | - | 批次号 生成规则8位数字 自增 |  |
|   └ cardNumber | string | 否 | - | 内部会员卡卡号 |  |
|   └ name | string | 否 | - | 影城卡名称 |  |
|   └ cardType | string | 否 | - | 影城卡类型 |  |
|   └ cardTypeName | string | 否 | - | 影城卡类型名称 |  |
|   └ cinemaId | int64 | 否 | - | 影城id | 0 |
|   └ cinemaName | string | 否 | - | 影城名称 |  |
|   └ userId | int64 | 否 | - | 用户id | 0 |
|   └ nickName | string | 否 | - | 会员名称 |  |
|   └ phoneNumber | string | 否 | - | 手机号码 |  |
|   └ cardAmount | int32 | 否 | - | 卡内余额 | 0 |
|   └ vipFee | int32 | 否 | - | 会费，单位：分 | 0 |
|   └ startTime | int64 | 否 | - | 有效期开始时间 | 0 |
|   └ endTime | int64 | 否 | - | 有效期结束时间 | 0 |
|   └ effective | boolean | 否 | - | 是否有效 true：有效，false：无效 | true |
|   └ displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
|   └ canUserCurrentCinema | boolean | 否 | - | 是否支持当前影院使用 | true |
|   └ sort | int32 | 否 | - | 推荐排序 | 0 |
|   └ status | int32 | 否 | - | 卡政策状态: 下架0 上架1 草稿2 | 0 |
|   └ cardStatus | int32 | 否 | - | 卡状态：初始化-1 未激活 0 正常1 冻结2 过期3 作废 4 | 0 |
|   └ acquisitionMethod | int32 | 否 | - | 获得方式：云智老卡0 个人购买电子卡1 | 0 |
|   └ cardCreationDate | int64 | 否 | - | 制卡日期 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "internalVipCardId": "",
            "batchId": "",
            "cardNumber": "",
            "name": "",
            "cardType": "",
            "cardTypeName": "",
            "cinemaId": 0,
            "cinemaName": "",
            "userId": 0,
            "nickName": "",
            "phoneNumber": "",
            "cardAmount": 0,
            "vipFee": 0,
            "startTime": 0,
            "endTime": 0,
            "effective": true,
            "displayDiscountPrice": 0,
            "canUserCurrentCinema": true,
            "sort": 0,
            "status": 0,
            "cardStatus": 0,
            "acquisitionMethod": 0,
            "cardCreationDate": 0
        }
    ],
    "result": [
        {
            "id": "",
            "internalVipCardId": "",
            "batchId": "",
            "cardNumber": "",
            "name": "",
            "cardType": "",
            "cardTypeName": "",
            "cinemaId": 0,
            "cinemaName": "",
            "userId": 0,
            "nickName": "",
            "phoneNumber": "",
            "cardAmount": 0,
            "vipFee": 0,
            "startTime": 0,
            "endTime": 0,
            "effective": true,
            "displayDiscountPrice": 0,
            "canUserCurrentCinema": true,
            "sort": 0,
            "status": 0,
            "cardStatus": 0,
            "acquisitionMethod": 0,
            "cardCreationDate": 0
        }
    ]
}
```

#### 错误码

无

### 会员卡列表
接口权限：/market/vipCardUser/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardUser/list


描述：会员卡列表
接口权限：/market/vipCardUser/query

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | id |  |
| internalVipCardId | string | 否 | - | 内部会员卡id |  |
| batchId | string | 否 | - | 批次号 生成规则8位数字 自增 |  |
| cardNumber | string | 否 | - | 内部会员卡卡号 |  |
| name | string | 否 | - | 影城卡名称 |  |
| cardType | string | 否 | - | 影城卡类型 |  |
| cardTypeName | string | 否 | - | 影城卡类型名称 |  |
| cinemaId | int64 | 否 | - | 影城id | 0 |
| cinemaName | string | 否 | - | 影城名称 |  |
| userId | int64 | 否 | - | 用户id | 0 |
| nickName | string | 否 | - | 会员名称 |  |
| phoneNumber | string | 否 | - | 手机号码 |  |
| cardAmount | int32 | 否 | - | 卡内余额 | 0 |
| vipFee | int32 | 否 | - | 会费，单位：分 | 0 |
| startTime | int64 | 否 | - | 有效期开始时间 | 0 |
| endTime | int64 | 否 | - | 有效期结束时间 | 0 |
| effective | boolean | 否 | - | 是否有效 true：有效，false：无效 | true |
| displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
| canUserCurrentCinema | boolean | 否 | - | 是否支持当前影院使用 | true |
| sort | int32 | 否 | - | 推荐排序 | 0 |
| status | int32 | 否 | - | 卡政策状态: 下架0 上架1 草稿2 | 0 |
| cardStatus | int32 | 否 | - | 卡状态：初始化-1 未激活 0 正常1 冻结2 过期3 作废 4 | 0 |
| acquisitionMethod | int32 | 否 | - | 获得方式：云智老卡0 个人购买电子卡1 | 0 |
| cardCreationDate | int64 | 否 | - | 制卡日期 | 0 |

#### 响应示例

```
{
    "id": "",
    "internalVipCardId": "",
    "batchId": "",
    "cardNumber": "",
    "name": "",
    "cardType": "",
    "cardTypeName": "",
    "cinemaId": 0,
    "cinemaName": "",
    "userId": 0,
    "nickName": "",
    "phoneNumber": "",
    "cardAmount": 0,
    "vipFee": 0,
    "startTime": 0,
    "endTime": 0,
    "effective": true,
    "displayDiscountPrice": 0,
    "canUserCurrentCinema": true,
    "sort": 0,
    "status": 0,
    "cardStatus": 0,
    "acquisitionMethod": 0,
    "cardCreationDate": 0
}
```

#### 错误码

无

### 查看会员卡详情
接口权限：/market/vipCardUser/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardUser/detail


描述：查看会员卡详情
接口权限：/market/vipCardUser/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cardId | string | 否 | - | 卡id |  |

#### 请求示例

```
{
    "cardId": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | id |  |
| internalVipCardId | string | 否 | - | 内部会员卡id |  |
| batchId | string | 否 | - | 批次号 生成规则8位数字 自增 |  |
| cardNumber | string | 否 | - | 内部会员卡卡号 |  |
| name | string | 否 | - | 影城卡名称 |  |
| cardType | string | 否 | - | 影城卡类型 |  |
| cardTypeName | string | 否 | - | 影城卡类型名称 |  |
| cinemaId | int64 | 否 | - | 影城id | 0 |
| cinemaName | string | 否 | - | 影城名称 |  |
| userId | int64 | 否 | - | 用户id | 0 |
| nickName | string | 否 | - | 会员名称 |  |
| phoneNumber | string | 否 | - | 手机号码 |  |
| cardAmount | int32 | 否 | - | 卡内余额 | 0 |
| vipFee | int32 | 否 | - | 会费，单位：分 | 0 |
| startTime | int64 | 否 | - | 有效期开始时间 | 0 |
| endTime | int64 | 否 | - | 有效期结束时间 | 0 |
| effective | boolean | 否 | - | 是否有效 true：有效，false：无效 | true |
| displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
| canUserCurrentCinema | boolean | 否 | - | 是否支持当前影院使用 | true |
| sort | int32 | 否 | - | 推荐排序 | 0 |
| status | int32 | 否 | - | 卡政策状态: 下架0 上架1 草稿2 | 0 |
| cardStatus | int32 | 否 | - | 卡状态：初始化-1 未激活 0 正常1 冻结2 过期3 作废 4 | 0 |
| acquisitionMethod | int32 | 否 | - | 获得方式：云智老卡0 个人购买电子卡1 | 0 |
| cardCreationDate | int64 | 否 | - | 制卡日期 | 0 |

#### 响应示例

```
{
    "id": "",
    "internalVipCardId": "",
    "batchId": "",
    "cardNumber": "",
    "name": "",
    "cardType": "",
    "cardTypeName": "",
    "cinemaId": 0,
    "cinemaName": "",
    "userId": 0,
    "nickName": "",
    "phoneNumber": "",
    "cardAmount": 0,
    "vipFee": 0,
    "startTime": 0,
    "endTime": 0,
    "effective": true,
    "displayDiscountPrice": 0,
    "canUserCurrentCinema": true,
    "sort": 0,
    "status": 0,
    "cardStatus": 0,
    "acquisitionMethod": 0,
    "cardCreationDate": 0
}
```

#### 错误码

无

### 修改会员卡
接口权限：/data/vipCardUser/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardUser/edit


描述：修改会员卡
接口权限：/data/vipCardUser/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | id |  |
| internalVipCardId | string | 否 | - | 内部会员卡id |  |
| batchId | string | 否 | - | 批次号 生成规则8位数字 自增 |  |
| cardNumber | string | 否 | - | 内部会员卡卡号 |  |
| name | string | 否 | - | 影城卡名称 |  |
| cardType | string | 否 | - | 影城卡类型 |  |
| cardTypeName | string | 否 | - | 影城卡类型名称 |  |
| cinemaId | int64 | 否 | - | 影城id | 0 |
| cinemaName | string | 否 | - | 影城名称 |  |
| userId | int64 | 否 | - | 用户id | 0 |
| nickName | string | 否 | - | 会员名称 |  |
| phoneNumber | string | 否 | - | 手机号码 |  |
| cardAmount | int32 | 否 | - | 卡内余额 | 0 |
| vipFee | int32 | 否 | - | 会费，单位：分 | 0 |
| startTime | int64 | 否 | - | 有效期开始时间 | 0 |
| endTime | int64 | 否 | - | 有效期结束时间 | 0 |
| effective | boolean | 否 | - | 是否有效 true：有效，false：无效 | true |
| displayDiscountPrice | int32 | 否 | - | 是否显示优惠价：不显示0 显示1 | 0 |
| canUserCurrentCinema | boolean | 否 | - | 是否支持当前影院使用 | true |
| sort | int32 | 否 | - | 推荐排序 | 0 |
| status | int32 | 否 | - | 卡政策状态: 下架0 上架1 草稿2 | 0 |
| cardStatus | int32 | 否 | - | 卡状态：初始化-1 未激活 0 正常1 冻结2 过期3 作废 4 | 0 |
| acquisitionMethod | int32 | 否 | - | 获得方式：云智老卡0 个人购买电子卡1 | 0 |
| cardCreationDate | int64 | 否 | - | 制卡日期 | 0 |

#### 请求示例

```
{
    "id": "",
    "internalVipCardId": "",
    "batchId": "",
    "cardNumber": "",
    "name": "",
    "cardType": "",
    "cardTypeName": "",
    "cinemaId": 0,
    "cinemaName": "",
    "userId": 0,
    "nickName": "",
    "phoneNumber": "",
    "cardAmount": 0,
    "vipFee": 0,
    "startTime": 0,
    "endTime": 0,
    "effective": true,
    "displayDiscountPrice": 0,
    "canUserCurrentCinema": true,
    "sort": 0,
    "status": 0,
    "cardStatus": 0,
    "acquisitionMethod": 0,
    "cardCreationDate": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 修改会员卡
接口权限：/data/vipCardUser/edit

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/market/vipCardUser/editStatus


描述：修改会员卡
接口权限：/data/vipCardUser/edit

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cardId | string | 否 | - | 卡id |  |
| status | int32 | 否 | - | 卡状态 | 0 |

#### 请求示例

```
{
    "cardId": "",
    "status": 0
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

## API接口导出说明

本模块提供了完整的会员卡相关API接口，包括三个主要功能模块：

1. 卡赠礼管理 (VipCardActivity)
   - 列表查询（分页/全部）
   - 详情查看
   - 编辑操作
   - 状态修改

2. 卡政策管理 (VipCardPolicy)
   - 列表查询（分页/全部）
   - 详情查看
   - 编辑操作
   - 状态修改

3. 会员卡管理 (VipCardUser)
   - 列表查询（分页/全部）
   - 详情查看
   - 编辑操作
   - 状态修改

所有接口均已按照文档要求实现，可直接在项目中导入使用。