import api from '../../index.ts'

/**
 * 企业管理相关API
 */

export interface Index {
  id?: string
  name: string
  province: string
  provinceCode: string
  city: string
  cityCode: string
  available?: boolean
}

export interface CompanyListParams {
  page?: number
  size?: number
  name?: string
}

export interface CompanyListResponse {
  total: number
  content: Index[]
  result: Index[]
}

/**
 * 获取企业列表
 */
function getCompanyList(params: CompanyListParams) {
  return api.post<CompanyListResponse>('/adm_company/search', params)
}

/**
 * 保存企业（添加/编辑）
 */
function saveCompany(data: Index) {
  return api.post('/adm_company/save', data)
}

/**
 * 获取企业详情
 */
function getCompanyDetail(id: string) {
  return api.get<Index>(`/adm_company/${id}`)
}

/**
 * 删除企业
 */
function deleteCompany(id: string) {
  return api.delete(`/adm_company/${id}`)
}

/**
 * 切换企业状态
 */
function toggleCompanyStatus(id: string, available: boolean) {
  return api.put(`/adm_company/${id}/status`, { available })
}

export default {
  getCompanyList,
  saveCompany,
  getCompanyDetail,
  deleteCompany,
  toggleCompanyStatus,
}
