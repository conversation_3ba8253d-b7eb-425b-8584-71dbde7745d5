import api from '../../index.ts'

export default {
  // 菜单管理接口
  // 获取菜单列表
  getMenuList: (params: {
    page?: number
    size?: number
    parentId?: string
  }) => api.post('/adm_menu/list', params, {
  }),

  // 获取所有菜单列表（用于角色权限分配）
  getMenuListAll: () => api.post('/adm_menu/listAll', {}, {
  }),

  // 编辑菜单
  editMenu: (data: {
    id: string
    name: string
  }) => api.post('/adm_menu/edit', data, {
  }),

  // 删除菜单
  deleteMenu: (data: {
    id: string
  }) => api.post('/adm_menu/delete', data, {
  }),

  // 角色管理接口
  role: {
    // 获取角色列表
    getRoleList: (params: {
      page?: number
      size?: number
    }) => api.post('/adm_role/list', params, {
    }),

    // 获取角色详情
    getRoleDetail: (data: {
      id: string
    }) => api.post('/adm_role/getDetailById', data, {
    }),

    // 新增/编辑角色
    saveRole: (data: {
      id?: string
      name: string
      menuIds?: string[]
    }) => api.post('/adm_role/save', data, {
    }),

    // 删除角色
    deleteRole: (data: {
      id: string
    }) => api.post('/adm_role/delete', data, {
    }),
  },

  // 用户管理接口
  // 获取用户列表
  getUserList: (params: {
    page?: number
    size?: number
    partmentId?: string
    name?: string
    account?: string
    available?: boolean
  }) => api.post('/adm_account/list', params, {
  }),

  // 新增/编辑用户
  saveUser: (data: {
    id?: string
    name: string
    account: string
    roleIds?: string[]
    partmentId?: string
    available?: boolean
  }) => api.post('/adm_account/save', data, {
  }),

  // 删除用户
  deleteUser: (data: {
    id: string
  }) => api.post('/adm_account/delete', data, {
  }),

  // 重置用户密码
  resetUserPassword: (data: {
    id: string
    password: string
  }) => api.post('/adm_account/resetPassword', data, {
  }),

  // 获取当前用户信息
  getMyInfo: () => api.post('/adm_account/getMyInfo', {}, {
  }),

  // 部门管理接口
  // 获取部门列表
  getDepartmentList: (params: {
    page?: number
    size?: number
    parentId?: string
    name?: string
  }) => api.post('/adm_partment/search', params, {
  }),

  // 新增部门
  addDepartment: (data: {
    name: string
    parentId?: string
    sort?: number
  }) => api.post('/adm_partment/add', data, {
  }),

  // 编辑部门
  editDepartment: (data: {
    id: string
    name: string
    parentId?: string
    sort?: number
  }) => api.post('/adm_partment/edit', data, {
  }),

  // 删除部门
  deleteDepartment: (data: {
    id: string
  }) => api.post('/adm_partment/delete', data, {
  }),

  // 字典管理接口
  // 获取字典类型列表
  getDictTypeList: (params: {
    page?: number
    size?: number
    dictType?: string
    dictName?: string
    status?: number
  }) => api.post('/adm_dict_type/list', params, {
  }),

  // 新增字典类型
  addDictType: (data: {
    dictType: string
    dictName: string
    description?: string
    status?: number
    sort?: number
  }) => api.post('/adm_dict_type/add', data, {
  }),

  // 编辑字典类型
  editDictType: (data: {
    id: string
    dictType: string
    dictName: string
    description?: string
    status?: number
    sort?: number
  }) => api.post('/adm_dict_type/edit', data, {
  }),

  // 删除字典类型
  deleteDictType: (data: {
    id: string
  }) => api.post('/adm_dict_type/delete', data, {
  }),

  // 获取字典项列表
  getDictItemList: (params: {
    page?: number
    size?: number
    dictType: string
    dictLabel?: string
    status?: number
  }) => api.post('/adm_dict_item/list', params, {
  }),

  // 新增字典项
  addDictItem: (data: {
    dictType: string
    dictLabel: string
    dictValue: string
    description?: string
    status?: number
    sort?: number
  }) => api.post('/adm_dict_item/add', data, {
  }),

  // 编辑字典项
  editDictItem: (data: {
    id: string
    dictType: string
    dictLabel: string
    dictValue: string
    description?: string
    status?: number
    sort?: number
  }) => api.post('/adm_dict_item/edit', data, {
  }),

  // 删除字典项
  deleteDictItem: (data: {
    id: string
  }) => api.post('/adm_dict_item/delete', data, {
  }),

  // 根据字典类型获取字典项（用于下拉选择）
  getDictItemsByType: (params: {
    dictType: string
    status?: number
  }) => api.post('/adm_dict_item/getByType', params, {
  }),
}
