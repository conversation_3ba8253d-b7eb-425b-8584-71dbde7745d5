
### 查看影院详情
接口权限：/data/cinema/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/data/cinema/detail


描述：查看影院详情
接口权限：/data/cinema/query

ContentType：`application/json`

#### 请求头

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| adminToken | 是 |  | wec76PvDb8fjXUrnRQon9e77r1BuoGsMyRiVsghCBZ4SjbkvojTNcWWMLYuSDgco9InJMOhy74+JcUhVLIp+KEjaz9840jtRV6pl6KqYUlKFA0Eldp1AlRKTywKhBBm3/fBjaAIhbf9MetK4FTGgn//7vvqB+Wbd8xJd/OKfWDkK/y4CXZivPDf5Op1O2rFF |
| accessToken | 是 |  | 7znHgC63zNtn+i4wiCjqc05m/XlmSUL5pdCmK/OvVQEfXJDjpckvmKiCJZNXJhJqZK1bmceSalUMmoPCFchlC+SZ2yIo0DCob+1UIi1mo4iI42juRyhY+qFrZbw9x6KIass57++T+2+NVWweBFKpIF28BemShYxg+ax5uCABYR/Cs824TA2qPPZSlZbS83VM |

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 影院id |  |

#### 请求示例

```
{
    "id": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | No comments found. |  |
| cid | string | 否 | - | 关联id |  |
| code | string | 否 | - | 影院编码 |  |
| name | string | 否 | - | 影院名称 |  |
| province | string | 否 | - | 影院所在省 |  |
| city | string | 否 | - | 影院所在城市 |  |
| createDate | int64 | 否 | - | 电影院授权日期 | 0 |
| priceType | int32 | 否 | - | 定价模式，0-影院定价，1-合作商定价 | 0 |
| address | string | 否 | - | 影院地址 |  |
| screenCount | int32 | 否 | - | 影厅数量 | 0 |
| screens | array | 否 |  | 影厅 |  |
|   └ id | string | 否 | - | id |  |
|   └ cid | string | 否 | - | 关联id |  |
|   └ code | string | 否 | - | 影厅编码 |  |
|   └ name | string | 否 | - | 影厅名称 |  |
|   └ seatCount | int32 | 否 | - | 影厅数量 | 0 |
|   └ type | string | 否 | - | 影厅类型：Normal，普通影厅<br>          3D，3D 影厅<br>          MAX，巨幕影厅<br>          MAX3D， 3D 巨幕影厅 |  |
| maxBuyCount | int32 | 否 | - | 最大购买数量 | 0 |
| goods | array | 否 |  | 商品 |  |
|   └ code | string | 否 | - | 影院卖品编码 |  |
|   └ goodsKey | string | 否 | - | 卖品信息的加密校验串<br>根据卖品的基础信息计算MD5<br>码，在确认订单时由第三⽅回<br>传，⽤来校验数据是否过期。 |  |
|   └ packageFlag | string | 否 | - | 是否套餐的标志 Y / N |  |
|   └ goodsPicUrl | string | 否 | - | 图⽚链接 |  |
|   └ settlePrice | int64 | 否 | - | 卖品第三⽅结算价，单位（分） | 0 |
|   └ standardPrice | int64 | 否 | - | 标准价，单位（分） | 0 |
|   └ name | string | 否 | - | 卖品名称 |  |
|   └ desc | string | 否 | - | 卖品描述<br>单品：商品描述信息<br>套餐：套餐的配⽅描述信息 |  |
|   └ provider | string | 否 | - | 服务供应商 |  |
| version | int32 | 否 | - | 系统版本<br>1、UsbKey13规范影院<br>2、CA证书23规范影院（上报票房与座位图需要后面增加行列号、座位编码保持20位） | 0 |
| ctime | int64 | 否 | - | 创建时间 | 0 |
| uptime | int64 | 否 | - | 更新时间 | 0 |
| syncStatus | int64 | 否 | - | 状态 1正常 0被隔离 | 0 |
| qrCodeUrl | string | 否 | - | 影院拉新二维码 |  |
| provider | string | 否 | - | No comments found. |  |

#### 响应示例

```
{
    "id": "",
    "cid": "",
    "code": "",
    "name": "",
    "province": "",
    "city": "",
    "createDate": 0,
    "priceType": 0,
    "address": "",
    "screenCount": 0,
    "screens": [
        {
            "id": "",
            "cid": "",
            "code": "",
            "name": "",
            "seatCount": 0,
            "type": ""
        }
    ],
    "maxBuyCount": 0,
    "goods": [
        {
            "code": "",
            "goodsKey": "",
            "packageFlag": "",
            "goodsPicUrl": "",
            "settlePrice": 0,
            "standardPrice": 0,
            "name": "",
            "desc": "",
            "provider": ""
        }
    ],
    "version": 0,
    "ctime": 0,
    "uptime": 0,
    "syncStatus": 0,
    "qrCodeUrl": "",
    "provider": ""
}
```

#### 错误码

无
