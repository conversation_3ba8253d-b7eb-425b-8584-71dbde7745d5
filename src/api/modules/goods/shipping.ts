/**
 * 运费模板管理API服务
 * 基于接口文档实现运费模板相关的API调用
 */

import api from '@/api'
import type {
  ShippingTemplateSearchParams,
  ShippingTemplateParams,
  ShippingTemplateListResponse,
  ShippingTemplateDetailResponse,
  ShippingTemplate,
} from '@/views/goods/types'

// ==================== 运费模板管理接口 ====================

/**
 * 创建/编辑运费模板
 * @param params 运费模板参数
 * @returns 操作结果
 */
export function createOrUpdateShippingTemplate(params: ShippingTemplateParams) {
  return api.post('/adm_product/createShippingFeeTpl', params)
}

/**
 * 搜索运费模板
 * @param params 搜索参数
 * @returns 运费模板列表响应
 */
export function searchShippingTemplates(params: ShippingTemplateSearchParams) {
  return api.post<ShippingTemplateListResponse>('/adm_product/searchShippingFeeTpl', params)
}

/**
 * 获取所有运费模板
 * @returns 运费模板列表
 */
export function getAllShippingTemplates() {
  return api.post<ShippingTemplate[]>('/adm_product/getAllShippingFeeTpl')
}

/**
 * 获取运费模板详情
 * @param id 模板ID
 * @returns 运费模板详情响应
 */
export function getShippingTemplateDetail(id: string) {
  return api.post<ShippingTemplateDetailResponse>(`/adm_product/getShippingFeeTplDetail/${id}`)
}

/**
 * 删除运费模板
 * @param id 模板ID
 * @returns 删除结果
 */
export function deleteShippingTemplate(id: string) {
  return api.post(`/adm_product/deleteShippingFeeTpl/${id}`)
}

// ==================== 导出默认对象 ====================

export default {
  createOrUpdateShippingTemplate,
  searchShippingTemplates,
  getAllShippingTemplates,
  getShippingTemplateDetail,
  deleteShippingTemplate,
}
