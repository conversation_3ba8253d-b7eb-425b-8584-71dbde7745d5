/**
 * 商品管理API服务
 * 基于接口文档实现所有商品相关的API调用
 */

import type {
  BatchUpdateProductStatusParams,
  CreateProductParams,
  ProductDetailResponse,
  ProductListResponse,
  UpdateProductParams,
  UpdateProductStatusParams,
} from '@/views/goods/types'
import api from '@/api'

// ==================== 商品管理接口 ====================

export function getGoodsTypeAll() {
  // return api.post('/adm_product/getAllProductType')
  return Promise.resolve({
    code: 0,
    data: [
      { label: '实物商品', value: 1 },
      { label: '虚拟商品', value: 2 },
      { label: '服务商品', value: 3 },
    ],
  })
}
/**
 * 获取商品列表
 * @param params 查询参数
 * @returns 商品列表响应
 */
export function getProductList(params: any) {
  return api.post<ProductListResponse>('/adm_product/list', params)
}

/**
 * 搜索商品
 * @param params 搜索参数
 * @returns 商品列表响应
 */
export function searchProducts(params: any) {
  return api.post<ProductListResponse>('/adm_product/search', params)
}

/**
 * 根据ID获取商品详情
 * @param id 商品ID
 * @returns 商品详情响应
 */
export function getProductDetail(id: string) {
  return api.post<ProductDetailResponse>(`/adm_product/detail/${id}`)
}

/**
 * 新增商品
 * @param params 商品创建参数
 * @returns 创建结果
 */
export function createProduct(params: CreateProductParams) {
  return api.post('/adm_product/add', params)
}

/**
 * 更新商品
 * @param params 商品更新参数
 * @returns 更新结果
 */
export function updateProduct(params: UpdateProductParams) {
  return api.post('/adm_product/update', params)
}

/**
 * 更新商品状态
 * @param params 状态更新参数
 * @returns 更新结果
 */
export function updateProductStatus(params: UpdateProductStatusParams) {
  return api.post('/adm_product/updateStatus', params)
}

/**
 * 批量更新商品状态
 * @param params 批量状态更新参数
 * @returns 更新结果
 */
export function batchUpdateProductStatus(params: BatchUpdateProductStatusParams) {
  return api.post('/adm_product/batchUpdateStatus', params)
}

/**
 * 删除商品
 * @param id 商品ID
 * @returns 删除结果
 */
export function deleteProduct(id: string) {
  return api.post(`/adm_product/delete/${id}`)
}

/**
 * 根据分类ID统计商品数量
 * @param categoryId 分类ID
 * @returns 商品数量
 */
export function countProductsByCategory(categoryId: string) {
  return api.post<number>(`/adm_product/countByCategory/${categoryId}`)
}

// ==================== 导出默认对象 ====================

export default {
  getProductList,
  searchProducts,
  getProductDetail,
  createProduct,
  updateProduct,
  updateProductStatus,
  batchUpdateProductStatus,
  deleteProduct,
  countProductsByCategory,
}
