/**
 * 订单管理API服务
 * 基于接口文档实现订单相关的API调用
 */

import api from '@/api'
import type {
  OrderListParams,
  OrderListResponse,
  OrderDetailResponse,
  ShipOrderParams,
  BaseResponse,
} from '@/views/orders/types'

// ==================== 订单管理接口 ====================

/**
 * 获取订单列表
 * @param params 查询参数
 * @returns 订单列表响应
 */
export function getOrderList(params: OrderListParams) {
  return api.post<BaseResponse<OrderListResponse>>('/adm_order/list', params)
}

/**
 * 获取订单详情
 * @param id 订单ID
 * @returns 订单详情响应
 */
export function getOrderDetail(id: string) {
  return api.post<BaseResponse<OrderDetailResponse>>(`/adm_order/detail/${id}`, {}, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    },
  })
}

/**
 * 订单发货
 * @param params 发货参数
 * @returns 操作结果
 */
export function shipOrder(params: ShipOrderParams) {
  return api.post<BaseResponse<void>>('/adm_order/ship', params)
}

// ==================== 导出所有API ====================
export default {
  getOrderList,
  getOrderDetail,
  shipOrder,
}

// 重新导出类型定义
export type * from '@/views/orders/types'
