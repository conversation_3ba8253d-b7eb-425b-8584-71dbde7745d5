// 模拟渠道数据
export const channelOptions = [
  {
    label: '微信小程序',
    value: 'wxapp',
    description: '微信小程序',
  },
  {
    label: '支付宝小程序',
    value: 'alipay',
    description: '支付宝小程序',
  },
  {
    label: '抖音小程序',
    value: 'douyin',
    description: '抖音小程序',
  },
  {
    label: 'B站小程序',
    value: 'bilibili',
    description: 'B站小程序',
  },
  {
    label: 'QQ小程序',
    value: 'qq',
    description: 'QQ小程序',
  },
]
// 票务系统数据
export const ticketSystemOptions = [
  {
    label: '辰星 3.0',
    value: 'CxV3',
    description: '辰星 3.0',
  },
  {
    label: '辰星 4.0',
    value: 'CxV4',
    description: '辰星 4.0',
  },
  {
    label: '鼎新',
    value: 'DingXin',
    description: '鼎新',
  },
  {
    label: '凤凰佳影',
    value: 'PhoV2',
    description: '凤凰佳影',
  },
  {
    label: '凤凰云智',
    value: 'PhoV3',
    description: '凤凰云智',
  },
]

// 影片版本数据
export const filmVersionList = [
  {
    name: '2D',
    value: '2D',
  },
  {
    name: '3D',
    value: '3D',
  },
  {
    name: '4D',
    value: '4D',
  },
  {
    name: 'IMAX-2D',
    value: 'IMAX2D',
  },
  {
    name: 'IMAX-3D',
    value: 'IMAX3D',
  },
  {
    name: '中国巨幕-2D',
    value: 'DMAX2D',
  },
  {
    name: '中国巨幕-3D',
    value: 'DMAX3D',
  },
]

// 影院影厅类型数据
export const cinemaHallTypeList = [
  {
    name: 'ScreenX厅',
    value: 'S',
  },
  {
    name: '激光巨幕厅',
    value: 'JH',
  },
  {
    name: '4DX厅',
    value: 'FDX',
  },
  {
    name: '双机厅',
    value: 'SJT',
  },
  {
    name: 'LUXE厅',
    value: 'LUXE',
  },
  {
    name: 'REALD厅',
    value: 'R',
  },
  {
    name: '激光IMAX厅',
    value: 'JGI',
  },
  {
    name: '中国巨幕厅',
    value: 'CGS',
  },
  {
    name: 'HOLOSOUND厅',
    value: 'HO',
  },
  {
    name: 'BONA ONE',
    value: 'BO',
  },
  {
    name: '超级贵宾厅',
    value: 'VV',
  },
  {
    name: 'MINI贵宾厅',
    value: 'M',
  },
  {
    name: '亲子厅',
    value: 'Q',
  },
  {
    name: 'ONYX LED厅',
    value: 'O',
  },
  {
    name: '普通厅',
    value: 'N',
  },
  {
    name: '情侣厅',
    value: 'T',
  },
  {
    name: 'D-BOX厅',
    value: 'B',
  },
  {
    name: '临境音厅',
    value: 'X',
  },
  {
    name: '4D厅',
    value: 'F',
  },
  {
    name: '杜比影院厅',
    value: 'C',
  },
  {
    name: '巨幕厅',
    value: 'H',
  },
  {
    name: 'HeyLED厅',
    value: 'HLED',
  },
  {
    name: 'IMAX厅',
    value: 'I',
  },
  {
    name: 'CINITY厅',
    value: 'CI',
  },
  {
    name: '杜比全景声厅',
    value: 'D',
  },
  {
    name: '激光厅',
    value: 'L',
  },
  {
    name: '贵宾厅',
    value: 'V',
  },
]

export function findDataByValue(list: { name: string, value: string }[], value: string) {
  return list.find(item => item.value === value)
}
