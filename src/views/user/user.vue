<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import type { Company, Index, UserListParams } from '@/api/modules/user'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import md5 from 'js-md5'
import { onMounted, reactive, ref } from 'vue'
import companyApi from '@/api/modules/company'

import userApi from '@/api/modules/user'

console.log({ companyApi })

// 响应式数据
const loading = ref(false)
const userList = ref<Index[]>([])
const companyList = ref<Company[]>([])
const dateRange = ref<number[]>([])

// 搜索表单
const searchForm = reactive({
  account: '',
  name: '',
  referralName: '',
  companyId: '',
  timeSection: {
    begin: null,
    end: null,
  },
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 绑定企业对话框
const bindDialogVisible = ref(false)
const bindForm = reactive({
  userId: '',
  companyId: '',
})

// 重置密码对话框
const resetDialogVisible = ref(false)
const resetFormRef = ref<FormInstance>()
const resetForm = reactive({
  userId: '',
  password: '',
  confirmPassword: '',
})

const resetRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== resetForm.password) {
          callback(new Error('两次输入密码不一致'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 用户详情对话框
const detailDialogVisible = ref(false)
const currentUser = ref<Index>({} as Index)

// 方法
function formatDate(timestamp: number) {
  if (!timestamp) { return '-' }
  // return new Date(timestamp).toLocaleString('zh-CN')
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

async function loadUserList() {
  loading.value = true
  try {
    const params: UserListParams = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm,
    }
    const res = await userApi.getUserList(params)
    const { data } = res
    userList.value = data.content || []
    pagination.total = data.total || 0
  }
  catch (error) {
    ElMessage.error('获取用户列表失败')
  }
  finally {
    loading.value = false
  }
}

async function loadCompanyList() {
  try {
    const res = await companyApi.getCompanyList({
      page: 0,
      size: 1000,
    })
    companyList.value = res.data.content || []
  }
  catch (error) {
    ElMessage.error('获取企业列表失败')
  }
}

function handleSearch() {
  pagination.page = 1
  loadUserList()
}

function handleReset() {
  Object.assign(searchForm, {
    account: '',
    name: '',
    referralName: '',
    companyId: '',
    timeSection: { begin: 0, end: 0 },
  })
  dateRange.value = []
  handleSearch()
}

function handleDateChange(dates: number[]) {
  if (dates && dates.length === 2) {
    searchForm.timeSection.begin = dates[0]
    searchForm.timeSection.end = dates[1]
  }
  else {
    // searchForm.timeSection.begin = null
    // searchForm.timeSection.end = null
  }
}

function handleSizeChange(size: number) {
  pagination.size = size
  loadUserList()
}

function handleCurrentChange(page: number) {
  pagination.page = page
  loadUserList()
}

function handleBindCompany(row: Index) {
  bindForm.userId = row.id
  bindForm.companyId = ''
  bindDialogVisible.value = true
}

async function handleBindSubmit() {
  if (!bindForm.companyId) {
    ElMessage.warning('请选择要绑定的企业')
    return
  }

  try {
    await userApi.bindCompany({
      ids: [bindForm.userId],
      companyId: bindForm.companyId,
    })
    ElMessage.success('绑定成功')
    bindDialogVisible.value = false
    loadUserList()
  }
  catch (error) {
    ElMessage.error('绑定失败')
  }
}

function handleResetPassword(row: Index) {
  resetForm.userId = row.id
  resetForm.password = ''
  resetForm.confirmPassword = ''
  resetDialogVisible.value = true
}

async function handleResetSubmit() {
  if (!resetFormRef.value) { return }

  await resetFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await userApi.resetPassword({
          id: resetForm.userId,
          password: md5(resetForm.password),
        })
        ElMessage.success('密码重置成功')
        resetDialogVisible.value = false
      }
      catch (error) {
        ElMessage.error('密码重置失败')
      }
    }
  })
}

async function handleViewDetail(row: Index) {
  try {
    const res = await userApi.getUserDetail(row.id)
    currentUser.value = res.data
    detailDialogVisible.value = true
  }
  catch (error) {
    ElMessage.error('获取用户详情失败')
  }
}

// 初始化
onMounted(() => {
  loadUserList()
  loadCompanyList()
})
</script>

<template>
  <fa-page-header title="用户管理" description="用户列表" />
  <fa-page-main>
    <!-- 搜索栏 -->
    <el-form :model="searchForm" inline>
      <el-form-item label="手机号/邮箱">
        <el-input v-model="searchForm.account" placeholder="请输入手机号或邮箱" clearable />
      </el-form-item>
      <el-form-item label="用户名">
        <el-input v-model="searchForm.name" placeholder="请输入用户名" clearable />
      </el-form-item>
      <el-form-item label="推广源">
        <el-input v-model="searchForm.referralName" placeholder="请输入推广源" clearable />
      </el-form-item>
      <el-form-item label="绑定企业">
        <el-select v-model="searchForm.companyId" placeholder="请选择企业" clearable filterable>
          <el-option
            v-for="company in companyList"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="注册时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="x"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          搜索
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 用户列表 -->
    <el-table v-loading="loading" :data="userList" border>
      <el-table-column prop="account" label="账号" min-width="120" />
      <el-table-column prop="name" label="用户名" min-width="100" />
      <el-table-column label="头像" width="80">
        <template #default="{ row }">
          <el-avatar :src="row.headImage" :size="40" />
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="性别" width="60">
        <template #default="{ row }">
          {{ row.gender === 1 ? '男' : row.gender === 2 ? '女' : '未知' }}
        </template>
      </el-table-column>
      <el-table-column prop="companys" label="所属企业" min-width="200">
        <template #default="{ row }">
          <el-tag
            v-for="company in row.companys"
            :key="company.id"
            type="info"
            size="small"
            class="mr-1"
          >
            {{ company.name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="banlance" label="钱包余额" width="100">
        <template #default="{ row }">
          ¥{{ (row.banlance || 0) / 100 }}
        </template>
      </el-table-column>
      <el-table-column prop="coupons" label="优惠券" width="80" />
      <el-table-column prop="benefitCards" label="光影卡" width="80" />
      <el-table-column prop="vipCardBalance" label="会员卡余额" width="100">
        <template #default="{ row }">
          ¥{{ (row.vipCardBalance || 0) / 100 }}
        </template>
      </el-table-column>
      <el-table-column prop="ctime" label="注册时间" width="160">
        <template #default="{ row }">
          {{ formatDate(row.ctime) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastActiveDay" label="最后活跃" width="160">
        <template #default="{ row }">
          {{ formatDate(row.lastActiveDay) }}
        </template>
      </el-table-column>
      <el-table-column prop="note" label="备注" min-width="150" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleBindCompany(row)">
            绑定企业
          </el-button>
          <el-button type="warning" link @click="handleResetPassword(row)">
            重置密码
          </el-button>
          <el-button type="info" link @click="handleViewDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      class="mt-4 text-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </fa-page-main>

  <!-- 绑定企业对话框 -->
  <el-dialog v-model="bindDialogVisible" title="绑定企业" width="400px">
    <el-form :model="bindForm" label-width="80px">
      <el-form-item label="选择企业" required>
        <el-select v-model="bindForm.companyId" placeholder="请选择企业" filterable>
          <el-option
            v-for="company in companyList"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="bindDialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="handleBindSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>

  <!-- 重置密码对话框 -->
  <el-dialog v-model="resetDialogVisible" title="重置密码" width="400px">
    <el-form ref="resetFormRef" :model="resetForm" :rules="resetRules" label-width="80px">
      <el-form-item label="新密码" prop="password" required>
        <el-input
          v-model="resetForm.password"
          type="password"
          placeholder="请输入新密码"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword" required>
        <el-input
          v-model="resetForm.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          show-password
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="resetDialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="handleResetSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>

  <!-- 用户详情对话框 -->
  <el-dialog v-model="detailDialogVisible" title="用户详情" width="600px">
    <div>
      <!-- 添加包裹元素作为根节点 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="账号">
          {{ currentUser.account }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ currentUser.name }}
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          {{ currentUser.gender === 1 ? '男' : currentUser.gender === 2 ? '女' : '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="钱包余额">
          ¥{{ (currentUser.banlance || 0) / 100 }}
        </el-descriptions-item>
        <el-descriptions-item label="优惠券">
          {{ currentUser.coupons || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="光影卡">
          {{ currentUser.benefitCards || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="会员卡余额">
          ¥{{ (currentUser.vipCardBalance || 0) / 100 }}
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">
          {{ formatDate(currentUser.ctime) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后活跃">
          {{ formatDate(currentUser.lastActiveDay) }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ currentUser.note || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" border class="mt-4">
        <el-descriptions-item label="所属企业">
          <el-tag
            v-for="company in currentUser.companys"
            :key="company.id"
            type="info"
            class="mr-2"
          >
            {{ company.name }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<style scoped>
.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}
</style>
