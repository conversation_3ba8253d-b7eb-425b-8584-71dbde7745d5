# 团购管理模块

基于API文档 `src/api/modules/market/groupBuy/团购.md` 实现的完整团购管理系统。

## 📁 目录结构

```
src/views/market/groupBuy/
├── components/              # 组件目录
│   ├── GroupBuySearchForm.vue  # 搜索表单组件
│   └── GroupBuyTable.vue       # 数据表格组件
├── detail/                  # 详情页面
│   └── index.vue              # 详情页面主文件
├── types/                   # 类型定义
│   └── index.ts               # TypeScript类型定义
├── utils/                   # 工具函数
│   └── index.ts               # 工具函数集合
├── index.vue               # 主页面
└── README.md              # 本文档
```

## 🚀 功能特性

### 核心功能
- ✅ **团购列表**: 分页查询、搜索过滤、状态管理
- ✅ **团购详情**: 完整的团购信息展示
- ✅ **状态管理**: 启用/禁用团购状态切换
- ✅ **批量操作**: 批量启用/禁用团购
- ✅ **响应式设计**: 完美适配各种设备

### 高级功能
- 🔄 **实时搜索**: 支持多条件组合搜索
- 📊 **数据可视化**: 价格展示、状态标签
- 🎨 **现代化UI**: Element Plus + 自定义样式
- 🔒 **类型安全**: 完整的TypeScript支持
- 📱 **移动端优化**: 响应式布局设计

## 📋 API接口

基于 `src/api/modules/market/groupBuy/团购.md` 文档实现的接口：

### 主要接口
- `POST /adm_groupBuy/list` - 获取团购列表
- `POST /adm_groupBuy/add` - 创建团购
- `POST /adm_groupBuy/save` - 编辑团购
- `POST /adm_groupBuy/status` - 状态变更
- `POST /adm_groupBuy/groupBuy` - 获取团购详情

## 🎯 数据类型

### 团购信息
```typescript
interface GroupBuy {
  id: string              // MongoDB自动生成的ID
  name: string            // 团购名称
  banner: string          // 封面图
  detailImg: string       // 长图
  originPrice: number     // 原价（单位：分）
  sellPrice: number       // 售价（单位：分）
  stockCount: number      // 库存数量
  useScope: number        // 可使用门店：自定义0 跟随物品1
  startTime: number       // 销售开始时间
  endTime: number         // 销售结束时间
  limitType: number       // 限购：不限购0 每个用户限购1
  limitCount: number      // 总量限制可优惠票数 张
  refundModel: number     // 退款条件: 不可退0 过期退1
  remark: string          // 购买须知
  description: string     // 说明
  goods: GroupBuyGoods[]  // 售卖物品
  stores: GroupBuyStore[] // 门店
  status: number          // 状态
  createTime: number      // 创建时间
  updateTime: number      // 更新时间
}
```

### 团购商品
```typescript
interface GroupBuyGoods {
  goodsType: number    // 物品类型 0:优惠券
  relationId: number   // 关联ID（物品类型：优惠券：coupon.id）
  goodsNum: number     // 数量
  amount: number       // 金额
}
```

### 团购门店
```typescript
interface GroupBuyStore {
  provinceCode: string // 省份编码
  cityCode: string     // 城市编码
  storeName: string    // 门店名称
}
```

## 🛠️ 使用方法

### 1. 基本使用
```vue
<template>
  <div>
    <!-- 搜索表单 -->
    <GroupBuySearchForm
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <GroupBuyTable
      :data="tableData"
      :loading="loading"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
      @status-change="handleStatusChange"
      @batch-enable="handleBatchEnable"
      @batch-disable="handleBatchDisable"
    />
  </div>
</template>
```

### 2. 数据获取
```typescript
import { getGroupBuyList } from '@/api/modules/market/groupBuy'
import { adaptGroupBuyList } from './utils'

// 获取团购列表
const response = await getGroupBuyList(params)
const adaptedData = adaptGroupBuyList(response.data)
```

### 3. 状态管理
```typescript
import { updateGroupBuyStatus } from '@/api/modules/market/groupBuy'

// 更新团购状态
await updateGroupBuyStatus({
  id: 'groupBuyId',
  useStatus: 1 // 0:关闭 1:开启
})
```

## 📊 常量定义

### 使用状态
```typescript
export const USE_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '关闭', value: 0 },
  { label: '开启', value: 1 }
]
```

### 限购类型
```typescript
export const LIMIT_TYPE_OPTIONS = [
  { label: '不限购', value: 0 },
  { label: '每个用户限购', value: 1 }
]
```

### 退款条件
```typescript
export const REFUND_MODEL_OPTIONS = [
  { label: '不可退', value: 0 },
  { label: '过期退', value: 1 }
]
```

## 🎨 样式特性

### 响应式设计
- **桌面端**: 网格布局，多列显示
- **平板端**: 自适应列数，保持可读性
- **移动端**: 单列布局，垂直排列

### 视觉效果
- **价格展示**: 原价和售价区分显示
- **状态标签**: 不同状态使用不同颜色
- **图片预览**: 支持点击放大查看
- **批量操作**: 选择后显示批量操作栏

## 🔧 工具函数

### 价格处理
```typescript
// 格式化价格（分转元）
formatPrice(price: number): string

// 价格转换（元转分）
priceToFen(price: number): number
```

### 时间处理
```typescript
// 格式化时间戳
formatTimestamp(timestamp: number): string
```

### 状态处理
```typescript
// 获取状态文本
getUseStatusText(status: number): string

// 获取状态标签类型
getUseStatusType(status: number): string
```

### 数据验证
```typescript
// 验证团购表单数据
validateGroupBuyForm(data: any): { valid: boolean; message?: string }
```

## 📱 页面路由

### 路由配置
```typescript
// 团购列表页
{
  path: '/market/group-buy',
  name: 'MarketGroupBuy',
  component: () => import('@/views/market/groupBuy/index.vue')
}

// 团购详情页
{
  path: '/market/group-buy/detail/:id',
  name: 'MarketGroupBuyDetail',
  component: () => import('@/views/market/groupBuy/detail/index.vue')
}
```

### 权限控制
```typescript
meta: {
  requiresAuth: true,
  permissions: ['/market/groupBuy/query']
}
```

## 🚧 待完善功能

### 1. 团购表单
- [ ] 新增团购对话框/页面
- [ ] 编辑团购对话框/页面
- [ ] 图片上传功能
- [ ] 富文本编辑器

### 2. 数据导入导出
- [ ] Excel批量导入团购
- [ ] 团购数据导出
- [ ] 模板下载

### 3. 高级功能
- [ ] 团购统计分析
- [ ] 销售数据报表
- [ ] 库存预警
- [ ] 自动上下架

## 🔍 开发规范

### 1. 代码规范
-