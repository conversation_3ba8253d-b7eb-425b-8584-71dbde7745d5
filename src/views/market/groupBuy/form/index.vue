<script setup lang="ts">
import type { CreateGroupBuyParams, UpdateGroupBuyParams } from '../types'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

import { useRoute, useRouter } from 'vue-router'
import { createGroupBuy, getGroupBuyDetail, updateGroupBuy } from '@/api/modules/market/groupBuy'
import { priceToFen, validateGroupBuyForm } from '../utils'

// ==================== 路由 ====================

const route = useRoute()
const router = useRouter()

// ==================== 响应式数据 ====================

const formRef = ref()
const submitLoading = ref(false)
const timeRange = ref<[string, string] | null>(null)

const isEdit = computed(() => !!route.params.id)

const formData = reactive<CreateGroupBuyParams>({
  name: '',
  banner: '',
  detailImg: '',
  originPrice: undefined,
  sellPrice: undefined,
  stockCount: undefined,
  useScope: 0,
  startTime: undefined,
  endTime: undefined,
  limitType: 0,
  limitCount: undefined,
  refundModel: 0,
  remark: '',
  description: '',
  goods: [],
  stores: [],
})

const formRules = {
  name: [
    { required: true, message: '请输入团购名称', trigger: 'blur' },
    { min: 2, max: 50, message: '团购名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  banner: [
    { required: true, message: '请上传封面图', trigger: 'change' },
  ],
  originPrice: [
    { required: true, message: '请输入原价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '原价必须大于0', trigger: 'blur' },
  ],
  sellPrice: [
    { required: true, message: '请输入售价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '售价必须大于0', trigger: 'blur' },
    {
      validator: (rule: any, value: number, callback: Function) => {
        if (value && formData.originPrice && value >= formData.originPrice) {
          callback(new Error('售价不能大于等于原价'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  stockCount: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '库存数量必须大于0', trigger: 'blur' },
  ],
  timeRange: [
    { required: true, message: '请选择销售时间', trigger: 'change' },
  ],
  limitCount: [
    {
      validator: (rule: any, value: number, callback: Function) => {
        if (formData.limitType === 1 && (!value || value < 1)) {
          callback(new Error('请输入限购数量'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// ==================== 方法 ====================

/** 处理时间范围变化 */
function handleTimeRangeChange(value: [string, string] | null) {
  if (value) {
    formData.startTime = Math.floor(new Date(value[0]).getTime() / 1000)
    formData.endTime = Math.floor(new Date(value[1]).getTime() / 1000)
  }
  else {
    formData.startTime = undefined
    formData.endTime = undefined
  }
}

/** 处理图片上传成功 */
function handleImageSuccess(response: any, field: 'banner' | 'detailImg') {
  if (response.code === 0) {
    formData[field] = response.data.url
    ElMessage.success('图片上传成功')
  }
  else {
    ElMessage.error(response.msg || '图片上传失败')
  }
}

/** 图片上传前验证 */
function beforeImageUpload(file: File) {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

/** 获取团购详情（编辑模式） */
async function fetchGroupBuyDetail() {
  const id = route.params.id as string
  if (!id) { return }

  try {
    const response = await getGroupBuyDetail({ id })

    if (response.code === 0 && response.data) {
      const data = response.data

      // 填充表单数据
      Object.assign(formData, {
        name: data.name,
        banner: data.banner,
        detailImg: data.detailImg,
        originPrice: data.originPrice / 100, // 分转元
        sellPrice: data.sellPrice / 100, // 分转元
        stockCount: data.stockCount,
        useScope: data.useScope,
        startTime: data.startTime,
        endTime: data.endTime,
        limitType: data.limitType,
        limitCount: data.limitCount,
        refundModel: data.refundModel,
        remark: data.remark,
        description: data.description,
        goods: data.goods || [],
        stores: data.stores || [],
      })

      // 设置时间范围
      if (data.startTime && data.endTime) {
        timeRange.value = [
          new Date(data.startTime * 1000).toISOString().slice(0, 19).replace('T', ' '),
          new Date(data.endTime * 1000).toISOString().slice(0, 19).replace('T', ' '),
        ]
      }
    }
    else {
      ElMessage.error(response.msg || '获取团购详情失败')
      handleBack()
    }
  }
  catch (error) {
    console.error('获取团购详情失败:', error)
    ElMessage.error('获取团购详情失败')
    handleBack()
  }
}

/** 处理提交 */
async function handleSubmit() {
  try {
    await formRef.value?.validate()

    // 构建提交数据
    const submitData = {
      ...formData,
      originPrice: priceToFen(formData.originPrice!), // 元转分
      sellPrice: priceToFen(formData.sellPrice!), // 元转分
    }

    // 验证数据
    const validation = validateGroupBuyForm(submitData)
    if (!validation.valid) {
      ElMessage.error(validation.message)
      return
    }

    submitLoading.value = true

    let response
    if (isEdit.value) {
      // 编辑模式
      const updateData: UpdateGroupBuyParams = {
        ...submitData,
        id: route.params.id as string,
      }
      response = await updateGroupBuy(updateData)
    }
    else {
      // 新增模式
      response = await createGroupBuy(submitData)
    }

    if (response.code === 0) {
      ElMessage.success(`${isEdit.value ? '编辑' : '创建'}团购成功`)
      handleBack()
    }
    else {
      ElMessage.error(response.msg || `${isEdit.value ? '编辑' : '创建'}团购失败`)
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(`${isEdit.value ? '编辑' : '创建'}团购失败`)
  }
  finally {
    submitLoading.value = false
  }
}

/** 处理返回 */
async function handleBack() {
  // 检查是否有未保存的更改
  const hasChanges = Object.values(formData).some(value =>
    value !== '' && value !== undefined && value !== null,
  )

  if (hasChanges) {
    try {
      await ElMessageBox.confirm(
        '当前有未保存的更改，确定要离开吗？',
        '确认离开',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )
    }
    catch {
      return
    }
  }

  router.push('/market/group-buy/list')
}

// ==================== 生命周期 ====================

onMounted(() => {
  if (isEdit.value) {
    fetchGroupBuyDetail()
  }
})
</script>

<template>
  <div class="group-buy-form-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="handleBack">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h2>{{ isEdit ? '编辑团购' : '新增团购' }}</h2>
      <div class="header-actions">
        <el-button @click="handleBack">
          取消
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
      >
        <!-- 基本信息 -->
        <el-card class="form-card" header="基本信息">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="团购名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入团购名称"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="库存数量" prop="stockCount">
                <el-input-number
                  v-model="formData.stockCount"
                  :min="1"
                  :max="999999"
                  placeholder="请输入库存数量"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="原价" prop="originPrice">
                <el-input-number
                  v-model="formData.originPrice"
                  :min="0.01"
                  :precision="2"
                  placeholder="请输入原价"
                  style="width: 100%"
                />
                <span class="price-unit">元</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="售价" prop="sellPrice">
                <el-input-number
                  v-model="formData.sellPrice"
                  :min="0.01"
                  :precision="2"
                  placeholder="请输入售价"
                  style="width: 100%"
                />
                <span class="price-unit">元</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="销售时间" prop="timeRange">
                <el-date-picker
                  v-model="timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  @change="handleTimeRangeChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 图片信息 -->
        <el-card class="form-card" header="图片信息">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="封面图" prop="banner">
                <el-upload
                  class="image-uploader"
                  :show-file-list="false"
                  :on-success="(response) => handleImageSuccess(response, 'banner')"
                  :before-upload="beforeImageUpload"
                  action="/api/upload/image"
                >
                  <img v-if="formData.banner" :src="formData.banner" class="uploaded-image">
                  <el-icon v-else class="image-uploader-icon">
                    <Plus />
                  </el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详情长图" prop="detailImg">
                <el-upload
                  class="image-uploader"
                  :show-file-list="false"
                  :on-success="(response) => handleImageSuccess(response, 'detailImg')"
                  :before-upload="beforeImageUpload"
                  action="/api/upload/image"
                >
                  <img v-if="formData.detailImg" :src="formData.detailImg" class="uploaded-image">
                  <el-icon v-else class="image-uploader-icon">
                    <Plus />
                  </el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 限购设置 -->
        <el-card class="form-card" header="限购设置">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="限购类型" prop="limitType">
                <el-radio-group v-model="formData.limitType">
                  <el-radio :label="0">
                    不限购
                  </el-radio>
                  <el-radio :label="1">
                    每个用户限购
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="formData.limitType === 1" :span="12">
              <el-form-item label="限购数量" prop="limitCount">
                <el-input-number
                  v-model="formData.limitCount"
                  :min="1"
                  :max="999"
                  placeholder="请输入限购数量"
                  style="width: 100%"
                />
                <span class="price-unit">张</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="退款条件" prop="refundModel">
                <el-radio-group v-model="formData.refundModel">
                  <el-radio :label="0">
                    不可退
                  </el-radio>
                  <el-radio :label="1">
                    过期退
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用门店" prop="useScope">
                <el-radio-group v-model="formData.useScope">
                  <el-radio :label="0">
                    自定义
                  </el-radio>
                  <el-radio :label="1">
                    跟随物品
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 说明信息 -->
        <el-card class="form-card" header="说明信息">
          <el-form-item label="购买须知" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入购买须知"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="说明" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入说明"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<style scoped>
.group-buy-form-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.price-unit {
  margin-left: 8px;
  color: #909399;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 12px;
  }

  .page-header h2 {
    text-align: left;
  }

  .form-content :deep(.el-col) {
    width: 100% !important;
  }
}
</style>
