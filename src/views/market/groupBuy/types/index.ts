/**
 * 团购管理相关类型定义
 */

// ==================== 基础类型 ====================

/** 团购商品 */
export interface GroupBuyGoods {
  /** 物品类型 0:优惠券 */
  goodsType: number
  /** 关联ID（物品类型：优惠券：coupon.id） */
  relationId: number
  /** 数量 */
  goodsNum: number
  /** 金额 */
  amount: number
}

/** 团购门店 */
export interface GroupBuyStore {
  /** 省份编码 */
  provinceCode: string
  /** 城市编码 */
  cityCode: string
  /** 门店名称 */
  storeName: string
}

/** 团购信息 */
export interface GroupBuy {
  /** MongoDB自动生成的ID */
  id: string
  /** 团购名称 */
  name: string
  /** 封面图 */
  banner: string
  /** 长图 */
  detailImg: string
  /** 原价（单位：分） */
  originPrice: number
  /** 售价（单位：分） */
  sellPrice: number
  /** 库存数量 */
  stockCount: number
  /** 可使用门店：自定义0 跟随物品1 */
  useScope: number
  /** 销售开始时间 */
  startTime: number
  /** 销售结束时间 */
  endTime: number
  /** 限购：不限购0 每个用户限购1 */
  limitType: number
  /** 总量限制可优惠票数 张 */
  limitCount: number
  /** 退款条件: 不可退0 过期退1 */
  refundModel: number
  /** 购买须知 */
  remark: string
  /** 说明 */
  description: string
  /** 售卖物品 */
  goods: GroupBuyGoods[]
  /** 门店 */
  stores: GroupBuyStore[]
  /** 状态 */
  status: number
  /** 创建时间 */
  createTime: number
  /** 更新时间 */
  updateTime: number
}

// ==================== 请求参数类型 ====================

/** 团购列表查询参数 */
export interface GroupBuyListParams {
  /** 页码 */
  page?: number
  /** 页大小 */
  size?: number
  /** 团购名称 */
  name?: string
  /** 售卖日期 开始时间 */
  startTime?: string
  /** 售卖日期 结束时间 */
  endTime?: string
  /** 使用状态，0关闭，1开启 */
  useStatus?: number
}

/** 创建团购参数 */
export interface CreateGroupBuyParams {
  /** 团购名称 */
  name?: string
  /** 封面图 */
  banner?: string
  /** 长图 */
  detailImg?: string
  /** 原价（单位：分） */
  originPrice?: number
  /** 售价（单位：分） */
  sellPrice?: number
  /** 库存数量 */
  stockCount?: number
  /** 可使用门店：自定义0 跟随物品1 */
  useScope?: number
  /** 销售开始时间 */
  startTime?: number
  /** 销售结束时间 */
  endTime?: number
  /** 限购：不限购0 每个用户限购1 */
  limitType?: number
  /** 总量限制可优惠票数 张 */
  limitCount?: number
  /** 退款条件: 不可退0 过期退1 */
  refundModel?: number
  /** 购买须知 */
  remark?: string
  /** 说明 */
  description?: string
  /** 售卖物品 */
  goods?: GroupBuyGoods[]
  /** 门店 */
  stores?: GroupBuyStore[]
}

/** 编辑团购参数 */
export interface UpdateGroupBuyParams extends CreateGroupBuyParams {
  /** 团购ID */
  id: string
}

/** 团购状态变更参数 */
export interface GroupBuyStatusParams {
  /** 团购 ID */
  id: string
  /** 使用状态，0关闭，1开启 */
  useStatus: number
}

/** 获取团购详情参数 */
export interface GetGroupBuyParams {
  /** ID */
  id?: string
}

// ==================== 响应类型 ====================

/** 团购列表响应 */
export interface GroupBuyListResponse {
  /** 总数 */
  total: number
  /** 内容 */
  content: GroupBuy[]
  /** 结果 */
  result: GroupBuy[]
}

// ==================== 常量定义 ====================

/** 使用状态选项 */
export const USE_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '关闭', value: 0 },
  { label: '开启', value: 1 }
]

/** 可使用门店选项 */
export const USE_SCOPE_OPTIONS = [
  { label: '自定义', value: 0 },
  { label: '跟随物品', value: 1 }
]

/** 限购类型选项 */
export const LIMIT_TYPE_OPTIONS = [
  { label: '不限购', value: 0 },
  { label: '每个用户限购', value: 1 }
]

/** 退款条件选项 */
export const REFUND_MODEL_OPTIONS = [
  { label: '不可退', value: 0 },
  { label: '过期退', value: 1 }
]

/** 物品类型选项 */
export const GOODS_TYPE_OPTIONS = [
  { label: '优惠券', value: 0 }
]