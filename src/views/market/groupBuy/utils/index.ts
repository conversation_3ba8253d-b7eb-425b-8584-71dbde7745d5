/**
 * 团购管理工具函数
 */

import type { GroupBuy, GroupBuyListResponse } from '../types'

/**
 * 格式化价格（分转元）
 * @param price 价格（分）
 * @returns 格式化后的价格字符串
 */
export function formatPrice(price: number): string {
  return (price / 100).toFixed(2)
}

/**
 * 价格转换（元转分）
 * @param price 价格（元）
 * @returns 价格（分）
 */
export function priceToFen(price: number): number {
  return Math.round(price * 100)
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
export function formatTimestamp(timestamp: number): string {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 获取使用状态文本
 * @param status 状态值
 * @returns 状态文本
 */
export function getUseStatusText(status: number): string {
  const statusMap: Record<number, string> = {
    0: '关闭',
    1: '开启'
  }
  return statusMap[status] || '未知'
}

/**
 * 获取使用状态标签类型
 * @param status 状态值
 * @returns Element Plus 标签类型
 */
export function getUseStatusType(status: number): string {
  const typeMap: Record<number, string> = {
    0: 'danger',
    1: 'success'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取限购类型文本
 * @param type 限购类型
 * @returns 限购类型文本
 */
export function getLimitTypeText(type: number): string {
  const typeMap: Record<number, string> = {
    0: '不限购',
    1: '每个用户限购'
  }
  return typeMap[type] || '未知'
}

/**
 * 获取退款条件文本
 * @param model 退款条件
 * @returns 退款条件文本
 */
export function getRefundModelText(model: number): string {
  const modelMap: Record<number, string> = {
    0: '不可退',
    1: '过期退'
  }
  return modelMap[model] || '未知'
}

/**
 * 获取使用门店文本
 * @param scope 使用门店类型
 * @returns 使用门店文本
 */
export function getUseScopeText(scope: number): string {
  const scopeMap: Record<number, string> = {
    0: '自定义',
    1: '跟随物品'
  }
  return scopeMap[scope] || '未知'
}

/**
 * 适配团购列表数据
 * @param response API响应数据
 * @returns 适配后的数据
 */
export function adaptGroupBuyList(response: GroupBuyListResponse) {
  return {
    total: response.total || 0,
    list: response.content || response.result || []
  }
}

/**
 * 验证团购表单数据
 * @param data 表单数据
 * @returns 验证结果
 */
export function validateGroupBuyForm(data: any): { valid: boolean; message?: string } {
  if (!data.name?.trim()) {
    return { valid: false, message: '请输入团购名称' }
  }
  
  if (!data.banner?.trim()) {
    return { valid: false, message: '请上传封面图' }
  }
  
  if (!data.originPrice || data.originPrice <= 0) {
    return { valid: false, message: '请输入有效的原价' }
  }
  
  if (!data.sellPrice || data.sellPrice <= 0) {
    return { valid: false, message: '请输入有效的售价' }
  }
  
  if (data.sellPrice >= data.originPrice) {
    return { valid: false, message: '售价不能大于等于原价' }
  }
  
  if (!data.stockCount || data.stockCount <= 0) {
    return { valid: false, message: '请输入有效的库存数量' }
  }
  
  if (!data.startTime) {
    return { valid: false, message: '请选择销售开始时间' }
  }
  
  if (!data.endTime) {
    return { valid: false, message: '请选择销售结束时间' }
  }
  
  if (data.startTime >= data.endTime) {
    return { valid: false, message: '开始时间不能大于等于结束时间' }
  }
  
  if (!data.goods || data.goods.length === 0) {
    return { valid: false, message: '请添加售卖物品' }
  }
  
  return { valid: true }
}

/**
 * 构建搜索参数
 * @param searchForm 搜索表单数据
 * @returns 搜索参数
 */
export function buildSearchParams(searchForm: any) {
  const params: any = {
    page: searchForm.page || 0,
    size: searchForm.size || 20
  }
  
  if (searchForm.name?.trim()) {
    params.name = searchForm.name.trim()
  }
  
  if (searchForm.startTime) {
    params.startTime = searchForm.startTime
  }
  
  if (searchForm.endTime) {
    params.endTime = searchForm.endTime
  }
  
  if (searchForm.useStatus !== undefined && searchForm.useStatus !== null) {
    params.useStatus = searchForm.useStatus
  }
  
  return params
}