<template>
  <ElDialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑团购' : '新增团购'"
    width="1200px"
    :close-on-click-modal="false"
    destroy-on-close
    @open="handleOpen"
    @closed="handleClosed"
  >
    <div v-loading="loading" class="group-buy-form">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
      >
        <!-- 基本信息 -->
        <ElCard class="form-card" header="基本信息">
          <ElRow :gutter="24">
            <ElCol :span="12">
              <ElFormItem label="团购名称" prop="name">
                <ElInput
                  v-model="formData.name"
                  placeholder="请输入团购名称"
                  maxlength="50"
                  show-word-limit
                  :disabled="pageMode === 'view'"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="库存数量" prop="stockCount">
                <ElInputNumber
                  v-model="formData.stockCount"
                  :min="1"
                  :max="999999"
                  placeholder="请输入库存数量"
                  style="width: 100%"
                  :disabled="pageMode === 'view'"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="24">
            <ElCol :span="12">
              <ElFormItem label="原价" prop="originPrice">
                <ElInputNumber
                  v-model="formData.originPrice"
                  :min="0.01"
                  :precision="2"
                  placeholder="请输入原价"
                  style="width: 100%"
                  :disabled="pageMode === 'view'"
                />
                <span class="price-unit">元</span>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="售价" prop="sellPrice">
                <ElInputNumber
                  v-model="formData.sellPrice"
                  :min="0.01"
                  :precision="2"
                  placeholder="请输入售价"
                  style="width: 100%"
                  :disabled="pageMode === 'view'"
                />
                <span class="price-unit">元</span>
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="24">
            <ElCol :span="24">
              <ElFormItem label="销售时间" prop="timeRange">
                <ElDatePicker
                  v-model="timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  :disabled="pageMode === 'view'"
                  @change="handleTimeRangeChange"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCard>

        <!-- 图片信息 -->
        <ElCard class="form-card" header="图片信息">
          <ElRow :gutter="24">
            <ElCol :span="12">
              <ElFormItem label="封面图" prop="banner">
                <ElUpload
                  v-if="pageMode !== 'view'"
                  class="image-uploader"
                  :show-file-list="false"
                  :on-success="(response) => handleImageSuccess(response, 'banner')"
                  :before-upload="beforeImageUpload"
                  action="/api/upload/image"
                >
                  <img v-if="formData.banner" :src="formData.banner" class="uploaded-image" />
                  <ElIcon v-else class="image-uploader-icon"><Plus /></ElIcon>
                </ElUpload>
                <div v-else class="image-preview">
                  <ElImage
                    v-if="formData.banner"
                    :src="formData.banner"
                    :preview-src-list="[formData.banner]"
                    class="preview-image"
                    fit="cover"
                  />
                  <div v-else class="no-image">暂无图片</div>
                </div>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="详情长图" prop="detailImg">
                <ElUpload
                  v-if="pageMode !== 'view'"
                  class="image-uploader"
                  :show-file-list="false"
                  :on-success="(response) => handleImageSuccess(response, 'detailImg')"
                  :before-upload="beforeImageUpload"
                  action="/api/upload/image"
                >
                  <img v-if="formData.detailImg" :src="formData.detailImg" class="uploaded-image" />
                  <ElIcon v-else class="image-uploader-icon"><Plus /></ElIcon>
                </ElUpload>
                <div v-else class="image-preview">
                  <ElImage
                    v-if="formData.detailImg"
                    :src="formData.detailImg"
                    :preview-src-list="[formData.detailImg]"
                    class="preview-image"
                    fit="cover"
                  />
                  <div v-else class="no-image">暂无图片</div>
                </div>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCard>

        <!-- 限购设置 -->
        <ElCard class="form-card" header="限购设置">
          <ElRow :gutter="24">
            <ElCol :span="12">
              <ElFormItem label="限购类型" prop="limitType">
                <ElRadioGroup v-model="formData.limitType" :disabled="pageMode === 'view'">
                  <ElRadio :label="0">不限购</ElRadio>
                  <ElRadio :label="1">每个用户限购</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol v-if="formData.limitType === 1" :span="12">
              <ElFormItem label="限购数量" prop="limitCount">
                <ElInputNumber
                  v-model="formData.limitCount"
                  :min="1"
                  :max="999"
                  placeholder="请输入限购数量"
                  style="width: 100%"
                  :disabled="pageMode === 'view'"
                />
                <span class="price-unit">张</span>
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="24">
            <ElCol :span="12">
              <ElFormItem label="退款条件" prop="refundModel">
                <ElRadioGroup v-model="formData.refundModel" :disabled="pageMode === 'view'">
                  <ElRadio :label="0">不可退</ElRadio>
                  <ElRadio :label="1">过期退</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="使用门店" prop="useScope">
                <ElRadioGroup v-model="formData.useScope" :disabled="pageMode === 'view'">
                  <ElRadio :label="0">自定义</ElRadio>
                  <ElRadio :label="1">跟随物品</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCard>

        <!-- 说明信息 -->
        <ElCard class="form-card" header="说明信息">
          <ElFormItem label="购买须知" prop="remark">
            <ElInput
              v-model="formData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入购买须知"
              maxlength="500"
              show-word-limit
              :disabled="pageMode === 'view'"
            />
          </ElFormItem>
          <ElFormItem label="说明" prop="description">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入说明"
              maxlength="500"
              show-word-limit
              :disabled="pageMode === 'view'"
            />
          </ElFormItem>
        </ElCard>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          {{ pageMode === 'view' ? '关闭' : '取消' }}
        </ElButton>
        <ElButton
          v-if="pageMode !== 'view'"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEdit ? '保存修改' : '创建团购' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getGroupBuyDetail, createGroupBuy, updateGroupBuy } from '@/api/modules/market/groupBuy'
import { validateGroupBuyForm, priceToFen } from '../utils'
import type { CreateGroupBuyParams, UpdateGroupBuyParams, GroupBuy } from '../types'

interface Props {
  modelValue: boolean
  pageMode?: 'add' | 'edit' | 'view'
  groupBuyId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: CreateGroupBuyParams | UpdateGroupBuyParams): void
  (e: 'success', data: { groupBuyId?: string, groupBuy?: GroupBuy }): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add'
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const formRef = ref()
const loading = ref(false)
const submitLoading = ref(false)
const timeRange = ref<[string, string] | null>(null)

const isEdit = computed(() => props.pageMode === 'edit')

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

const formData = reactive<CreateGroupBuyParams>({
  name: '',
  banner: '',
  detailImg: '',
  originPrice: undefined,
  sellPrice: undefined,
  stockCount: undefined,
  useScope: 0,
  startTime: undefined,
  endTime: undefined,
  limitType: 0,
  limitCount: undefined,
  refundModel: 0,
  remark: '',
  description: '',
  goods: [],
  stores: []
})

const formRules = {
  name: [
    { required: true, message: '请输入团购名称', trigger: 'blur' },
    { min: 2, max: 50, message: '团购名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  banner: [
    { required: true, message: '请上传封面图', trigger: 'change' }
  ],
  originPrice: [
    { required: true, message: '请输入原价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '原价必须大于0', trigger: 'blur' }
  ],
  sellPrice: [
    { required: true, message: '请输入售价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '售价必须大于0', trigger: 'blur' },
    {
      validator: (rule: any, value: number, callback: Function) => {
        if (value && formData.originPrice && value >= formData.originPrice) {
          callback(new Error('售价不能大于等于原价'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  stockCount: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '库存数量必须大于0', trigger: 'blur' }
  ],
  timeRange: [
    { required: true, message: '请选择销售时间', trigger: 'change' }
  ],
  limitCount: [
    {
      validator: (rule: any, value: number, callback: Function) => {
        if (formData.limitType === 1 && (!value || value < 1)) {
          callback(new Error('请输入限购数量'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// ==================== 监听器 ====================

watch(dialogVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      if (props.pageMode === 'edit' || props.pageMode === 'view') {
        fetchGroupBuyDetail()
      } else {
        resetForm()
      }
    })
  }
})

// ==================== 方法 ====================

/** 处理时间范围变化 */
function handleTimeRangeChange(value: [string, string] | null) {
  if (value) {
    formData.startTime = Math.floor(new Date(value[0]).getTime() / 1000)
    formData.endTime = Math.floor(new Date(value[1]).getTime() / 1000)
  } else {
    formData.startTime = undefined
    formData.endTime = undefined
  }
}

/** 处理图片上传成功 */
function handleImageSuccess(response: any, field: 'banner' | 'detailImg') {
  if (response.code === 0) {
    formData[field] = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.msg || '图片上传失败')
  }
}

/** 图片上传前验证 */
function beforeImageUpload(file: File) {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

/** 获取团购详情 */
async function fetchGroupBuyDetail() {
  if (!props.groupBuyId) return

  try {
    loading.value = true
    const response = await getGroupBuyDetail({ id: props.groupBuyId })

    if (response.code === 0 && response.data) {
      const data = response.data

      // 填充表单数据
      Object.assign(formData, {
        name: data.name,
        banner: data.banner,
        detailImg: data.detailImg,
        originPrice: data.originPrice / 100, // 分转元
        sellPrice: data.sellPrice / 100,     // 分转元
        stockCount: data.stockCount,
        useScope: data.useScope,
        startTime: data.startTime,
        endTime: data.endTime,
        limitType: data.limitType,
        limitCount: data.limitCount,
        refundModel: data.refundModel,
        remark: data.remark,
        description: data.description,
        goods: data.goods || [],
        stores: data.stores || []
      })

      // 设置时间范围
      if (data.startTime && data.endTime) {
        timeRange.value = [
          new Date(data.startTime * 1000).toISOString().slice(0, 19).replace('T', ' '),
          new Date(data.endTime * 1000).toISOString().slice(0, 19).replace('T', ' ')
        ]
      }
    } else {
      ElMessage.error(response.msg || '获取团购详情失败')
    }
  } catch (error) {
    console.error('获取团购详情失败:', error)
    ElMessage.error('获取团购详情失败')
  } finally {
    loading.value = false
  }
}

/** 重置表单 */
function resetForm() {
  Object.assign(formData, {
    name: '',
    banner: '',
    detailImg: '',
    originPrice: undefined,
    sellPrice: undefined,
    stockCount: undefined,
    useScope: 0,
    startTime: undefined,
    endTime: undefined,
    limitType: 0,
    limitCount: undefined,
    refundModel: 0,
    remark: '',
    description: '',
    goods: [],
    stores: []
  })
  timeRange.value = null
  formRef.value?.clearValidate()
}

/** 处理提交 */
async function handleSubmit() {
  try {
    await formRef.value?.validate()

    // 构建提交数据
    const submitData = {
      ...formData,
      originPrice: priceToFen(formData.originPrice!), // 元转分
      sellPrice: priceToFen(formData.sellPrice!),     // 元转分
    }

    // 验证数据
    const validation = validateGroupBuyForm(submitData)
    if (!validation.valid) {
      ElMessage.error(validation.message)
      return
    }

    submitLoading.value = true

    let response
    if (isEdit.value) {
      // 编辑模式
      const updateData: UpdateGroupBuyParams = {
        ...submitData,
        id: props.groupBuyId!
      }
      response = await updateGroupBuy(updateData)
      emit('submit', updateData)
    } else {
      // 新增模式
      response = await createGroupBuy(submitData)
      emit('submit', submitData)
    }

    if (response.code === 0) {
      ElMessage.success(`${isEdit.value ? '编辑' : '创建'}团购成功`)
      emit('success', { 
        groupBuyId: response.data?.id || props.groupBuyId,
        groupBuy: response.data 
      })
      dialogVisible.value = false
    } else {
      ElMessage.error(response.msg || `${isEdit.value ? '编辑' : '创建'}团购失败`)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(`${isEdit.value ? '编辑' : '创建'}团购失败`)
  } finally {
    submitLoading.value = false
  }
}

/** 处理取消 */
function handleCancel() {
  emit('cancel')
  dialogVisible.value = false
}

/** 处理对话框打开 */
function handleOpen() {
  // 对话框打开时的逻辑
}

/** 处理对话框关闭 */
function handleClosed() {
  resetForm()
}
</script>

<style scoped>
.group-buy-form {
  max-height: 70vh;
  overflow-y: auto;
}

.form-card {
  margin-bottom: 20px;
}

.form-card:last-child {
  margin-bottom: 0;
}

.price-unit {
  margin-left: 8px;
  color: #909399;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-preview {
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

.preview-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.no-image {
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button + .el-button {
  margin-left: 12px;
}

@media (max-width: 768px) {
  .form-card :deep(.el-col) {
    width: 100% !important;
  }
}
</style>