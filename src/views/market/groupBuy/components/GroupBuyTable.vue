<template>
  <div class="table-container">
    <el-table
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="name" label="团购名称" min-width="150">
        <template #default="{ row }">
          <div class="name-cell">
            <el-image
              v-if="row.banner"
              :src="row.banner"
              :preview-src-list="[row.banner]"
              class="banner-image"
              fit="cover"
            />
            <span class="name-text">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="originPrice" label="原价" width="100" align="right">
        <template #default="{ row }">
          <span class="price-text">¥{{ formatPrice(row.originPrice) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="sellPrice" label="售价" width="100" align="right">
        <template #default="{ row }">
          <span class="price-text sell-price">¥{{ formatPrice(row.sellPrice) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="stockCount" label="库存" width="80" align="center" />
      
      <el-table-column prop="limitType" label="限购" width="100" align="center">
        <template #default="{ row }">
          <span>{{ getLimitTypeText(row.limitType) }}</span>
          <span v-if="row.limitType === 1 && row.limitCount" class="limit-count">
            ({{ row.limitCount }}张)
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="useScope" label="使用门店" width="100" align="center">
        <template #default="{ row }">
          {{ getUseScopeText(row.useScope) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="startTime" label="销售时间" width="160">
        <template #default="{ row }">
          <div class="time-range">
            <div>{{ formatTimestamp(row.startTime) }}</div>
            <div>{{ formatTimestamp(row.endTime) }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="getUseStatusType(row.status)">
            {{ getUseStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="createTime" label="创建时间" width="160">
        <template #default="{ row }">
          {{ formatTimestamp(row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            link
            @click="handleViewDetail(row)"
          >
            查看
          </el-button>
          <el-button
            type="primary"
            size="small"
            link
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            :type="row.status === 1 ? 'warning' : 'success'"
            size="small"
            link
            @click="handleStatusChange(row)"
          >
            {{ row.status === 1 ? '关闭' : '开启' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 批量操作 -->
    <div v-if="selectedRows.length > 0" class="batch-actions">
      <span class="selected-info">已选择 {{ selectedRows.length }} 项</span>
      <el-button type="success" size="small" @click="handleBatchEnable">
        批量开启
      </el-button>
      <el-button type="warning" size="small" @click="handleBatchDisable">
        批量关闭
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { GroupBuy } from '../types'
import {
  formatPrice,
  formatTimestamp,
  getUseStatusText,
  getUseStatusType,
  getLimitTypeText,
  getUseScopeText
} from '../utils'

// ==================== Props & Emits ====================

interface Props {
  data: GroupBuy[]
  loading?: boolean
}

interface Emits {
  (e: 'view-detail', row: GroupBuy): void
  (e: 'edit', row: GroupBuy): void
  (e: 'status-change', row: GroupBuy): void
  (e: 'batch-enable', rows: GroupBuy[]): void
  (e: 'batch-disable', rows: GroupBuy[]): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const selectedRows = ref<GroupBuy[]>([])

// ==================== 方法 ====================

/** 处理选择变化 */
function handleSelectionChange(selection: GroupBuy[]) {
  selectedRows.value = selection
}

/** 处理查看详情 */
function handleViewDetail(row: GroupBuy) {
  emit('view-detail', row)
}

/** 处理编辑 */
function handleEdit(row: GroupBuy) {
  emit('edit', row)
}

/** 处理状态变更 */
function handleStatusChange(row: GroupBuy) {
  emit('status-change', row)
}

/** 处理批量启用 */
function handleBatchEnable() {
  emit('batch-enable', selectedRows.value)
}

/** 处理批量禁用 */
function handleBatchDisable() {
  emit('batch-disable', selectedRows.value)
}
</script>

<style scoped>
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.banner-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  flex-shrink: 0;
}

.name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-text {
  font-weight: 500;
}

.sell-price {
  color: #f56c6c;
}

.limit-count {
  color: #909399;
  font-size: 12px;
}

.time-range {
  font-size: 12px;
  line-height: 1.4;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #f5f7fa;
  border-top: 1px solid #ebeef5;
}

.selected-info {
  color: #606266;
  font-size: 14px;
}
</style>