<template>
  <ElDialog
    v-model="dialogVisible"
    :title="groupBuy?.name || '团购详情'"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="group-buy-detail">
      <div v-if="groupBuy" class="detail-content">
        <!-- 头部统计卡片 -->
        <div class="stats-grid">
          <ElCard class="stat-card" shadow="hover">
            <div class="stat-value">{{ formatPrice(groupBuy.sellPrice) }}</div>
            <div class="stat-label">售价</div>
          </ElCard>
          <ElCard class="stat-card" shadow="hover">
            <div class="stat-value">{{ formatPrice(groupBuy.originPrice) }}</div>
            <div class="stat-label">原价</div>
          </ElCard>
          <ElCard class="stat-card" shadow="hover">
            <div class="stat-value">{{ groupBuy.stockCount }}</div>
            <div class="stat-label">库存数量</div>
          </ElCard>
          <ElCard class="stat-card" shadow="hover">
            <div class="stat-value">
              <ElTag :type="groupBuy.status === 1 ? 'success' : 'danger'">
                {{ groupBuy.status === 1 ? '开启' : '关闭' }}
              </ElTag>
            </div>
            <div class="stat-label">状态</div>
          </ElCard>
        </div>

        <!-- 基本信息 -->
        <ElCard class="info-card" header="基本信息">
          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="团购名称">
              {{ groupBuy.name }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="库存数量">
              {{ groupBuy.stockCount }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="原价">
              {{ formatPrice(groupBuy.originPrice) }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="售价">
              {{ formatPrice(groupBuy.sellPrice) }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="销售开始时间">
              {{ formatTime(groupBuy.startTime) }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="销售结束时间">
              {{ formatTime(groupBuy.endTime) }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="限购类型">
              <ElTag :type="groupBuy.limitType === 0 ? 'info' : 'warning'">
                {{ groupBuy.limitType === 0 ? '不限购' : '每个用户限购' }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="限购数量">
              {{ groupBuy.limitType === 1 ? `${groupBuy.limitCount}张` : '-' }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="退款条件">
              <ElTag :type="groupBuy.refundModel === 0 ? 'danger' : 'success'">
                {{ groupBuy.refundModel === 0 ? '不可退' : '过期退' }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="使用门店">
              <ElTag :type="groupBuy.useScope === 0 ? 'primary' : 'success'">
                {{ groupBuy.useScope === 0 ? '自定义' : '跟随物品' }}
              </ElTag>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 图片信息 -->
        <ElCard class="info-card" header="图片信息">
          <div class="image-gallery">
            <div v-if="groupBuy.banner" class="image-item">
              <div class="image-label">封面图：</div>
              <ElImage
                :src="groupBuy.banner"
                :preview-src-list="[groupBuy.banner]"
                class="preview-image"
                fit="cover"
              />
            </div>
            <div v-if="groupBuy.detailImg" class="image-item">
              <div class="image-label">详情长图：</div>
              <ElImage
                :src="groupBuy.detailImg"
                :preview-src-list="[groupBuy.detailImg]"
                class="preview-image"
                fit="cover"
              />
            </div>
          </div>
        </ElCard>

        <!-- 说明信息 -->
        <ElCard v-if="groupBuy.remark || groupBuy.description" class="info-card" header="说明信息">
          <div v-if="groupBuy.remark" class="description-item">
            <div class="description-label">购买须知：</div>
            <div class="description-content">{{ groupBuy.remark }}</div>
          </div>
          <div v-if="groupBuy.description" class="description-item">
            <div class="description-label">说明：</div>
            <div class="description-content">{{ groupBuy.description }}</div>
          </div>
        </ElCard>

        <!-- 售卖物品 -->
        <ElCard v-if="groupBuy.goods?.length" class="info-card" header="售卖物品">
          <ElTable :data="groupBuy.goods" border>
            <ElTableColumn prop="goodsType" label="物品类型" width="120">
              <template #default="{ row }">
                <ElTag type="primary">
                  {{ row.goodsType === 0 ? '优惠券' : '未知类型' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="relationId" label="关联ID" />
            <ElTableColumn prop="goodsNum" label="数量" width="80" />
            <ElTableColumn prop="amount" label="金额" width="100">
              <template #default="{ row }">
                {{ formatPrice(row.amount) }}
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>

        <!-- 门店信息 -->
        <ElCard v-if="groupBuy.stores?.length" class="info-card" header="门店信息">
          <ElTable :data="groupBuy.stores" border>
            <ElTableColumn prop="storeName" label="门店名称" />
            <ElTableColumn prop="provinceCode" label="省份编码" width="120" />
            <ElTableColumn prop="cityCode" label="城市编码" width="120" />
          </ElTable>
        </ElCard>

        <!-- 创建信息 -->
        <ElCard class="info-card" header="创建信息">
          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="创建时间">
              {{ formatTime(groupBuy.createTime) }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="更新时间">
              {{ formatTime(groupBuy.updateTime) }}
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>
      </div>

      <div v-else class="empty-state">
        <ElEmpty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <ElButton :icon="Share" @click="handleShare">分享</ElButton>
          <ElButton :icon="Download" @click="handleExport">导出</ElButton>
        </div>
        <div class="footer-right">
          <ElButton @click="dialogVisible = false">关闭</ElButton>
          <ElButton type="primary" :icon="Edit" @click="handleEdit">编辑</ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Share, Download } from '@element-plus/icons-vue'
import { getGroupBuyDetail } from '@/api/modules/market/groupBuy'
import type { GroupBuy } from '../types'

interface Props {
  modelValue: boolean
  groupBuyId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', groupBuy: GroupBuy): void
  (e: 'share', groupBuy: GroupBuy): void
  (e: 'export', groupBuy: GroupBuy): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const loading = ref(false)
const groupBuy = ref<GroupBuy | null>(null)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

// ==================== 监听器 ====================

watch(dialogVisible, (visible) => {
  if (visible && props.groupBuyId) {
    fetchGroupBuyDetail()
  }
})

// ==================== 方法 ====================

/** 获取团购详情 */
async function fetchGroupBuyDetail() {
  if (!props.groupBuyId) return

  try {
    loading.value = true
    const response = await getGroupBuyDetail({ id: props.groupBuyId })

    if (response.code === 0 && response.data) {
      groupBuy.value = response.data
    } else {
      ElMessage.error(response.msg || '获取团购详情失败')
      groupBuy.value = null
    }
  } catch (error) {
    console.error('获取团购详情失败:', error)
    ElMessage.error('获取团购详情失败')
    groupBuy.value = null
  } finally {
    loading.value = false
  }
}

/** 格式化价格 */
function formatPrice(price: number): string {
  return `¥${(price / 100).toFixed(2)}`
}

/** 格式化时间 */
function formatTime(timestamp: number): string {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/** 处理编辑 */
function handleEdit() {
  if (groupBuy.value) {
    emit('edit', groupBuy.value)
  }
}

/** 处理分享 */
function handleShare() {
  if (groupBuy.value) {
    emit('share', groupBuy.value)
    ElMessage.success('分享功能开发中...')
  }
}

/** 处理导出 */
function handleExport() {
  if (groupBuy.value) {
    emit('export', groupBuy.value)
    ElMessage.success('导出功能开发中...')
  }
}
</script>

<style scoped>
.group-buy-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: default;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.image-item {
  text-align: center;
}

.image-label {
  font-weight: 500;
  margin-bottom: 10px;
  color: #606266;
}

.preview-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-image:hover {
  transform: scale(1.05);
}

.description-item {
  margin-bottom: 16px;
}

.description-item:last-child {
  margin-bottom: 0;
}

.description-label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.description-content {
  color: #303133;
  line-height: 1.6;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .image-gallery {
    grid-template-columns: 1fr;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }
  
  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style>