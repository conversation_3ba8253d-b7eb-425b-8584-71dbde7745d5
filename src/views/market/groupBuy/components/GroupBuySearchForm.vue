<template>
  <div class="search-form">
    <el-form
      ref="searchFormRef"
      :model="searchForm"
      :inline="true"
      label-width="80px"
      class="search-form-content"
    >
      <el-form-item label="团购名称" prop="name">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入团购名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="售卖时间" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 350px"
          @change="handleTimeRangeChange"
        />
      </el-form-item>
      
      <el-form-item label="使用状态" prop="useStatus">
        <el-select
          v-model="searchForm.useStatus"
          placeholder="请选择状态"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="option in USE_STATUS_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { USE_STATUS_OPTIONS } from '../types'

// ==================== Props & Emits ====================

interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'search', params: any): void
  (e: 'reset'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const searchFormRef = ref()
const timeRange = ref<[string, string] | null>(null)

const searchForm = reactive({
  name: '',
  startTime: '',
  endTime: '',
  useStatus: undefined as number | undefined
})

// ==================== 方法 ====================

/** 处理时间范围变化 */
function handleTimeRangeChange(value: [string, string] | null) {
  if (value) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

/** 处理搜索 */
function handleSearch() {
  emit('search', { ...searchForm })
}

/** 处理重置 */
function handleReset() {
  searchFormRef.value?.resetFields()
  timeRange.value = null
  searchForm.name = ''
  searchForm.startTime = ''
  searchForm.endTime = ''
  searchForm.useStatus = undefined
  emit('reset')
}

// ==================== 暴露方法 ====================

defineExpose({
  handleSearch,
  handleReset
})
</script>

<style scoped>
.search-form {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form-content {
  margin-bottom: -18px;
}

.search-form-content .el-form-item {
  margin-bottom: 18px;
}
</style>