<script setup lang="ts">
import type { GroupBuy, GroupBuyListParams } from './types'
import { Check, Plus, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref } from 'vue'
import { getGroupBuyList, updateGroupBuyStatus } from '@/api/modules/market/groupBuy'

// 导入弹窗组件
import GroupBuyDetailDialog from './components/GroupBuyDetailDialog.vue'
import GroupBuyFormDialog from './components/GroupBuyFormDialog.vue'

// ==================== 响应式数据 ====================

const loading = ref(false)
const tableData = ref<GroupBuy[]>([])
const selectedRows = ref<GroupBuy[]>([])

// 弹窗控制
const showDetailDialog = ref(false)
const showFormDialog = ref(false)
const currentGroupBuyId = ref<string>('')
const formMode = ref<'add' | 'edit' | 'view'>('add')

// 搜索参数
const searchParams = reactive<GroupBuyListParams>({
  page: 1,
  size: 20,
  name: '',
  startTime: '',
  endTime: '',
  useStatus: undefined,
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
})

// ==================== 方法 ====================

/** 获取团购列表 */
async function fetchGroupBuyList() {
  try {
    loading.value = true
    const params = {
      ...searchParams,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size,
    }

    const response = await getGroupBuyList(params)

    if (response.code === 0) {
      tableData.value = response.data?.content || []
      pagination.total = response.data?.total || 0
    }
    else {
      ElMessage.error(response.msg || '获取团购列表失败')
    }
  }
  catch (error) {
    console.error('获取团购列表失败:', error)
    ElMessage.error('获取团购列表失败')
  }
  finally {
    loading.value = false
  }
}

/** 处理新增 */
function handleAdd() {
  formMode.value = 'add'
  currentGroupBuyId.value = ''
  showFormDialog.value = true
}

/** 处理查看详情 */
function handleViewDetail(row: GroupBuy) {
  currentGroupBuyId.value = row.id
  showDetailDialog.value = true
}

/** 处理编辑 */
function handleEdit(row: GroupBuy) {
  formMode.value = 'edit'
  currentGroupBuyId.value = row.id
  showFormDialog.value = true
}

/** 处理详情弹窗中的编辑 */
function handleDetailEdit(groupBuy: GroupBuy) {
  showDetailDialog.value = false
  formMode.value = 'edit'
  currentGroupBuyId.value = groupBuy.id
  showFormDialog.value = true
}

/** 处理详情弹窗中的分享 */
function handleDetailShare(groupBuy: GroupBuy) {
  ElMessage.success('分享功能开发中...')
}

/** 处理详情弹窗中的导出 */
function handleDetailExport(groupBuy: GroupBuy) {
  ElMessage.success('导出功能开发中...')
}

/** 处理表单提交 */
function handleFormSubmit(data: any) {
  console.log('表单提交:', data)
}

/** 处理表单提交成功 */
function handleFormSuccess(data: any) {
  console.log('表单提交成功:', data)
  fetchGroupBuyList() // 刷新列表
}

/** 处理表单取消 */
function handleFormCancel() {
  console.log('表单取消')
}

/** 处理状态变更 */
async function handleStatusChange(row: GroupBuy) {
  const newStatus = row.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '开启' : '关闭'

  try {
    await ElMessageBox.confirm(
      `确定要${action}团购"${row.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const response = await updateGroupBuyStatus({
      id: row.id,
      useStatus: newStatus,
    })

    if (response.code === 0) {
      ElMessage.success(`${action}成功`)
      fetchGroupBuyList()
    }
    else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('状态变更失败:', error)
      ElMessage.error(`${action}失败`)
    }
  }
}

/** 处理批量启用 */
async function handleBatchEnable() {
  try {
    await ElMessageBox.confirm(
      `确定要批量开启选中的 ${selectedRows.value.length} 个团购吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 批量处理
    const promises = selectedRows.value.map(row =>
      updateGroupBuyStatus({ id: row.id, useStatus: 1 }),
    )

    await Promise.all(promises)
    ElMessage.success('批量开启成功')
    fetchGroupBuyList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量开启失败:', error)
      ElMessage.error('批量开启失败')
    }
  }
}

/** 处理选择变化 */
function handleSelectionChange(selection: GroupBuy[]) {
  selectedRows.value = selection
}

/** 处理分页大小变化 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchGroupBuyList()
}

/** 处理页码变化 */
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchGroupBuyList()
}

/** 格式化时间 */
function formatTime(timestamp: number): string {
  if (!timestamp) { return '-' }
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

// ==================== 生命周期 ====================

onMounted(() => {
  fetchGroupBuyList()
})
</script>

<template>
  <div class="group-buy-page">
    <!-- 搜索表单 -->
    <ElCard class="search-card" shadow="never">
      <!-- ... 搜索表单内容保持不变 ... -->
    </ElCard>

    <!-- 数据表格 -->
    <ElCard class="table-card" shadow="never">
      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <ElButton type="primary" :icon="Plus" @click="handleAdd">
            新增团购
          </ElButton>
          <ElButton
            type="success"
            :icon="Check"
            :disabled="!selectedRows.length"
            @click="handleBatchEnable"
          >
            批量开启
          </ElButton>
        </div>
        <div class="toolbar-right">
          <ElButton :icon="Refresh" @click="fetchGroupBuyList">
            刷新
          </ElButton>
        </div>
      </div>

      <!-- 表格内容 -->
      <ElTable
        v-loading="loading"
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <ElTableColumn type="selection" width="55" />
        <ElTableColumn prop="name" label="团购名称" min-width="150" />
        <ElTableColumn prop="banner" label="封面图" width="100">
          <template #default="{ row }">
            <ElImage
              v-if="row.banner"
              :src="row.banner"
              :preview-src-list="[row.banner]"
              class="table-image"
              fit="cover"
            />
            <span v-else class="no-image">暂无图片</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="originPrice" label="原价" width="100">
          <template #default="{ row }">
            ¥{{ (row.originPrice / 100).toFixed(2) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="sellPrice" label="售价" width="100">
          <template #default="{ row }">
            ¥{{ (row.sellPrice / 100).toFixed(2) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="stockCount" label="库存" width="80" />
        <ElTableColumn prop="startTime" label="开始时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.startTime) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="endTime" label="结束时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.endTime) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" width="80">
          <template #default="{ row }">
            <ElTag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '开启' : '关闭' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <ElButton link type="primary" @click="handleViewDetail(row)">
              查看
            </ElButton>
            <ElButton link type="primary" @click="handleEdit(row)">
              编辑
            </ElButton>
            <ElButton
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              @click="handleStatusChange(row)"
            >
              {{ row.status === 1 ? '关闭' : '开启' }}
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>

    <!-- 团购详情弹窗 -->
    <GroupBuyDetailDialog
      v-model="showDetailDialog"
      :group-buy-id="currentGroupBuyId"
      @edit="handleDetailEdit"
      @share="handleDetailShare"
      @export="handleDetailExport"
    />

    <!-- 团购表单弹窗 -->
    <GroupBuyFormDialog
      v-model="showFormDialog"
      :page-mode="formMode"
      :group-buy-id="currentGroupBuyId"
      @submit="handleFormSubmit"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />
  </div>
</template>

<style scoped>
.group-buy-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
}

.table-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}

.no-image {
  color: #909399;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
}
</style>
