<script setup lang="ts">
import type { GroupBuy, GroupBuyListParams } from './types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

import { useRouter } from 'vue-router'
import { getGroupBuyList, updateGroupBuyStatus } from '@/api/modules/market/groupBuy'

// ==================== 路由 ====================

const router = useRouter()

// ==================== 响应式数据 ====================

const loading = ref(false)
const tableData = ref<GroupBuy[]>([])

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
})

const searchParams = reactive<GroupBuyListParams>({
  name: '',
  startTime: '',
  endTime: '',
  useStatus: undefined,
})

// ==================== 方法 ====================

/** 获取团购列表 */
async function fetchGroupBuyList() {
  try {
    loading.value = true

    const params = ({
      ...searchParams,
      page: pagination.page - 1, // API使用0基索引
      size: pagination.size,
    })

    const response = await getGroupBuyList(params)

    if (response.code === 0 && response.data) {
      const adaptedData = (response.data)
      tableData.value = adaptedData.list
      pagination.total = adaptedData.total
    }
    else {
      ElMessage.error(response.msg || '获取团购列表失败')
    }
  }
  catch (error) {
    console.error('获取团购列表失败:', error)
    ElMessage.error('获取团购列表失败')
  }
  finally {
    loading.value = false
  }
}

/** 处理搜索 */
function handleSearch(params: any) {
  Object.assign(searchParams, params)
  pagination.page = 1
  fetchGroupBuyList()
}

/** 处理重置 */
function handleReset() {
  Object.assign(searchParams, {
    name: '',
    startTime: '',
    endTime: '',
    useStatus: undefined,
  })
  pagination.page = 1
  fetchGroupBuyList()
}

/** 处理新增 */
function handleAdd() {
  // TODO: 跳转到新增页面或打开新增对话框
  ElMessage.info('新增功能开发中...')
}

/** 处理查看详情 */
function handleViewDetail(row: GroupBuy) {
  router.push(`/market/group-buy/detail/${row.id}`)
}

/** 处理编辑 */
function handleEdit(row: GroupBuy) {
  // TODO: 跳转到编辑页面或打开编辑对话框
  ElMessage.info('编辑功能开发中...')
}

/** 处理状态变更 */
async function handleStatusChange(row: GroupBuy) {
  const newStatus = row.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '开启' : '关闭'

  try {
    await ElMessageBox.confirm(
      `确定要${action}团购"${row.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const response = await updateGroupBuyStatus({
      id: row.id,
      useStatus: newStatus,
    })

    if (response.code === 0) {
      ElMessage.success(`${action}成功`)
      fetchGroupBuyList()
    }
    else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('状态变更失败:', error)
      ElMessage.error(`${action}失败`)
    }
  }
}

/** 处理批量启用 */
async function handleBatchEnable(rows: GroupBuy[]) {
  try {
    await ElMessageBox.confirm(
      `确定要批量开启选中的 ${rows.length} 个团购吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 批量处理
    const promises = rows.map(row =>
      updateGroupBuyStatus({ id: row.id, useStatus: 1 }),
    )

    await Promise.all(promises)
    ElMessage.success('批量开启成功')
    fetchGroupBuyList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量开启失败:', error)
      ElMessage.error('批量开启失败')
    }
  }
}

/** 处理批量禁用 */
async function handleBatchDisable(rows: GroupBuy[]) {
  try {
    await ElMessageBox.confirm(
      `确定要批量关闭选中的 ${rows.length} 个团购吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 批量处理
    const promises = rows.map(row =>
      updateGroupBuyStatus({ id: row.id, useStatus: 0 }),
    )

    await Promise.all(promises)
    ElMessage.success('批量关闭成功')
    fetchGroupBuyList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量关闭失败:', error)
      ElMessage.error('批量关闭失败')
    }
  }
}

/** 处理页大小变化 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchGroupBuyList()
}

/** 处理当前页变化 */
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchGroupBuyList()
}

// ==================== 生命周期 ====================

onMounted(() => {
  fetchGroupBuyList()
})
</script>

<template>
  <div class="group-buy-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="handleBack">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h2>团购详情</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleEdit">
          编辑
        </el-button>
        <el-button
          :type="groupBuyDetail?.status === 1 ? 'warning' : 'success'"
          @click="handleStatusChange"
        >
          {{ groupBuyDetail?.status === 1 ? '关闭' : '开启' }}
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 详情内容 -->
    <div v-else-if="groupBuyDetail" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-card" header="基本信息">
        <div class="info-grid">
          <div class="info-item">
            <label>团购名称：</label>
            <span>{{ groupBuyDetail.name }}</span>
          </div>
          <div class="info-item">
            <label>状态：</label>
            <el-tag :type="getUseStatusType(groupBuyDetail.status)">
              {{ getUseStatusText(groupBuyDetail.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>原价：</label>
            <span class="price-text">¥{{ formatPrice(groupBuyDetail.originPrice) }}</span>
          </div>
          <div class="info-item">
            <label>售价：</label>
            <span class="price-text sell-price">¥{{ formatPrice(groupBuyDetail.sellPrice) }}</span>
          </div>
          <div class="info-item">
            <label>库存数量：</label>
            <span>{{ groupBuyDetail.stockCount }}</span>
          </div>
          <div class="info-item">
            <label>使用门店：</label>
            <span>{{ getUseScopeText(groupBuyDetail.useScope) }}</span>
          </div>
          <div class="info-item">
            <label>限购类型：</label>
            <span>
              {{ getLimitTypeText(groupBuyDetail.limitType) }}
              <span v-if="groupBuyDetail.limitType === 1 && groupBuyDetail.limitCount" class="limit-count">
                ({{ groupBuyDetail.limitCount }}张)
              </span>
            </span>
          </div>
          <div class="info-item">
            <label>退款条件：</label>
            <span>{{ getRefundModelText(groupBuyDetail.refundModel) }}</span>
          </div>
          <div class="info-item full-width">
            <label>销售时间：</label>
            <span>{{ formatTimestamp(groupBuyDetail.startTime) }} 至 {{ formatTimestamp(groupBuyDetail.endTime) }}</span>
          </div>
          <div class="info-item full-width">
            <label>创建时间：</label>
            <span>{{ formatTimestamp(groupBuyDetail.createTime) }}</span>
          </div>
          <div class="info-item full-width">
            <label>更新时间：</label>
            <span>{{ formatTimestamp(groupBuyDetail.updateTime) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 图片信息 -->
      <el-card class="info-card" header="图片信息">
        <div class="image-grid">
          <div class="image-item">
            <label>封面图：</label>
            <el-image
              v-if="groupBuyDetail.banner"
              :src="groupBuyDetail.banner"
              :preview-src-list="[groupBuyDetail.banner]"
              class="detail-image"
              fit="cover"
            />
            <span v-else class="no-image">暂无图片</span>
          </div>
          <div class="image-item">
            <label>详情长图：</label>
            <el-image
              v-if="groupBuyDetail.detailImg"
              :src="groupBuyDetail.detailImg"
              :preview-src-list="[groupBuyDetail.detailImg]"
              class="detail-image"
              fit="cover"
            />
            <span v-else class="no-image">暂无图片</span>
          </div>
        </div>
      </el-card>

      <!-- 售卖物品 -->
      <el-card class="info-card" header="售卖物品">
        <el-table :data="groupBuyDetail.goods" border>
          <el-table-column prop="goodsType" label="物品类型" width="120">
            <template #default="{ row }">
              {{ getGoodsTypeText(row.goodsType) }}
            </template>
          </el-table-column>
          <el-table-column prop="relationId" label="关联ID" width="120" />
          <el-table-column prop="goodsNum" label="数量" width="80" align="center" />
          <el-table-column prop="amount" label="金额" width="100" align="right">
            <template #default="{ row }">
              ¥{{ formatPrice(row.amount) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 适用门店 -->
      <el-card v-if="groupBuyDetail.stores?.length > 0" class="info-card" header="适用门店">
        <el-table :data="groupBuyDetail.stores" border>
          <el-table-column prop="storeName" label="门店名称" min-width="200" />
          <el-table-column prop="provinceCode" label="省份编码" width="120" />
          <el-table-column prop="cityCode" label="城市编码" width="120" />
        </el-table>
      </el-card>

      <!-- 说明信息 -->
      <el-card class="info-card" header="说明信息">
        <div class="description-content">
          <div class="description-item">
            <label>购买须知：</label>
            <div class="description-text">
              {{ groupBuyDetail.remark || '暂无' }}
            </div>
          </div>
          <div class="description-item">
            <label>说明：</label>
            <div class="description-text">
              {{ groupBuyDetail.description || '暂无' }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据为空 -->
    <div v-else class="empty-container">
      <el-empty description="未找到团购信息" />
    </div>
  </div>
</template>
≈

<style scoped>
.group-buy-detail-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.loading-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  min-width: 100px;
  color: #606266;
  font-weight: 500;
  margin-right: 8px;
}

.price-text {
  font-weight: 500;
}

.sell-price {
  color: #f56c6c;
}

.limit-count {
  color: #909399;
  font-size: 12px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.image-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-item label {
  color: #606266;
  font-weight: 500;
}

.detail-image {
  width: 200px;
  height: 150px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.no-image {
  color: #c0c4cc;
  font-style: italic;
}

.description-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.description-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.description-item label {
  color: #606266;
  font-weight: 500;
}

.description-text {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .image-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
  }

  .page-header h2 {
    text-align: left;
  }
}
</style>
