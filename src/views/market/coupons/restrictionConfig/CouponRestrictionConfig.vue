<script setup lang="ts">
// import type { CouponRestrictionParams } from '@/api/modules/market/coupons/index'

// import { CouponType } from '@/views/market/coupons/baseConfig/types'
import type { CreateRestrictionParams, ExhibitionRestrictionType, FilmVersion, GoodsRestrictionType, ShowRestrictionType, TicketRestrictionType } from './types/index'
import { ElButton, ElMessage, ElMessageBox, ElText } from 'element-plus'
// API导入

import { computed, onMounted, ref } from 'vue'
import { createCouponRestriction, getCouponRestriction, updateCouponRestriction } from '@/api/modules/market/coupons/index'
import { CouponType, UseOnType } from '../baseConfig/types'
// 组件导入
import TicketRestrictionComponent from './components/TicketRestriction.vue'
// 常量导入
import {
  DEFAULT_EXHIBITION_RESTRICTION,
  DEFAULT_GOODS_RESTRICTION,
  DEFAULT_SHOW_RESTRICTION,
  DEFAULT_TICKET_RESTRICTION,
} from './constants'

defineOptions({
  name: 'CouponRestrictionConfig',
})

// Ensure couponType is always a number with a valid default
const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
  couponType: 1,
})

const emit = defineEmits<Emits>()

const pageMode = ref('add')

// ==================== Props & Emits ====================

interface Props {
  couponId: string
  useOn: UseOnType // 适用商品：影票0，卖品1，演出2，展览3;
  couponType: number | undefined
  pageMode?: 'add' | 'edit' | 'view'
}

interface Emits {
  (e: 'submit', data: any): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
  (e: 'close'): void
}

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)
// 初始化状态
const initialized = ref(false)

// 各类型限制数据
const ticketRestriction = ref<TicketRestrictionType[]>([])
const showRestriction = ref<ShowRestrictionType>({ showScope: 0, shows: [] })
const goodsRestriction = ref<GoodsRestrictionType>({ cinemas: [], goods: [], goodsScope: 0, goodsTypeIds: [] })
const exhibitionRestriction = ref<ExhibitionRestrictionType>({ exhibitHouses: [], exhibitScope: 0, exhibits: [] })

// 是否只读模式
const isReadonly = computed(() => props.pageMode === 'view')

// ==================== 业务逻辑 ====================

/**
 * 加载限制规则数据
 */
async function loadRestrictionData() {
  if (!props.couponId) {
    return
  }
  switch (props.useOn) {
    case UseOnType.MOVIE_TICKET:
      initTicketRestriction()
      break
    // case 1:
    //   initGoodsRestriction()
    //   break
    // case 2:
    //   initShowRestriction()
    //   break
    // case 3:
    //   initExhibitionRestriction()
    //   break
  }
}

// 初始化电影票限制
async function initTicketRestriction() {
  try {
    loading.value = true
    const response = await getCouponRestriction({
      couponId: props.couponId,
      useOn: props.useOn,
    }) as any
    if (response.code !== 0) {
      ElMessage.error('获取优惠券限制规则失败')
      return
    }
    const { data } = response
    // console.log(data)
    if (data.ticketRestrictions && data.ticketRestrictions.length > 0) {
      pageMode.value = 'edit'
      ticketRestriction.value = data.ticketRestrictions
    }
    else {
      pageMode.value = 'add'
      ticketRestriction.value = [JSON.parse(JSON.stringify(DEFAULT_TICKET_RESTRICTION))]
    }
    console.log('初始化完成', ticketRestriction.value)
  }
  catch (error) {
    console.error('加载限制规则失败:', error)
    ElMessage.error('加载限制规则失败')
  }
  finally {
    loading.value = false
    initialized.value = true
  }
}

/**
 * 添加电影票限制
 */
function addTicketRestriction() {
  ticketRestriction.value.push(JSON.parse(JSON.stringify(DEFAULT_TICKET_RESTRICTION)))
}

/**
 * 构建提交数据
 */
function buildSubmitData() {
  const submitData: CreateRestrictionParams = {
    couponId: props.couponId,
    restrictionIds: ticketRestriction.value.map(item => item.id) as string[],
  }

  // 根据类型添加对应的限制数据
  switch (props.useOn) {
    case 0: // 影票
      // if (ticketRestriction.value.length > 0) {
      //   submitData.ticketRestriction = {
      //     ...ticketRestriction.value[0],
      //   }
      //   submitData.restrictionId = ticketRestriction.value[0].id
      // }
      submitData.ticketRestrictions = ticketRestriction.value
      break
    case 1: // 卖品
      submitData.goodsRestriction = { ...goodsRestriction.value }
      break
    case 2: // 演出
      submitData.showRestriction = { ...showRestriction.value }
      break
    case 3: // 展览
      submitData.exhibitionRestriction = { ...exhibitionRestriction.value }
      break
  }

  return submitData
}

/**
 * 验证表单数据
 */
function validateForm(): boolean {
  // 基础验证
  if (!props.couponId) {
    ElMessage.error('优惠券ID不能为空')
    return false
  }

  // 根据类型进行特定验证
  switch (props.useOn) {
    case 0: // 影票
      // 确保有数据可验证
      if (ticketRestriction.value.length === 0) {
        ElMessage.error('影票限制数据不完整')
        return false
      }

      for (const ticketData of ticketRestriction.value) {
        // 验证影院限制
        if (ticketData.cinemaRestriction?.cinemaScope !== 0
          && ticketData.cinemaRestriction?.cinemas?.length === 0) {
          ElMessage.error('请选择适用的影院')
          return false
        }

        // 验证影片限制
        if (ticketData.filmRestriction?.filmScope === 1
          && ticketData.filmRestriction?.films?.length === 0) {
          ElMessage.error('请选择适用的影片')
          return false
        }

        // 验证时段限制
        if (ticketData.periodRestriction?.periodScope === 1
          && ticketData.periodRestriction?.periods?.length === 0) {
          ElMessage.error('请添加时段限制')
          return false
        }

        // 验证版本限制
        if (ticketData.filmVersionRestriction?.versionScope === 1
          && ticketData.filmVersionRestriction?.versions?.length === 0) {
          ElMessage.error('请选择适用的影片版本')
          return false
        }
      }
      break

    case 2: // 演出
      if (showRestriction.value.showScope !== 0
        && showRestriction.value.shows.length === 0) {
        ElMessage.error('请选择适用的演出')
        return false
      }
      break

    case 3: // 展览
      if (exhibitionRestriction.value.exhibitScope !== 0
        && exhibitionRestriction.value.exhibitHouses.length === 0
        && exhibitionRestriction.value.exhibits.length === 0) {
        ElMessage.error('请至少选择展览场馆或具体展览')
        return false
      }
      break
  }

  return true
}

/**
 * 提交表单
 */
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  try {
    loading.value = true
    const submitData = buildSubmitData()

    await ElMessageBox.confirm('确定提交吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    if (pageMode.value === 'add') {
      const response = await createCouponRestriction(submitData) as any
      if (response.code === 0) {
        ElMessage.success('创建限制规则成功')
        emit('success', response.data)
      }
      else {
        ElMessage.error(response.msg || '创建限制规则失败')
      }
    }
    else {
      // 编辑模式
      const response = await updateCouponRestriction(submitData) as any
      if (response.code === 0) {
        ElMessage.success('更新限制规则成功')
        emit('success', response.data)
      }
      else {
        ElMessage.error(response.msg || '更新限制规则失败')
      }
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  }
  finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
function handleReset() {
  switch (props.useOn) {
    case 0:
      // 使用深拷贝确保对象结构完整，并确保是数组格式
      ticketRestriction.value = [JSON.parse(JSON.stringify(DEFAULT_TICKET_RESTRICTION))]
      break
    case 1:
      goodsRestriction.value = JSON.parse(JSON.stringify(DEFAULT_GOODS_RESTRICTION))
      break
    case 2:
      showRestriction.value = JSON.parse(JSON.stringify(DEFAULT_SHOW_RESTRICTION))
      break
    case 3:
      exhibitionRestriction.value = JSON.parse(JSON.stringify(DEFAULT_EXHIBITION_RESTRICTION))
      break
  }
  ElMessage.info('表单已重置')
}

/**
 * 取消操作
 */
function handleCancel() {
  emit('cancel')
}

// ==================== 监听器 ====================

// 监听couponId变化，重新加载数据
// watch(() => props.couponId, (newId) => {
//   if (newId && props.pageMode === 'edit') {
//     loadRestrictionData()
//   }
// }, { immediate: true })

onMounted(() => {
  loadRestrictionData()
})

// ==================== 暴露方法 ====================

defineExpose({
  handleSubmit,
  handleReset,
  validateForm,
  buildSubmitData,
})
</script>

<template>
  <div class="min-h-[80vh] flex flex-col rounded-lg bg-white">
    <div class="mb-6 border-b-2 border-blue-500 pb-4">
      <h2 class="mb-2 text-xl text-gray-900 font-semibold">
        优惠券限制规则配置
      </h2>
      <p class="text-sm text-gray-600">
        根据优惠券适用商品类型设置相应的限制规则
      </p>
    </div>

    <div v-loading="loading" class="mb-5 min-h-[400px] flex-1 border border-gray-200 rounded-lg bg-white p-5">
      <!-- 根据useOn类型显示不同的限制配置组件，仅在初始化完成后渲染 -->
      <TicketRestrictionComponent v-if="initialized && props.useOn === 0 && ticketRestriction.length > 0" v-model="ticketRestriction" :coupon-type="props.couponType" :readonly="isReadonly" />
      <!-- 如果是电影券并且是多对一券则限制增加一条规则按钮 -->
      <ElButton v-if="props.couponType === CouponType.MULTI_TO_ONE && props.useOn === 0" type="primary" @click="addTicketRestriction">
        增加一条规则
      </ElButton>
      <!--      <GoodsRestriction -->
      <!--        v-else-if="initialized && useOn === 1" -->
      <!--        v-model="goodsRestriction" -->
      <!--        :readonly="isReadonly" -->
      <!--      /> -->

      <!--      <ShowRestriction -->
      <!--        v-else-if="initialized && useOn === 2" -->
      <!--        v-model="showRestriction" -->
      <!--        :readonly="isReadonly" -->
      <!--      /> -->

      <!--      <ExhibitionRestriction -->
      <!--        v-else-if="initialized && useOn === 3" -->
      <!--        v-model="exhibitionRestriction" -->
      <!--        :readonly="isReadonly" -->
      <!--      /> -->
    </div>

    <!-- 操作按钮 -->
    <div
      v-if="initialized"
      class="sticky bottom-0 z-10 flex justify-center gap-4 border-t border-gray-200 bg-white p-5"
    >
      <ElButton @click="handleReset">
        重置
      </ElButton>
      <ElButton @click="handleCancel">
        取消
      </ElButton>
      <ElButton type="primary" :loading="loading" @click="handleSubmit">
        {{ pageMode === 'add' ? '创建限制规则' : '保存修改' }}
      </ElButton>
    </div>
  </div>
</template>
