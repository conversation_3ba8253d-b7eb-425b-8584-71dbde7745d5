/**
 * 优惠券限制条件常量定义
 */

import { FilmScopeType, PeriodScopeType, ScopeType, VersionScopeType, WeekDay } from '../types'

// ==================== 范围选项 ====================

/** 通用范围选项 */
export const SCOPE_OPTIONS = [
  { label: '全部', value: ScopeType.ALL },
  { label: '指定', value: ScopeType.SPECIFIED },
  { label: '排除', value: ScopeType.EXCLUDED },
]

/** 影片范围选项 */
export const FILM_SCOPE_OPTIONS = [
  { label: '全部影片', value: FilmScopeType.ALL },
  { label: '指定影片', value: FilmScopeType.SPECIFIED },
  { label: '最低价影片', value: FilmScopeType.LOWEST_PRICE },
]

/** 时段范围选项 */
export const PERIOD_SCOPE_OPTIONS = [
  { label: '全部时段', value: PeriodScopeType.ALL },
  { label: '指定时段', value: PeriodScopeType.SPECIFIED },
]

/** 版本范围选项 */
export const VERSION_SCOPE_OPTIONS = [
  { label: '全部影片版本', value: VersionScopeType.ALL },
  { label: '指定影片版本', value: VersionScopeType.SPECIFIED },
]

// ==================== 星期选项 ====================

/** 星期选项 */
export const WEEK_DAY_OPTIONS = [
  { label: '周日', value: WeekDay.SUNDAY },
  { label: '周一', value: WeekDay.MONDAY },
  { label: '周二', value: WeekDay.TUESDAY },
  { label: '周三', value: WeekDay.WEDNESDAY },
  { label: '周四', value: WeekDay.THURSDAY },
  { label: '周五', value: WeekDay.FRIDAY },
  { label: '周六', value: WeekDay.SATURDAY },
]

// ==================== 映射对象 ====================

/** 范围类型映射 */
export const SCOPE_TYPE_MAP = {
  [ScopeType.ALL]: '全部',
  [ScopeType.SPECIFIED]: '指定',
  [ScopeType.EXCLUDED]: '排除',
} as const

/** 影片范围类型映射 */
export const FILM_SCOPE_TYPE_MAP = {
  [FilmScopeType.ALL]: '全部影片',
  [FilmScopeType.SPECIFIED]: '指定影片',
  [FilmScopeType.LOWEST_PRICE]: '最低价影片',
} as const

/** 星期映射 */
export const WEEK_DAY_MAP = {
  [WeekDay.SUNDAY]: '周日',
  [WeekDay.MONDAY]: '周一',
  [WeekDay.TUESDAY]: '周二',
  [WeekDay.WEDNESDAY]: '周三',
  [WeekDay.THURSDAY]: '周四',
  [WeekDay.FRIDAY]: '周五',
  [WeekDay.SATURDAY]: '周六',
} as const

// ==================== 默认值 ====================

/** 影票限制默认值 */
export const DEFAULT_TICKET_RESTRICTION = {
  couponId: '',
  id: '',
  cinemaRestriction: {
    cinemaScope: ScopeType.ALL,
    cinemas: [],
  },
  filmRestriction: {
    filmScope: FilmScopeType.ALL,
    films: [],
  },
  periodRestriction: {
    periodScope: PeriodScopeType.ALL,
    periods: [],
  },
  filmVersionRestriction: {
    versionScope: VersionScopeType.ALL,
    versions: [],
  },
}

/** 演出限制默认值 */
export const DEFAULT_SHOW_RESTRICTION = {
  showScope: ScopeType.ALL,
  shows: [],
}

/** 卖品限制默认值 */
export const DEFAULT_GOODS_RESTRICTION = {
  goodsScope: ScopeType.ALL,
  cinemas: [],
  goodsTypeIds: [],
  goods: [],
}

/** 展览限制默认值 */
export const DEFAULT_EXHIBITION_RESTRICTION = {
  exhibitScope: ScopeType.ALL,
  exhibitHouses: [],
  exhibits: [],
}

// ==================== 时间相关常量 ====================

/** 时间格式化选项 */
export const TIME_FORMAT_OPTIONS = {
  HOUR_MINUTE: 'HH:mm',
  FULL_TIME: 'YYYY-MM-DD HH:mm:ss',
  DATE_ONLY: 'YYYY-MM-DD',
}

// ==================== 工具函数 ====================

/**
 * 将时间数字转换为显示格式
 * @param timeNum 时间数字，如 2030
 * @returns 格式化时间，如 "20:30"
 */
export function formatTimeNumber(timeNum: number): string {
  const timeStr = timeNum.toString().padStart(4, '0')
  const hour = timeStr.slice(0, 2)
  const minute = timeStr.slice(2, 4)
  return `${hour}:${minute}`
}

/**
 * 将显示格式时间转换为数字
 * @param timeStr 时间字符串，如 "20:30"
 * @returns 时间数字，如 2030
 */
export function parseTimeString(timeStr: string): number {
  const [hour, minute] = timeStr.split(':')
  return Number.parseInt(hour) * 100 + Number.parseInt(minute)
}

/**
 * 获取范围类型描述
 * @param scopeType 范围类型
 * @returns 描述文字
 */
export function getScopeTypeDesc(scopeType: number): string {
  return SCOPE_TYPE_MAP[scopeType as ScopeType] || '未知'
}

/**
 * 获取星期描述
 * @param day 星期数字
 * @returns 星期描述
 */
export function getWeekDayDesc(day: number): string {
  return WEEK_DAY_MAP[day as WeekDay] || '未知'
}
