# 优惠券限制条件配置功能

基于API文档中的"获取优惠券限制规则详情"和"创建优惠券限制规则"接口，实现了完整的优惠券限制条件配置功能。

## 🎯 功能概述

根据不同的优惠券类型（影票、卖品、演出、展览），动态加载相应的限制条件配置组件，提供灵活且类型安全的配置界面。

## 📁 文件结构

```
src/views/coupons/restriction/
├── types/
│   └── index.ts                    # 类型定义
├── constants/
│   └── index.ts                    # 常量定义
├── api/
│   └── index.ts                    # API接口和模拟数据
├── components/
│   ├── TicketRestriction.vue       # 影票限制配置组件
│   ├── ShowRestriction.vue         # 演出限制配置组件
│   ├── GoodsRestriction.vue        # 卖品限制配置组件
│   └── ExhibitionRestriction.vue   # 展览限制配置组件
├── CouponRestrictionConfig.vue     # 主配置组件
├── CouponRestrictionDialog.vue     # 对话框组件
└── README.md                       # 本文档
```

## 🚀 核心特性

### 1. **类型驱动设计**
- 完全基于API文档的数据结构
- TypeScript类型安全
- 智能的类型推导和验证

### 2. **组件化架构**
- 根据优惠券类型动态加载对应组件
- 高度可复用的子组件设计
- 清晰的组件职责分离

### 3. **数据结构对齐**
- 严格按照API文档格式
- 自动数据验证和转换
- 完整的错误处理机制

### 4. **用户体验优化**
- 响应式设计，适配各种设备
- 智能表单验证和提示
- 流畅的交互动画效果

## 📊 支持的限制类型

### 1. **影票限制 (useOn = 0)**
- **影院限制**: 全部/指定/排除影院
  - **影厅精选**: 选择影院后可进一步选择具体影厅 ⭐
- **影片限制**: 全部影片/指定影片/最低价影片
- **时段限制**: 全部时段/指定时段（支持多个时段配置）
- **版本限制**: 全部版本/指定版本（2D、3D、IMAX等）

### 2. **卖品限制 (useOn = 1)**
- **卖品范围**: 全部/指定/排除卖品
- **影院选择**: 可选择适用的影院
- **分类限制**: 按卖品分类进行限制
- **具体卖品**: 选择具体的卖品项目

### 3. **演出限制 (useOn = 2)**
- **演出范围**: 全部/指定/排除演出
- **演出选择**: 支持多选演出项目
- **场次信息**: 显示演出的场次详情

### 4. **展览限制 (useOn = 3)**
- **展览范围**: 全部/指定/排除展览
- **场馆选择**: 选择展览场馆和展厅
- **展览选择**: 选择具体的展览项目

## 🔧 使用方式

### 基本使用

```vue
<template>
  <CouponRestrictionConfig
    :coupon-id="couponId"
    :use-on="useOn"
    :page-mode="pageMode"
    @submit="handleSubmit"
    @success="handleSuccess"
    @cancel="handleCancel"
  />
</template>

<script setup>
import CouponRestrictionConfig from '@/views/coupons/restrictionConfig/CouponRestrictionConfig.vue'

const couponId = ref('coupon_001')
const useOn = ref(0) // 0:影票 1:卖品 2:演出 3:展览
const pageMode = ref('add') // add:新增 edit:编辑 view:查看

function handleSubmit(data) {
  console.log('提交数据:', data)
}

function handleSuccess(data) {
  console.log('操作成功:', data)
}
</script>
```

### 外部控制

```vue
<template>
  <CouponRestrictionConfig ref="configRef" />
  <ElButton @click="submitForm">外部提交</ElButton>
</template>

<script setup>
const configRef = ref()

function submitForm() {
  configRef.value?.handleSubmit()
}

function resetForm() {
  configRef.value?.handleReset()
}

function validateForm() {
  return configRef.value?.validateForm()
}

function getData() {
  return configRef.value?.buildSubmitData()
}
</script>
```

## 📋 API接口

### 获取限制规则详情

```typescript
interface GetRestrictionParams {
  couponId: string
  useOn: number  // 0:影票 1:卖品 2:演出 3:展览
}

// 调用示例
const response = await getRestriction({
  couponId: 'coupon_001',
  useOn: 0
})
```

### 创建限制规则

```typescript
interface CreateRestrictionParams {
  couponId: string
  ticketRestriction?: TicketRestriction      // 影票限制
  showRestriction?: ShowRestriction          // 演出限制
  goodsRestriction?: GoodsRestriction        // 卖品限制
  exhibitionRestriction?: ExhibitionRestriction // 展览限制
}

// 调用示例
const response = await createRestriction({
  couponId: 'coupon_001',
  ticketRestriction: {
    // 影票限制配置
  }
})
```

## 🎨 数据结构示例

### 影票限制数据结构

```json
{
  "couponId": "coupon_001",
  "ticketRestriction": {
    "couponId": "coupon_001",
    "id": "restriction_001",
    "cinemaRestriction": {
      "cinemaScope": 1,
      "cinemas": [
        {
          "cinemaId": "cinema_001",
          "cinemaName": "万达影城（CBD店）",
          "selectedHalls": ["hall_001", "hall_003"],
          "halls": [
            {
              "hallId": "hall_001",
              "hallName": "1号厅"
            },
            {
              "hallId": "hall_002",
              "hallName": "2号厅"
            },
            {
              "hallId": "hall_003",
              "hallName": "IMAX厅"
            }
          ]
        }
      ]
    },
    "filmRestriction": {
      "filmScope": 1,
      "films": [
        {
          "filmId": 1001,
          "filmName": "流浪地球3"
        }
      ]
    },
    "periodRestriction": {
      "periodScope": 1,
      "periods": [
        {
          "num": 1,
          "day": 1,
          "start": 1800,
          "end": 2200
        }
      ]
    },
    "filmVersionRestriction": {
      "versionScope": 1,
      "versions": [
        {
          "filmVersion": "IMAX 3D",
          "versionId": "version_imax_3d"
        }
      ]
    }
  }
}
```

### 卖品限制数据结构

```json
{
  "couponId": "coupon_001",
  "goodsRestriction": {
    "goodsScope": 1,
    "cinemas": [
      {
        "cinemaId": "cinema_001",
        "cinemaName": "万达影城（CBD店）"
      }
    ],
    "goodsTypeIds": [1, 2],
    "goods": [
      {
        "goodsId": "goods_001",
        "goodsName": "爆米花（大）"
      }
    ]
  }
}
```

## 🔍 验证规则

### 影票限制验证
- 指定影院时必须选择至少一个影院
- 指定影片时必须选择至少一个影片
- 指定时段时必须添加至少一个时段
- 指定版本时必须选择至少一个版本

### 卖品限制验证
- 指定/排除卖品时必须至少选择一种限制条件
- 可以同时配置影院、分类、具体卖品

### 演出限制验证
- 指定/排除演出时必须选择至少一个演出

### 展览限制验证
- 指定/排除展览时必须至少选择场馆或具体展览

## 🎯 影厅选择功能

### 特色功能：二级选择结构

影票限制支持**影院 → 影厅**的二级选择结构，实现精确到影厅级别的限制控制。

#### 使用场景

1. **精确限制**：某优惠券只能在万达影城的IMAX厅使用
2. **部分限制**：某优惠券在多个影院的特定影厅使用
3. **全影院限制**：选择影院但不选择具体影厅（表示全部影厅）

#### 交互流程

```
选择影院范围 → 指定影院 → 选择具体影院 → 选择该影院的影厅
```

#### 数据结构

```typescript
interface Cinema {
  cinemaId: string
  cinemaName: string
  selectedHalls?: string[]  // 选中的影厅ID列表
  halls?: Hall[]           // 该影院的所有影厅
}
```

详细说明请参考：`HALL_SELECTION_GUIDE.md`

## 🎯 最佳实践

### 1. **组件使用**
```vue
<!-- 推荐：明确指定所有必要属性 -->
<CouponRestrictionConfig
  :coupon-id="couponId"
  :use-on="useOn"
  :page-mode="pageMode"
  @submit="handleSubmit"
  @success="handleSuccess"
/>

<!-- 不推荐：缺少必要属性 -->
<CouponRestrictionConfig />
```

### 2. **数据处理**
```typescript
// 推荐：使用类型安全的数据处理
function handleSubmit(data: CreateRestrictionParams) {
  // 数据已经是正确的格式，可直接提交
  submitToAPI(data)
}

// 不推荐：手动构建数据结构
function handleSubmit(formData: any) {
  const data = {
    couponId: formData.id,
    // 手动映射字段...
  }
}
```

### 3. **错误处理**
```typescript
// 推荐：完整的错误处理
try {
  const response = await createRestriction(data)
  if (response.code === 0) {
    ElMessage.success('创建成功')
  } else {
    ElMessage.error(response.msg)
  }
} catch (error) {
  console.error('创建失败:', error)
  ElMessage.error('网络错误，请重试')
}
```

## 📱 响应式设计

- **桌面端**: 网格布局，多列显示
- **平板端**: 自适应列数，保持可读性
- **移动端**: 单列布局，垂直排列

## 🔧 扩展指南

### 添加新的限制类型

1. **定义类型**
```typescript
// types/index.ts
export interface NewRestriction {
  newScope: number
  newItems: NewItem[]
}
```

2. **创建组件**
```vue
<!-- components/NewRestriction.vue -->
<template>
  <div class="new-restriction">
    <!-- 限制配置UI -->
  </div>
</template>
```

3. **集成到主组件**
```vue
<!-- CouponRestrictionConfig.vue -->
<NewRestriction 
  v-else-if="useOn === 4"
  v-model="newRestriction"
/>
```

### 自定义验证规则

```typescript
function validateForm(): boolean {
  // 基础验证
  if (!props.couponId) {
    ElMessage.error('优惠券ID不能为空')
    return false
  }
  
  // 自定义验证逻辑
  if (customCondition) {
    ElMessage.error('自定义验证失败')
    return false
  }
  
  return true
}
```

## 🚀 使用方式

在优惠券列表页面中点击"编辑条件"按钮即可使用限制条件配置功能。

## 📞 技术支持

如有问题或建议，请：

1. 检查控制台错误信息
2. 确认数据格式是否正确
3. 查看相关文档说明
4. 联系开发团队
