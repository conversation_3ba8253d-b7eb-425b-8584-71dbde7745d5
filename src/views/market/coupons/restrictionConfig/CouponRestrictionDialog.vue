<script setup lang="ts">
// 类型导入
import type { CreateRestrictionParams, RestrictionResponse } from './types'
import { ElDialog } from 'element-plus'

import { computed, ref, watch } from 'vue'

// 导入主配置组件
import CouponRestrictionConfig from './CouponRestrictionConfig.vue'

defineOptions({
  name: 'CouponRestrictionDialog',
})

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
  title: '',
  width: '90%',
  destroyOnClose: true,
  closeOnClickModal: false,
})

const emit = defineEmits<Emits>()

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  couponId: string
  useOn: number // 适用商品：影票0，卖品1，演出2，展览3
  pageMode?: 'add' | 'edit' | 'view'
  title?: string
  width?: string | number
  destroyOnClose?: boolean
  closeOnClickModal?: boolean
  restrictionData?: RestrictionResponse
  couponType?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: CreateRestrictionParams): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
  (e: 'close'): void
}

// ==================== 响应式数据 ====================

// 配置组件引用
const configRef = ref()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 动态标题
const dialogTitle = computed(() => {
  return props.title || ''

  const typeMap = {
    0: '影票',
    1: '卖品',
    2: '演出',
    3: '展览',
  }

  const typeName = typeMap[props.useOn as keyof typeof typeMap] || '优惠券'

  switch (props.pageMode) {
    case 'add': return `创建${typeName}限制条件`
    case 'edit': return `编辑${typeName}限制条件`
    case 'view': return `查看${typeName}限制条件`
    default: return `${typeName}限制条件配置`
  }
})

// 是否只读模式
const isReadonly = computed(() => props.pageMode === 'view')

// ==================== 事件处理 ====================

/**
 * 处理表单提交
 */
function handleSubmit(data: CreateRestrictionParams) {
  emit('submit', data)
}

/**
 * 处理成功操作
 */
function handleSuccess(data: any) {
  emit('success', data)
}

/**
 * 处理取消操作
 */
function handleCancel() {
  emit('cancel')
  dialogVisible.value = false
}

/**
 * 处理对话框关闭
 */
function handleClose() {
  console.log('对话框关闭')
  emit('close')
}

/**
 * 处理对话框打开前的操作
 */
function handleOpen() {
  // 对话框打开时的逻辑
}

/**
 * 处理对话框关闭后的操作
 */
function handleClosed() {
  // 对话框关闭后的清理逻辑
  console.log('对话框已关闭')
  configRef.value?.handleReset()
}

/**
 * 外部调用：提交表单
 */
function submit() {
  return configRef.value?.handleSubmit()
}

/**
 * 外部调用：重置表单
 */
function reset() {
  return configRef.value?.handleReset()
}

/**
 * 外部调用：验证表单
 */
function validate() {
  return configRef.value?.validateForm()
}

/**
 * 外部调用：获取表单数据
 */
function getFormData() {
  return configRef.value?.buildSubmitData()
}

// ==================== 监听器 ====================

// 监听对话框显示状态，在打开时加载数据
watch(() => props.modelValue, (visible) => {
  if (visible && props.pageMode === 'edit' && props.restrictionData) {
    // 编辑模式下预填充数据的逻辑可以在这里处理
    console.log('编辑模式，预填充数据:', props.restrictionData)
  }
})

// ==================== 暴露方法 ====================

// onMounted(() => {
//   console.log('使用条件编辑', { ...props })
//   loadRestrictionData()
// })
defineExpose({
  submit,
  reset,
  validate,
  getFormData,
})
</script>

<template>
  <ElDialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="width"
    :destroy-on-close="destroyOnClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="!isReadonly"
    :show-close="true"
    draggable
    align-center
    class="coupon-restriction-dialog"
    @open="handleOpen"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 对话框内容 -->
    <div class="dialog-content">
      <CouponRestrictionConfig
        ref="configRef"
        :coupon-id="couponId"
        :use-on="useOn"
        :page-mode="pageMode"
        :coupon-type="props.couponType"
        @submit="handleSubmit"
        @success="handleSuccess"
        @cancel="handleCancel"
        @close="handleCancel"
      />
    </div>
  </ElDialog>
</template>
