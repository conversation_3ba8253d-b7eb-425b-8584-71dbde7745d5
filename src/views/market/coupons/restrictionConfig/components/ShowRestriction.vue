<script setup lang="ts">
// 类型导入
import type { Show, ShowRestriction } from '../types'
import { ElFormItem, ElOption, ElRadio, ElRadioGroup, ElSelect, ElText } from 'element-plus'

import { computed, onMounted, ref } from 'vue'

// 常量导入
import { SCOPE_OPTIONS } from '../constants'

defineOptions({
  name: 'ShowRestriction',
})

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    showScope: 0,
    shows: [],
  }),
})

const emit = defineEmits<Emits>()

// API导入
// import { getShowList } from '@/api/modules/market/coupons'

function getShowList() {
  console.log('获取演出列表')
}

// ==================== Props & Emits ====================

interface Props {
  modelValue?: ShowRestriction
}

interface Emits {
  (e: 'update:modelValue', value: ShowRestriction): void
}

// ==================== 响应式数据 ====================

// 选项数据
const showOptions = ref<Show[]>([])

// 加载状态
const loading = ref(false)

// 表单数据计算属性
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// ==================== 业务逻辑 ====================

/**
 * 加载演出列表
 */
async function loadShowList() {
  try {
    loading.value = true
    const response = await getShowList()
    showOptions.value = response.data
  }
  catch (error) {
    console.error('加载演出列表失败:', error)
  }
  finally {
    loading.value = false
  }
}

/**
 * 处理演出范围变化
 */
function handleShowScopeChange() {
  if (formData.value.showScope === 0) {
    formData.value.shows = []
  }
}

// ==================== 生命周期 ====================

onMounted(() => {
  loadShowList()
})
</script>

<template>
  <div class="show-restriction">
    <div class="section-title">
      <h3>演出限制条件</h3>
      <p>设置演出优惠券的使用限制条件</p>
    </div>

    <div class="restriction-sections">
      <!-- 演出限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>演出限制</h4>
        </div>

        <ElFormItem label="演出范围">
          <ElRadioGroup
            v-model="formData.showScope"
            @change="handleShowScopeChange"
          >
            <ElRadio
              v-for="option in SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem
          v-if="formData.showScope !== 0"
          label="选择演出"
        >
          <ElSelect
            v-model="formData.shows"
            placeholder="请选择演出"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :loading="loading"
            style="width: 100%"
            value-key="showId"
          >
            <ElOption
              v-for="show in showOptions"
              :key="show.showId"
              :label="`${show.showName} - ${show.cinemaName}`"
              :value="show"
            >
              <div class="show-option">
                <div class="show-name">
                  {{ show.showName }}
                </div>
                <div class="show-venue">
                  {{ show.cinemaName }}
                </div>
              </div>
            </ElOption>
          </ElSelect>
        </ElFormItem>

        <!-- 已选演出展示 -->
        <div v-if="formData.shows.length > 0" class="selected-shows">
          <ElText type="info" size="small">
            已选择演出：
          </ElText>
          <div class="show-list">
            <div
              v-for="show in formData.shows"
              :key="show.showId"
              class="show-item"
            >
              <div class="show-info">
                <div class="show-name">
                  {{ show.showName }}
                </div>
                <div class="show-venue">
                  {{ show.cinemaName }}
                </div>
                <div v-if="show.sessions && show.sessions.length > 0" class="show-sessions">
                  场次：{{ show.sessions.length }} 场
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.show-restriction {
  width: 100%;
}

.section-title {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.restriction-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.restriction-section {
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
}

.section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 演出选项样式 */
.show-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.show-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.show-venue {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 已选演出样式 */
.selected-shows {
  margin-top: 16px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.show-list {
  margin-top: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.show-item {
  padding: 12px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.show-info .show-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.show-info .show-venue {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-bottom: 4px;
}

.show-info .show-sessions {
  font-size: 12px;
  color: var(--el-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .restriction-section {
    padding: 16px;
  }

  .show-list {
    grid-template-columns: 1fr;
  }

  :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
.restriction-section-animation {
  animation: fade-in-up 0.3s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
