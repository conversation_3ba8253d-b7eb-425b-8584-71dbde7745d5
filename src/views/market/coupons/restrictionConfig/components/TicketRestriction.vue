<script setup lang="ts">
import type { CouponType } from '../../baseConfig/types'
// 类型导入
import type { Cinema, FilmVersion, Period, TicketRestrictionType } from '../types'
import { Delete, InfoFilled, Plus } from '@element-plus/icons-vue'
import {
  ElButton,
  ElFormItem,
  ElIcon,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElSelect,
  ElText,
  ElTimePicker,
} from 'element-plus'

import { computed, onMounted, ref } from 'vue'

import cinemaSelector from '@/components/cinemaSelector/index.vue'

// 导入自定义组件

import { filmVersionList } from '@/config'
// 常量导入
import {
  FILM_SCOPE_OPTIONS,
  formatTimeNumber,
  parseTimeString,
  PERIOD_SCOPE_OPTIONS,
  SCOPE_OPTIONS,
  VERSION_SCOPE_OPTIONS,
  WEEK_DAY_OPTIONS,
} from '../constants'

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ([]),
  couponType: 0,
})

const emit = defineEmits<Emits>()

// ==================== Props & Emits ====================

interface Props {
  modelValue?: TicketRestrictionType[]
  couponType?: CouponType
}

interface Emits {
  (e: 'update:modelValue', value: TicketRestrictionType[]): void
}

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref({
  cinema: false,
  film: false,
  version: false,
})

// 表单数据计算属性
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// ==================== 业务逻辑 ====================

/**
 * 添加时间段
 */
function addPeriod(index: number) {
  const newPeriod: Period = {
    num: formData.value[index].periodRestriction.periods.length + 1,
    day: 1, // 默认周一
    start: 1800, // 默认18:00
    end: 2200, // 默认22:00
  }

  formData.value[index].periodRestriction.periods.push(newPeriod)
}

/**
 * 删除时间段
 */
function removePeriod(index: number, periodIndex: number) {
  formData.value[index].periodRestriction.periods.splice(periodIndex, 1)
  // 重新编号
  formData.value[index].periodRestriction.periods.forEach((period, idx) => {
    period.num = idx + 1
  })
}

/**
 * 处理影院范围变化
 */
function handleCinemaScopeChange(index: number) {
  if (formData.value[index].cinemaRestriction.cinemaScope === 0) {
    formData.value[index].cinemaRestriction.cinemas = []
  }
}

// 添加一个新的方法来处理影厅选择变化
function handleHallSelectionChange(index: number, cinemaIndex: number, selectedCodes: { hallId: string, hallName: string }[], halls: any[]) {
  // 更新对应影院的 selectedHalls 数据，包含 hallId 和 hallName
  const cinema = formData.value[index].cinemaRestriction.cinemas[cinemaIndex] as Cinema
  cinema.halls = halls.map(hall => ({
    hallId: hall.code,
    hallName: hall.name,
  }))
  console.log('hall change', cinemaIndex, selectedCodes, halls)
}

/**
 * 处理影片范围变化
 */
function handleFilmScopeChange(index: number) {
  if (formData.value[index].filmRestriction.filmScope === 0 || formData.value[index].filmRestriction.filmScope === 2) {
    formData.value[index].filmRestriction.films = []
  }
}

/**
 * 处理时段范围变化
 */
function handlePeriodScopeChange(index: number) {
  console.log('period scope change', index, formData.value[index])
  if (formData.value[index].periodRestriction.periodScope === 0) {
    formData.value[index].periodRestriction.periods = []
  }
}

/**
 * 处理版本范围变化
 */
function handleVersionScopeChange(index: number) {
  if (formData.value[index]?.filmVersionRestriction.versionScope === 0) {
    formData.value[index].filmVersionRestriction.versions = []
  }
}

/**
 * 格式化时间显示
 */
function formatPeriodTime(timeNum: number): string {
  return formatTimeNumber(timeNum)
}

/**
 * 更新时间段时间
 */
function updatePeriodTime(index: number, periodIndex: number, field: 'start' | 'end', timeStr: string) {
  formData.value[index].periodRestriction.periods[periodIndex][field] = parseTimeString(timeStr)
}

/**
 * 删除限制条件
 */
function removeTicketRestriction(index: number) {
  formData.value.splice(index, 1)
}

// ==================== 生命周期 ====================

onMounted(() => {
  // loadCinemaList()
  // loadFilmList()
  // loadVersionList()
})

function filmVersionRestrictionCop(index: number) {
  return computed({
    set: (value) => {
      console.log('set', value, index)
      formData.value[index].filmVersionRestriction.versions = value.map((item) => {
        const ver = filmVersionList.find(v => v.value === item)
        return {
          versionId: ver?.value,
          filmVersion: ver?.value,
        }
      }) as FilmVersion[]
    },
    get: () => {
      return formData.value[index].filmVersionRestriction.versions.map(item => item.filmVersion)
    },
  })
}
</script>

<template>
  <div class="w-full px-4 py-3">
    <div v-for="(config, _index) in modelValue" :key="_index" class="mb-6 w-full">
      <div class="mb-6 border-b-2 border-primary pb-3">
        <h3 class="m-0 mb-1 text-xl text-foreground font-semibold">
          影票限制条件
        </h3>
        <p class="m-0 text-sm text-muted-foreground">
          设置影票优惠券的使用限制条件
        </p>
        <div class="h-10 flex items-center justify-between">
          <span class="text-sm text-foreground font-medium">限制条件 {{ _index + 1 }}</span>
          <ElButton type="danger" size="small" @click="removeTicketRestriction(_index)">
            删除
          </ElButton>
        </div>
      </div>
      <div class="flex flex-col gap-6">
        <pre class="h-128 overflow-auto rounded-md bg-muted p-4">
          {{ config }}
        </pre>
        <!-- 影院限制 -->
        <div class="border border-border rounded-lg bg-background p-5 shadow-sm transition-all duration-300 hover:shadow-md">
          <div class="mb-4 border-b border-border pb-2">
            <h4 class="m-0 text-base text-foreground font-semibold">
              影院限制
            </h4>
          </div>

          <ElFormItem label="影院范围">
            <ElRadioGroup v-model="config.cinemaRestriction.cinemaScope" class="flex flex-wrap gap-4 sm:gap-6" @change="handleCinemaScopeChange(_index)">
              <ElRadio v-for="option in SCOPE_OPTIONS" :key="option.value" :value="option.value">
                {{ option.label }}
              </ElRadio>
            </ElRadioGroup>
          </ElFormItem>

          <ElFormItem v-if="config.cinemaRestriction.cinemaScope > 0" label="选择影院">
            <cinemaSelector v-model="config.cinemaRestriction.cinemas" />
          </ElFormItem>

          <!-- 影厅选择 -->
          <div v-if="config.cinemaRestriction.cinemas.length > 0 && config.cinemaRestriction.cinemaScope" class="mt-4 rounded-md bg-muted p-4">
            <ElText type="info" size="small" class="mb-4 flex items-center gap-1 text-muted-foreground font-medium">
              <ElIcon>
                <InfoFilled />
              </ElIcon>
              影厅选择（可选择具体影厅进行更精确的限制）
            </ElText>

            <div class="flex flex-col gap-4">
              <div v-for="(cinema, cindex) in config.cinemaRestriction.cinemas" :key="cinema.cinemaId" class="border border-border rounded-md bg-background p-4 shadow-sm">
                <!--              {{ cinema }} -->
                <hall-selector
                  :model-value="cinema.halls" :cinema-name="cinema.cinemaName" :cinema-id="cinema.cinemaId"
                  @change="(selectedCodes, halls) => handleHallSelectionChange(_index, cindex, selectedCodes, halls)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 影片限制 -->
        <div class="border border-border rounded-lg bg-background p-5 shadow-sm transition-all duration-300 hover:shadow-md">
          <div>
            <h4 class="m-0 text-base text-foreground font-semibold">
              影片限制
            </h4>
          </div>

          <ElFormItem label="影片范围">
            <ElRadioGroup v-model="config.filmRestriction.filmScope" class="flex flex-wrap gap-4 sm:gap-6" @change="handleFilmScopeChange(_index)">
              <ElRadio v-for="option in FILM_SCOPE_OPTIONS" :key="option.value" :value="option.value">
                {{ option.label }}
              </ElRadio>
            </ElRadioGroup>
          </ElFormItem>

          <ElFormItem v-if="config.filmRestriction.filmScope === 1" label="选择影片">
            <film-selector v-model="config.filmRestriction.films" />
          </ElFormItem>
        </div>

        <!-- 时段限制 -->
        <div class="border border-border rounded-lg bg-background p-5 shadow-sm transition-all duration-300 hover:shadow-md">
          <div>
            <h4 class="m-0 text-base text-foreground font-semibold">
              时段限制
            </h4>
          </div>

          <ElFormItem label="时段范围">
            <ElRadioGroup v-model="config.periodRestriction.periodScope" class="flex flex-wrap gap-4 sm:gap-6" @change="handlePeriodScopeChange(_index)">
              <ElRadio v-for="option in PERIOD_SCOPE_OPTIONS" :key="option.value" :value="option.value">
                {{ option.label }}
              </ElRadio>
            </ElRadioGroup>
          </ElFormItem>

          <div v-if="config.periodRestriction.periodScope === 1" class="mt-4 rounded-md bg-muted p-4">
            <div class="mb-4 flex items-center justify-between">
              <ElText class="text-foreground">
                时段配置
              </ElText>
              <ElButton type="primary" size="small" :icon="Plus" class="transition-all duration-300 hover:shadow-md" @click="addPeriod(_index)">
                添加时段
              </ElButton>
            </div>

            <div class="flex flex-wrap">
              <div v-for="(period, periodIndex) in config.periodRestriction.periods" :key="periodIndex" class="mb-4 mr-1 min-w-[200px] w-[calc(24.6%)] animate-[fade-in-up_0.3s_ease-out] border border-border rounded-md bg-background p-4 shadow-sm">
                <div class="mb-3 flex items-center justify-between border-b border-border pb-2">
                  <ElText class="text-foreground">
                    时段 {{ period.num }}
                  </ElText>
                  <ElButton type="danger" size="small" text :icon="Delete" class="transition-all duration-300 hover:text-red-700" @click="removePeriod(_index, periodIndex)">
                    删除
                  </ElButton>
                </div>

                <div class="grid grid-cols-1 gap-4">
                  <ElFormItem label="星期">
                    <ElSelect v-model="period.day" style="width: 100%;">
                      <ElOption v-for="day in WEEK_DAY_OPTIONS" :key="day.value" :label="day.label" :value="day.value" />
                    </ElSelect>
                  </ElFormItem>

                  <ElFormItem label="开始时间">
                    <ElTimePicker
                      :model-value="formatPeriodTime(period.start)"
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%;"
                      @update:model-value="(val) => updatePeriodTime(_index, periodIndex, 'start', val)"
                    />
                  </ElFormItem>

                  <ElFormItem label="结束时间">
                    <ElTimePicker
                      :model-value="formatPeriodTime(period.end)"
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 100%;"
                      @update:model-value="(val) => updatePeriodTime(_index, periodIndex, 'end', val)"
                    />
                  </ElFormItem>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 影片版本限制 -->
        <div class="restriction-section border border-border rounded-lg bg-background p-5 shadow-sm transition-all duration-300 hover:shadow-md">
          <div class="section-header">
            <h4 class="m-0 text-base text-foreground font-semibold">
              影片版本限制
            </h4>
          </div>

          <ElFormItem label="版本范围">
            <ElRadioGroup v-model="config.filmVersionRestriction.versionScope" class="flex flex-wrap gap-4 sm:gap-6" @change="handleVersionScopeChange(_index)">
              <ElRadio v-for="option in VERSION_SCOPE_OPTIONS" :key="option.value" :value="option.value">
                {{ option.label }}
              </ElRadio>
            </ElRadioGroup>
          </ElFormItem>

          <ElFormItem v-if="config.filmVersionRestriction.versionScope === 1" label="选择版本">
            <ElSelect
              v-model="filmVersionRestrictionCop(_index).value" placeholder="请选择影片版本" multiple collapse-tags
              collapse-tags-tooltip :loading="loading.version" style="width: 100%;" value-key="versionId"
              :max-collapse-tags="8"
            >
              <ElOption
                v-for="version in filmVersionList" :key="version.value" :label="version.name"
                :value="version.value"
              />
            </ElSelect>
          </ElFormItem>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- 已使用 Tailwind CSS 类名，移除原有样式 -->
