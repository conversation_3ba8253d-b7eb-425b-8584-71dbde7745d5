
### 获取优惠券限制规则详情
接口权限：/market/coupon/restriction/get

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_coupon/getRestriction


描述：获取优惠券限制规则详情
接口权限：/market/coupon/restriction/get

ContentType：`application/json`

#### 请求头

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| adminToken | 是 |  | wec76PvDb8fjXUrnRQon9e77r1BuoGsMyRiVsghCBZ4SjbkvojTNcWWMLYuSDgco9InJMOhy74+JcUhVLIp+KEjaz9840jtRV6pl6KqYUlKFA0Eldp1AlRKTywKhBBm3/fBjaAIhbf9MetK4FTGgn//7vvqB+Wbd8xJd/OKfWDkK/y4CXZivPDf5Op1O2rFF |
| accessToken | 是 |  | 7znHgC63zNtn+i4wiCjqc05m/XlmSUL5pdCmK/OvVQEfXJDjpckvmKiCJZNXJhJqZK1bmceSalUMmoPCFchlC+SZ2yIo0DCob+1UIi1mo4iI42juRyhY+qFrZbw9x6KIass57++T+2+NVWweBFKpIF28BemShYxg+ax5uCABYR/Cs824TA2qPPZSlZbS83VM |

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| couponId | string | 否 | - | 优惠券 ID |  |
| useOn | int32 | 否 | - | 适用商品：影票0，卖品1，演出2，展览3 | 0 |

#### 请求示例

```
{
    "couponId": "",
    "useOn": 0
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| couponId | string | 否 | - | 优惠券 ID |  |
| ticketRestrictions | array | 否 |  | 影票优惠券 限制规则 |  |
|   └ couponId | string | 否 | - | 优惠券 ID |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ cinemaRestriction | object | 否 |  | 影院限制 |  |
|     └ cinemaScope | int32 | 否 | - | 影院范围：全部0，指定1，排除2 | 0 |
|     └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|       └ cinemaId | string | 否 | - | 影院ID |  |
|       └ cinemaName | string | 否 | - | 影院名称 |  |
|       └ halls | array | 否 |  | 影厅 |  |
|         └ hallId | string | 否 | - | 影厅ID |  |
|         └ hallName | string | 否 | - | 影厅名称 |  |
|   └ filmRestriction | object | 否 |  | 影片限制 |  |
|     └ filmScope | int32 | 否 | - | 适用影片：全部影片0，指定影片1，最低价影片2 | 0 |
|     └ films | array | 否 |  | 优惠券适用影片 |  |
|       └ filmId | string | 否 | - | 影片ID |  |
|       └ filmCode | string | 否 | - | 影片国家编码 |  |
|       └ filmName | string | 否 | - | 影片名称 |  |
|   └ periodRestriction | object | 否 |  | 时间段限制 |  |
|     └ periodScope | int32 | 否 | - | 适用时段：全部时段0，指定时段1 | 0 |
|     └ periods | array | 否 |  | 优惠券适用时段 |  |
|       └ day | int32 | 否 | - | 星期几 | 0 |
|       └ start | int64 | 否 | - | 开始时间：2030（晚上8点半） | 0 |
|       └ end | int64 | 否 | - | 结束时间：2400（晚上12点） | 0 |
|   └ filmVersionRestriction | object | 否 |  | 电影版本限制 |  |
|     └ versionScope | int32 | 否 | - | 适用影片版本：全部影片版本0，指定影片版本1 | 0 |
|     └ versions | array | 否 |  | 优惠券适用影片版本 |  |
|       └ filmVersion | string | 否 | - | 影片版本 |  |
|       └ versionId | string | 否 | - | 版本ID |  |
| showRestriction | object | 否 |  | 演出优惠券 限制规则 |  |
|   └ showScope | int32 | 否 | - | 适用演出: 全部 0，指定 1，排除 2 | 0 |
|   └ shows | array | 否 |  | 优惠券适用影院对象 |  |
|     └ cinemaId | int64 | 否 | - | 演出场馆id | 0 |
|     └ cinemaName | string | 否 | - | 演出场馆 |  |
|     └ showName | string | 否 | - | 演出名称 |  |
|     └ showId | string | 否 | - | 演出Id |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ sessions | array | 否 |  | 场次 |  |
|       └ id | string | 否 | - | No comments found. |  |
|       └ showScheduleId | int32 | 否 | - | 演出场次ID | 0 |
|       └ startTime | int64 | 否 | - | 开始时间 | 0 |
|       └ endTime | int64 | 否 | - | 结束时间 | 0 |
| goodsRestriction | object | 否 |  | 卖品优惠券 限制规则 |  |
|   └ goodsScope | int32 | 否 | - | 适用卖品分类: 全部0，指定1，排除2 | 0 |
|   └ cinemas | array | 否 |  | 优惠券适用影院对象 |  |
|     └ cinemaId | string | 否 | - | 影院ID |  |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ halls | array | 否 |  | 影厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|   └ goodsTypeIds | array | 否 | - | 优惠券适用商品类型 | 0,0 |
|   └ goods | array | 否 |  | 优惠券适用指定商品 |  |
|     └ id | int64 | 否 | - |  | 0 |
|     └ cinemaId | int64 | 否 | - | 影院ID | 0 |
|     └ cinemaName | string | 否 | - | 影院名称 |  |
|     └ goodsId | string | 否 | - | 卖品ID |  |
|     └ goodsName | string | 否 | - | 卖品名称 |  |
| exhibitionRestriction | object | 否 |  | 展览优惠券 限制规则 |  |
|   └ exhibitScope | int32 | 否 | - | 展览范围：全部 0，指定 1，排除 2 | 0 |
|   └ exhibitHouses | array | 否 |  | 优惠券适用（场馆&展厅） |  |
|     └ halls | array | 否 |  | 展厅 |  |
|       └ hallId | string | 否 | - | 影厅ID |  |
|       └ hallName | string | 否 | - | 影厅名称 |  |
|     └ houseId | string | 否 | - | No comments found. |  |
|   └ exhibits | array | 否 |  | 优惠券适用（展品） |  |
|     └ exhibitId | int64 | 否 | - | 展览ID | 0 |
|     └ exhibitName | string | 否 | - | 展览名称 |  |

#### 响应示例

```
{
    "couponId": "",
    "ticketRestrictions": [
        {
            "couponId": "",
            "id": "",
            "cinemaRestriction": {
                "cinemaScope": 0,
                "cinemas": [
                    {
                        "cinemaId": "",
                        "cinemaName": "",
                        "halls": [
                            {
                                "hallId": "",
                                "hallName": ""
                            }
                        ]
                    }
                ]
            },
            "filmRestriction": {
                "filmScope": 0,
                "films": [
                    {
                        "filmId": "",
                        "filmCode": "",
                        "filmName": ""
                    }
                ]
            },
            "periodRestriction": {
                "periodScope": 0,
                "periods": [
                    {
                        "day": 0,
                        "start": 0,
                        "end": 0
                    }
                ]
            },
            "filmVersionRestriction": {
                "versionScope": 0,
                "versions": [
                    {
                        "filmVersion": "",
                        "versionId": ""
                    }
                ]
            }
        }
    ],
    "showRestriction": {
        "showScope": 0,
        "shows": [
            {
                "cinemaId": 0,
                "cinemaName": "",
                "showName": "",
                "showId": "",
                "id": "",
                "sessions": [
                    {
                        "id": "",
                        "showScheduleId": 0,
                        "startTime": 0,
                        "endTime": 0
                    }
                ]
            }
        ]
    },
    "goodsRestriction": {
        "goodsScope": 0,
        "cinemas": [
            {
                "cinemaId": "",
                "cinemaName": "",
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ]
            }
        ],
        "goodsTypeIds": [
            0,
            0
        ],
        "goods": [
            {
                "id": 0,
                "cinemaId": 0,
                "cinemaName": "",
                "goodsId": "",
                "goodsName": ""
            }
        ]
    },
    "exhibitionRestriction": {
        "exhibitScope": 0,
        "exhibitHouses": [
            {
                "halls": [
                    {
                        "hallId": "",
                        "hallName": ""
                    }
                ],
                "houseId": ""
            }
        ],
        "exhibits": [
            {
                "exhibitId": 0,
                "exhibitName": ""
            }
        ]
    }
}
```

#### 错误码

无
