# 优惠券管理模块

基于API文档 `src/api/modules/coupons/优惠券.md` 实现的完整优惠券管理系统。

## 📁 目录结构

```
src/views/coupons/
├── components/              # 组件目录
│   ├── CouponSearchForm.vue    # 搜索表单组件
│   ├── CouponTable.vue         # 数据表格组件
│   ├── CouponDetailDialog.vue  # 详情对话框组件
│   └── CouponDetailCard.vue    # 详情卡片组件
├── detail/                  # 详情页面
│   └── index.vue              # 详情页面主文件
├── types/                   # 类型定义
│   └── index.ts               # TypeScript类型定义
├── utils/                   # 工具函数
│   └── index.ts               # 工具函数集合
├── constants/               # 常量定义
│   └── index.ts               # 常量和枚举定义
├── index.vue               # 主页面
├── README.md              # 本文档
├── MIGRATION.md           # 迁移说明
└── test.md               # 测试指南
```

## 🚀 功能特性

### 核心功能
- ✅ **优惠券列表**: 分页查询、搜索过滤、状态管理
- ✅ **优惠券详情**: 完整的优惠券信息展示
- ✅ **状态管理**: 启用/禁用优惠券状态切换
- ✅ **数据导出**: CSV格式数据导出
- ✅ **响应式设计**: 完美适配各种设备

### 高级功能
- 🔄 **实时搜索**: 支持多条件组合搜索
- 📊 **数据可视化**: 使用率进度条、统计卡片
- 🎨 **现代化UI**: Element Plus + 自定义样式
- 🔒 **类型安全**: 完整的TypeScript支持
- 📱 **移动端优化**: 响应式布局设计

## 📋 API接口

基于 `src/api/modules/coupons/优惠券.md` 文档实现的接口：

### 主要接口
- `GET /adm_coupon/list` - 获取优惠券列表
- `GET /adm_coupon/detail/:id` - 获取优惠券详情
- `POST /adm_coupon/add` - 创建优惠券
- `POST /adm_coupon/save` - 更新优惠券
- `POST /adm_coupon/status` - 状态变更
- `POST /adm_coupon/bind` - 绑定优惠券
- `POST /adm_coupon/delete` - 删除优惠券

### 扩展接口
- `POST /adm_coupon/export` - 导出数据
- `POST /adm_coupon/codes` - 获取券码列表
- `POST /adm_coupon/statistics` - 获取统计信息

## 🎯 数据类型

### 优惠券主要字段
```typescript
interface Coupon {
  id: string                    // 优惠券ID
  name: string                  // 优惠券名称
  useOn: UseOnType             // 适用类型：影票0，卖品1，演出2，展览3，电商4
  reduction: ServiceFeeReduction // 服务费减免：减免0，不减免1
  userServiceFee: number       // 用户服务费(分)
  couponType: CouponType       // 优惠券类型：满减0，减至1，通兑2，折扣3，多对一4
  useStatus: UseStatus         // 使用状态：关闭0，开启1，草稿2
  generateStatus: GenerateStatus // 生成状态：未生成0，已生成1
  bindCount: number            // 已绑定数量
  usedCount: number            // 已使用数量
  createTime: number           // 创建时间
  updatedTime: number          // 更新时间
  // ... 其他字段
}
```

### 枚举类型
```typescript
enum UseOnType {
  MOVIE_TICKET = 0,  // 影票
  GOODS = 1,         // 卖品
  SHOW = 2,          // 演出
  EXHIBITION = 3,    // 展览
  ECOMMERCE = 4,     // 电商
}

enum CouponType {
  FULL_REDUCTION = 0, // 满减
  REDUCTION_TO = 1,   // 减至
  UNIVERSAL = 2,      // 通兑
  DISCOUNT = 3,       // 折扣
  MULTI_TO_ONE = 4,   // 多对一
}

enum UseStatus {
  DISABLED = 0,  // 关闭
  ENABLED = 1,   // 开启
  DRAFT = 2,     // 草稿
}
```

## 🛠️ 使用方法

### 1. 基本使用
```vue
<template>
  <div>
    <!-- 搜索表单 -->
    <CouponSearchForm
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
      @export="handleExport"
    />

    <!-- 数据表格 -->
    <CouponTable
      :data="tableData"
      :loading="loading"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
      @delete="handleDelete"
      @status-change="handleStatusChange"
    />

    <!-- 详情对话框 -->
    <CouponDetailDialog
      v-model="detailVisible"
      :coupon="currentCoupon"
      @edit="handleEdit"
      @export="handleExport"
      @share="handleShare"
    />
  </div>
</template>
```

### 2. 数据获取
```typescript
import { getCouponList } from '@/api/modules/coupons'
import { adaptCouponList } from './utils'

const fetchCouponList = async () => {
  const response = await getCouponList(params)
  const { data, code } = response
  
  if (code === 0) {
    const rawList = data.content || data.result || []
    tableData.value = adaptCouponList(rawList)
  }
}
```

### 3. 状态管理
```typescript
const handleStatusChange = async (coupon: Coupon, newValue: boolean) => {
  const newStatus = newValue ? 1 : 0
  
  await ElMessageBox.confirm(`确定要${newStatus === 1 ? '启用' : '禁用'}优惠券吗？`)
  
  const response = await changeCouponStatus({
    id: coupon.id,
    useStatus: newStatus,
  })
  
  if (response.data.code === 0) {
    ElMessage.success('状态更新成功')
    fetchCouponList()
  }
}
```

## 🎨 样式定制

### 主题色彩
```css
:root {
  --coupon-primary: #409eff;
  --coupon-success: #67c23a;
  --coupon-warning: #e6a23c;
  --coupon-danger: #f56c6c;
  --coupon-info: #909399;
}
```

### 组件样式
```css
/* 表格样式 */
.coupon-table {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片样式 */
.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 进度条样式 */
.el-progress-bar__inner {
  background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  animation: gradient 3s ease infinite;
}
```

## 📱 响应式设计

### 断点设置
- **桌面端**: >= 1200px
- **平板端**: 768px - 1199px
- **手机端**: < 768px

### 适配策略
```css
/* 桌面端 */
@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 平板端 */
@media (max-width: 1199px) and (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 手机端 */
@media (max-width: 767px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
```

## 🔧 工具函数

### 格式化函数
```typescript
// 金额格式化（分转元）
formatAmount(1000) // "¥10.00"

// 时间格式化
formatTime(1704067200000) // "2024-01-01 00:00:00"

// 使用率格式化
formatUsageRate(25, 100) // "25%"
```

### 描述获取函数
```typescript
// 获取状态描述
getUseStatusDesc(1) // "已开启"

// 获取类型描述
getCouponTypeDesc(0) // "满减券"

// 获取标签类型
getUseStatusTagType(1) // "success"
```

### 数据适配函数
```typescript
// 适配单个优惠券数据
const adaptedCoupon = adaptCouponData(rawCoupon)

// 批量适配优惠券列表
const adaptedList = adaptCouponList(rawList)
```

## 🧪 测试指南

详见 [test.md](./test.md) 文件。

## 📖 迁移指南

详见 [MIGRATION.md](./MIGRATION.md) 文件。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

**注意**: 本模块基于 Element Plus 和 Vue 3 Composition API 开发，请确保项目环境支持这些技术栈。
