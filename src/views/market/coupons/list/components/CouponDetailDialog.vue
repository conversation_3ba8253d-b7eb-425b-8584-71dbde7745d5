<script setup lang="ts">
import type { Coupon } from '../../baseConfig/types'
import { Download, Edit, Share } from '@element-plus/icons-vue'
import {
  ElButton,
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElDialog,
  ElDivider,
  ElProgress,
  ElTag,
  ElText,
} from 'element-plus'
import { computed } from 'vue'
import {
  formatAmount,
  formatTime,
  formatUsageRate,
  getCouponTypeDesc,
  getCouponTypeTagType,
  getGenerateStatusDesc,
  getGenerateStatusTagType,
  getServiceFeeReductionDesc,
  getServiceFeeReductionTagType,
  getUseOnDesc,
  getUseOnTagType,
  getUseStatusDesc,
  getUseStatusTagType,
} from '../../utils'

defineOptions({
  name: 'CouponDetailDialog',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  modelValue: boolean
  coupon: Coupon | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', coupon: Coupon): void
  (e: 'export', coupon: Coupon): void
  (e: 'share', coupon: Coupon): void
}

// ==================== 计算属性 ====================

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 使用率百分比
const usagePercentage = computed(() => {
  if (!props.coupon) { return 0 }
  const { usedCount = 0, bindCount = 0 } = props.coupon
  if (!bindCount) { return 0 }
  return Math.round((usedCount / bindCount) * 100)
})

// 状态颜色
const statusColor = computed(() => {
  if (!props.coupon) { return 'var(--el-color-info)' }
  return Number(props.coupon.useStatus) === 1 ? 'var(--el-color-success)' : 'var(--el-color-danger)'
})

// ==================== 事件处理 ====================

// 编辑
function handleEdit() {
  if (props.coupon) {
    emit('edit', props.coupon)
  }
}

// 导出
function handleExport() {
  if (props.coupon) {
    emit('export', props.coupon)
  }
}

// 分享
function handleShare() {
  if (props.coupon) {
    emit('share', props.coupon)
  }
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="coupon?.name || '优惠券详情'"
    width="900px"
    :close-on-click-modal="false"
  >
    <div v-if="coupon" class="coupon-detail">
      <!-- 头部统计卡片 -->
      <div class="grid grid-cols-1 mb-6 gap-4 md:grid-cols-3">
        <ElCard class="text-center" shadow="hover">
          <div class="mb-2 text-3xl font-bold" :style="{ color: statusColor }">
            {{ formatAmount(coupon.userServiceFee) }}
          </div>
          <div class="text-sm text-gray-500">
            用户服务费
          </div>
        </ElCard>

        <ElCard class="text-center" shadow="hover">
          <div class="mb-2 text-3xl text-blue-600 font-bold">
            {{ coupon.bindCount || 0 }}
          </div>
          <div class="text-sm text-gray-500">
            绑定数量
          </div>
        </ElCard>

        <ElCard class="text-center" shadow="hover">
          <div class="mb-2 text-3xl text-orange-600 font-bold">
            {{ coupon.usedCount || 0 }}
          </div>
          <div class="text-sm text-gray-500">
            使用数量
          </div>
        </ElCard>
      </div>

      <!-- 使用率进度条 -->
      <div class="mb-6">
        <div class="mb-2 flex items-center justify-between">
          <span class="text-sm text-gray-700 font-medium">使用率</span>
          <span class="text-sm text-gray-900 font-bold">
            {{ formatUsageRate(coupon.usedCount || 0, coupon.bindCount || 0) }}
          </span>
        </div>
        <ElProgress
          :percentage="usagePercentage"
          :stroke-width="12"
          :show-text="false"
          class="mb-2"
        />
        <div class="text-center text-xs text-gray-500">
          已使用 {{ coupon.usedCount || 0 }} / 已绑定 {{ coupon.bindCount || 0 }}
        </div>
      </div>

      <ElDivider />

      <!-- 基本信息 -->
      <div class="mb-6">
        <div class="mb-4 flex items-center justify-between">
          <span class="text-lg font-semibold">基本信息</span>
          <div class="flex space-x-2">
            <ElTag :type="getUseStatusTagType(Number(coupon.useStatus)) as any" size="large">
              {{ getUseStatusDesc(Number(coupon.useStatus)) }}
            </ElTag>
            <ElTag :type="getGenerateStatusTagType(Number(coupon.generateStatus)) as any" size="large">
              {{ getGenerateStatusDesc(Number(coupon.generateStatus)) }}
            </ElTag>
          </div>
        </div>

        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="优惠券ID">
            <ElText class="font-mono">
              {{ coupon.id }}
            </ElText>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="优惠券名称">
            <ElText class="font-medium">
              {{ coupon.name }}
            </ElText>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="适用类型">
            <ElTag :type="getUseOnTagType(Number(coupon.useOn)) as any" size="small">
              {{ getUseOnDesc(Number(coupon.useOn)) }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="优惠券类型">
            <ElTag :type="getCouponTypeTagType(Number(coupon.couponType)) as any" size="small">
              {{ getCouponTypeDesc(Number(coupon.couponType)) }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="用户服务费">
            <ElText class="text-green-600 font-medium">
              {{ formatAmount(coupon.userServiceFee) }}
            </ElText>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="服务费减免">
            <ElTag :type="getServiceFeeReductionTagType(Number(coupon.reduction)) as any" size="small">
              {{ getServiceFeeReductionDesc(Number(coupon.reduction)) }}
            </ElTag>
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>

      <!-- 规则信息 -->
      <div v-if="coupon.priceRule || coupon.channelRule || coupon.generateRule || coupon.settleRule || coupon.periodRule" class="mb-6">
        <div class="mb-4 text-lg font-semibold">
          规则信息
        </div>

        <ElDescriptions :column="1" border>
          <ElDescriptionsItem v-if="coupon.priceRule" label="价格规则">
            <pre class="text-sm">{{ JSON.stringify(coupon.priceRule, null, 2) }}</pre>
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="coupon.channelRule" label="渠道规则">
            <pre class="text-sm">{{ JSON.stringify(coupon.channelRule, null, 2) }}</pre>
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="coupon.generateRule" label="生成规则">
            <pre class="text-sm">{{ JSON.stringify(coupon.generateRule, null, 2) }}</pre>
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="coupon.settleRule" label="结算规则">
            <pre class="text-sm">{{ JSON.stringify(coupon.settleRule, null, 2) }}</pre>
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="coupon.periodRule" label="时间规则">
            <pre class="text-sm">{{ JSON.stringify(coupon.periodRule, null, 2) }}</pre>
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>

      <!-- 备注信息 -->
      <div v-if="coupon.note || coupon.remark" class="mb-6">
        <div class="mb-4 text-lg font-semibold">
          备注信息
        </div>

        <ElDescriptions :column="1" border>
          <ElDescriptionsItem v-if="coupon.note" label="备注">
            <ElText>{{ coupon.note }}</ElText>
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="coupon.remark" label="说明">
            <ElText>{{ coupon.remark }}</ElText>
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>

      <!-- 时间信息 -->
      <div class="mb-6">
        <div class="mb-4 text-lg font-semibold">
          时间信息
        </div>

        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="创建时间">
            <div class="flex items-center">
              <i class="i-ep:plus mr-2 text-blue-500" />
              {{ formatTime(coupon.createTime) }}
            </div>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="更新时间">
            <div class="flex items-center">
              <i class="i-ep:edit mr-2 text-green-500" />
              {{ formatTime(coupon.updatedTime) }}
            </div>
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="flex space-x-2">
          <ElButton :icon="Share" @click="handleShare">
            分享
          </ElButton>
          <ElButton :icon="Download" @click="handleExport">
            导出
          </ElButton>
        </div>
        <div class="flex space-x-2">
          <ElButton @click="dialogVisible = false">
            关闭
          </ElButton>
          <ElButton type="primary" :icon="Edit" @click="handleEdit">
            编辑
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.coupon-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.text-center {
  text-align: center;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-900 {
  color: #111827;
}

.text-blue-600 {
  color: #2563eb;
}

.text-orange-600 {
  color: #ea580c;
}

.text-green-600 {
  color: #16a34a;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

/* 卡片悬停效果 */
.grid > :deep(.el-card:hover) {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 6px;
  background: var(--el-fill-color-light);
}

:deep(.el-progress-bar__inner) {
  border-radius: 6px;
  background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 描述列表样式 */
:deep(.el-descriptions__body .el-descriptions__table) {
  border-radius: 6px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 500;
  background: var(--el-fill-color-lighter);
}

/* 代码块样式 */
pre {
  background: var(--el-fill-color-light);
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .grid-cols-1 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .coupon-detail {
    max-height: 80vh;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .flex {
    flex-direction: column;
    gap: 0.5rem;
  }

  .justify-between {
    justify-content: center;
  }
}
</style>
