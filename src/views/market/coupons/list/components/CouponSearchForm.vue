<script setup lang="ts">
// 类型导入
import type { CouponListParams } from '@/api/modules/market/coupons'
import { Download, Refresh, Search } from '@element-plus/icons-vue'
import {
  ElButton,
  ElCard,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElSelect,
} from 'element-plus'

import { ref, toRaw } from 'vue'

// 常量导入
import {
  COUPON_TYPE_OPTIONS,
  QUICK_DATE_OPTIONS,
  USE_ON_TYPE_OPTIONS,
  USE_STATUS_OPTIONS,
} from '../../baseConfig/constants'

defineOptions({
  name: 'CouponSearchForm',
})

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

interface Props {
  loading?: boolean
  modelValue: CouponListParams
}

interface Emits {
  (e: 'update:modelValue', params: CouponListParams): void
  (e: 'search', params: CouponListParams): void
  (e: 'reset'): void
  (e: 'export'): void
}

// ==================== 响应式数据 ====================

// 使用简单的方法来处理双向绑定，避免循环引用
function updateField(field: keyof CouponListParams, value: any) {
  const newData = { ...toRaw(props.modelValue) }
  newData[field] = value
  emit('update:modelValue', newData)
}

// 表单引用
const formRef = ref()

// 是否展开高级搜索
const isAdvanced = ref(false)

// ==================== 事件处理 ====================

// 搜索
function handleSearch() {
  const params = { ...props.modelValue }

  // 清理空值
  Object.keys(params).forEach((key) => {
    const value = params[key as keyof CouponListParams]
    if (value === '' || value === null || value === undefined) {
      delete params[key as keyof CouponListParams]
    }
  })

  emit('search', params)
}

// 重置
function handleReset() {
  formRef.value?.resetFields()

  // 重置为初始值，通过双向绑定更新父组件
  const resetData: CouponListParams = {
    id: '',
    name: '',
    useOn: undefined,
    couponType: undefined,
    useStatus: undefined,
    dateRange: undefined,
    cinemaId: '',
    channelCode: '',
  }

  emit('update:modelValue', resetData)
  emit('reset')
}

// 导出
function handleExport() {
  emit('export')
}

// 切换高级搜索
function toggleAdvanced() {
  isAdvanced.value = !isAdvanced.value
}

// 回车搜索
function handleKeyup(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleSearch()
  }
}
</script>

<template>
  <ElCard class="search-form-card" shadow="never">
    <ElForm
      ref="formRef"
      :model="modelValue"
      inline
      label-width="auto"
      class="search-form"
    >
      <!-- 基础搜索 -->
      <div class="search-row">
        <ElFormItem label="优惠券ID" prop="id">
          <ElInput
            :model-value="modelValue.id"
            placeholder="请输入优惠券ID"
            clearable
            style="width: 200px;"
            @keyup="handleKeyup"
            @input="(value) => updateField('id', value)"
          />
        </ElFormItem>

        <ElFormItem label="优惠券名称" prop="name">
          <ElInput
            :model-value="modelValue.name"
            placeholder="请输入优惠券名称"
            clearable
            style="width: 200px;"
            @keyup="handleKeyup"
            @input="(value) => updateField('name', value)"
          />
        </ElFormItem>

        <ElFormItem label="适用类型" prop="useOn">
          <ElSelect
            :model-value="modelValue.useOn"
            placeholder="请选择适用类型"
            clearable
            style="width: 150px;"
            @change="(value) => updateField('useOn', value)"
          >
            <ElOption
              v-for="option in USE_ON_TYPE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="优惠券类型" prop="couponType">
          <ElSelect
            :model-value="modelValue.couponType"
            placeholder="请选择优惠券类型"
            clearable
            style="width: 150px;"
            @change="(value) => updateField('couponType', value)"
          >
            <ElOption
              v-for="option in COUPON_TYPE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="使用状态" prop="useStatus">
          <ElSelect
            :model-value="modelValue.useStatus"
            placeholder="请选择使用状态"
            clearable
            style="width: 120px;"
            @change="(value) => updateField('useStatus', value)"
          >
            <ElOption
              v-for="option in USE_STATUS_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>
      </div>

      <!-- 高级搜索 -->
      <div v-show="isAdvanced" class="search-row advanced-row">
        <ElFormItem label="创建时间" prop="dateRange">
          <ElDatePicker
            :model-value="modelValue.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="QUICK_DATE_OPTIONS"
            style="width: 350px;"
            @change="(value) => updateField('dateRange', value)"
          />
        </ElFormItem>

        <ElFormItem label="影院ID" prop="cinemaId">
          <ElInput
            :model-value="modelValue.cinemaId"
            placeholder="请输入影院ID"
            clearable
            style="width: 150px;"
            @keyup="handleKeyup"
            @input="(value) => updateField('cinemaId', value)"
          />
        </ElFormItem>

        <ElFormItem label="渠道代码" prop="channelCode">
          <ElInput
            :model-value="modelValue.channelCode"
            placeholder="请输入渠道代码"
            clearable
            style="width: 150px;"
            @keyup="handleKeyup"
            @input="(value) => updateField('channelCode', value)"
          />
        </ElFormItem>
      </div>

      <!-- 操作按钮 -->
      <div class="search-actions">
        <ElButton
          type="primary"
          :icon="Search"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </ElButton>

        <ElButton
          :icon="Refresh"
          @click="handleReset"
        >
          重置
        </ElButton>

        <ElButton
          :icon="Download"
          @click="handleExport"
        >
          导出
        </ElButton>

        <ElButton
          type="info"
          text
          @click="toggleAdvanced"
        >
          {{ isAdvanced ? '收起' : '展开' }}
          <i :class="isAdvanced ? 'i-ep:arrow-up' : 'i-ep:arrow-down'" class="ml-1" />
        </ElButton>
      </div>
    </ElForm>
  </ElCard>
</template>

<style scoped>
.search-form-card {
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

.search-form {
  width: 100%;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.search-row:last-child {
  margin-bottom: 0;
}

.advanced-row {
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.search-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  border-radius: 6px;
}

/* 按钮样式 */
:deep(.el-button) {
  font-weight: 500;
  border-radius: 6px;
}

/* 图标样式 */
.i-ep-arrow-up,
.i-ep-arrow-down {
  font-size: 12px;
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (width <= 1200px) {
  .search-row {
    gap: 12px;
  }

  .search-actions {
    gap: 8px;
  }
}

@media (width <= 768px) {
  .search-row {
    flex-direction: column;
    gap: 12px;
  }

  .search-actions {
    flex-direction: column;
    align-items: stretch;
  }

  :deep(.el-form-item) {
    margin-right: 0;
  }

  .el-input,
  .el-select,
  .el-date-editor {
    width: 100% !important;
  }
}

/* 动画效果 */
.advanced-row-animation {
  animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 悬停效果 */
.search-form-card:hover {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  transition: box-shadow 0.3s ease;
}

/* 焦点样式 */
:deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-select:focus-within .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}
</style>
