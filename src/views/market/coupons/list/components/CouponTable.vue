<script setup lang="ts">
import type { Coupon } from '../../baseConfig/types'
import { ElProgress, ElSwitch, ElTable, ElTableColumn, ElTag, ElText } from 'element-plus'
import { ref } from 'vue'
import {
  formatAmount,
  formatTime,
  formatUsageRate,
  getCouponTypeDesc,
  getCouponTypeTagType,
  getGenerateStatusDesc,
  getGenerateStatusTagType,
  getServiceFeeReductionDesc,
  getServiceFeeReductionTagType,
  getUseOnDesc,
  getUseOnTagType,
} from '../../utils'

defineOptions({
  name: 'CouponTable',
})

withDefaults(defineProps<Props>(), {
  loading: false,
  showSelection: false,
})

const emit = defineEmits<Emits>()

interface Props {
  data: Coupon[]
  loading?: boolean
  showSelection?: boolean
}

interface Emits {
  (e: 'view-detail', coupon: Coupon): void
  (e: 'edit', coupon: Coupon, type: 'base' | 'condition'): void // 修复点：补充 edit 事件定义
  (e: 'delete', coupon: Coupon): void
  (e: 'status-change', coupon: Coupon, newValue: boolean): void
  (e: 'bind', coupon: Coupon): void
  (e: 'view-codes', coupon: Coupon): void
  (e: 'selection-change', selection: Coupon[]): void
}

// 开关加载状态，防止重复触发
const switchLoading = ref(false)

// ==================== 事件处理 ====================

// 查看详情
function handleViewDetail(coupon: Coupon) {
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('view-detail', coupon)
}

// 编辑主信息
function handleEditBaseConfig(coupon: Coupon) {
  emit('edit', coupon, 'base')
}

// 编辑条件配置
function handleEditConditionConfig(coupon: Coupon) {
  emit('edit', coupon, 'condition')
}

// 删除
function handleDelete(coupon: Coupon) {
  emit('delete', coupon)
}

// 状态切换点击处理
function handleStatusClick(coupon: Coupon) {
  // 如果正在加载中，忽略这次操作
  if (switchLoading.value) {
    return
  }

  // 计算新的状态值（切换当前状态）
  const currentStatus = Number(coupon.useStatus)
  const newValue = currentStatus !== 1

  // 设置加载状态
  switchLoading.value = true

  // 延迟一下再重置加载状态，防止快速连续触发
  setTimeout(() => {
    switchLoading.value = false
  }, 500)

  // 触发状态变化事件
  emit('status-change', coupon, newValue)
}

// 绑定
function handleBind(coupon: Coupon) {
  emit('bind', coupon)
}

// 查看券码
function handleViewCodes(coupon: Coupon) {
  emit('view-codes', coupon)
}

// 选择变化
function handleSelectionChange(selection: Coupon[]) {
  emit('selection-change', selection)
}

// 计算使用率百分比
function getUsagePercentage(usedCount: number, bindCount: number): number {
  if (!bindCount || bindCount === 0) { return 0 }
  return Math.round((usedCount / bindCount) * 100)
}
</script>

<template>
  <div class="coupon-table">
    <ElTable
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <ElTableColumn v-if="showSelection" type="selection" width="55" fixed="left" />

      <!-- 优惠券ID -->
      <ElTableColumn prop="id" label="优惠券ID" width="120" fixed="left">
        <template #default="{ row }">
          <ElText class="text-sm font-mono">
            {{ row.id }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 优惠券名称 -->
      <ElTableColumn prop="name" label="优惠券名称" width="200" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <ElText class="font-medium">
            {{ row.name }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 适用类型 -->
      <ElTableColumn label="适用类型" width="100" align="center">
        <template #default="{ row }">
          <ElTag
            :type="getUseOnTagType(Number(row.useOn)) as any"
            size="small"
          >
            {{ getUseOnDesc(Number(row.useOn)) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 优惠券类型 -->
      <ElTableColumn label="优惠券类型" width="120" align="center">
        <template #default="{ row }">
          <ElTag
            :type="getCouponTypeTagType(Number(row.couponType)) as any"
            size="small"
          >
            {{ getCouponTypeDesc(Number(row.couponType)) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 用户服务费 -->
      <ElTableColumn label="用户服务费" width="120" align="right">
        <template #default="{ row }">
          <ElText class="text-green-600 font-medium">
            {{ formatAmount(row.userServiceFee) }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 服务费减免 -->
      <ElTableColumn label="服务费减免" width="120" align="center">
        <template #default="{ row }">
          <ElTag
            :type="getServiceFeeReductionTagType(Number(row.reduction)) as any"
            size="small"
          >
            {{ getServiceFeeReductionDesc(Number(row.reduction)) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 绑定数量 -->
      <ElTableColumn label="绑定数量" width="100" align="center">
        <template #default="{ row }">
          <ElText class="text-blue-600 font-medium">
            {{ row.bindCount || 0 }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 使用数量 -->
      <ElTableColumn label="使用数量" width="100" align="center">
        <template #default="{ row }">
          <ElText class="text-orange-600 font-medium">
            {{ row.usedCount || 0 }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 使用率 -->
      <ElTableColumn label="使用率" width="120" align="center">
        <template #default="{ row }">
          <div class="usage-rate">
            <ElProgress
              :percentage="getUsagePercentage(row.usedCount || 0, row.bindCount || 0)"
              :stroke-width="6"
              :show-text="false"
              class="mb-1"
            />
            <ElText size="small" class="text-gray-500">
              {{ formatUsageRate(row.usedCount || 0, row.bindCount || 0) }}
            </ElText>
          </div>
        </template>
      </ElTableColumn>

      <!-- 使用状态 -->
      <ElTableColumn label="使用状态" width="100" align="center">
        <template #default="{ row }">
          <ElSwitch
            :model-value="row.useStatus"
            :active-value="1"
            :inactive-value="0"
            :disabled="switchLoading"
            @click="() => handleStatusClick(row)"
          />
        </template>
      </ElTableColumn>
      <!--      generateRule -->
      <!-- 生成数量方式 -->
      <ElTableColumn label="生成数量方式" width="220" align="center">
        <template #default="{ row }">
          <ElText size="small">
            <!--            {{ row.generateRule.generateType === 0 ? '按需生成' : '一次性生成' }} -->
            <ElText v-if="row.generateRule.generateType === 1" type="primary">
              一次性生成
              <ElTag size="small">
                {{ row.generateRule.num }}
              </ElTag> 张
            </ElText>
            <ElText v-if="row.generateRule.generateType === 0" type="warning">
              <ElTooltip content="最多生成">
                <template #content>
                  最多生成{{ row.generateRule.num }}张
                </template>
                需要时生成
              </ElTooltip>
              <!--              <ElTag size="small"> -->
              <!--                {{ row.generateRule.num }} -->
              <!--              </ElTag> 张 -->
            </ElText>
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 生成状态 -->
      <ElTableColumn label="生成状态" width="100" align="center">
        <template #default="{ row }">
          <ElTag
            :type="getGenerateStatusTagType(Number(row.generateStatus)) as any"
            size="small"
          >
            {{ getGenerateStatusDesc(Number(row.generateStatus)) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 创建时间 -->
      <ElTableColumn label="创建时间" width="160">
        <template #default="{ row }">
          <ElText size="small">
            {{ formatTime(row.createTime) }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 更新时间 -->
      <ElTableColumn label="更新时间" width="160">
        <template #default="{ row }">
          <ElText size="small">
            {{ formatTime(row.updatedTime) }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 操作列 -->
      <ElTableColumn label="操作" fixed="right">
        <template #default="{ row }">
          <ElDropdown trigger="click">
            <ElButton type="primary" link size="small">
              更多
              <fa-icon name="i-ep:arrow-down" />
            </ElButton>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem command="view" @click="handleViewDetail(row)">
                  <fa-icon name="i-ep:view" />
                  详情
                </ElDropdownItem>
                <ElDropdownItem command="edit" @click="handleEditBaseConfig(row)">
                  <fa-icon name="i-ep:edit" />
                  编辑主信息
                </ElDropdownItem>
                <ElDropdownItem command="config" @click="handleEditConditionConfig(row)">
                  <fa-icon name="i-ep:setting" />
                  编辑条件
                </ElDropdownItem>
                <ElDropdownItem command="bind" @click="handleBind(row)">
                  <fa-icon name="i-ep:link" />
                  绑定
                </ElDropdownItem>
                <ElDropdownItem command="codes" @click="handleViewCodes(row)">
                  <fa-icon name="i-ep:tickets" />
                  券码
                </ElDropdownItem>
                <ElDropdownItem command="delete" @click="handleDelete(row)">
                  <fa-icon name="i-ep:delete" />
                  删除
                </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<style scoped>
.coupon-table {
  overflow: hidden;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.usage-rate {
  width: 100%;
}

.font-mono {
  font-family: Monaco, Menlo, "Ubuntu Mono", monospace;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background: var(--el-fill-color-light);
}

:deep(.el-table th) {
  font-weight: 600;
  color: var(--el-text-color-primary);
  background: var(--el-fill-color-light);
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-table__row:hover) {
  background: var(--el-fill-color-lighter);
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

:deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #67c23a, #e6a23c, #f56c6c);
  border-radius: 3px;
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: var(--el-color-success);
  --el-switch-off-color: var(--el-color-info);
}

/* 按钮样式 */
:deep(.el-button.is-circle) {
  width: 28px;
  height: 28px;
  padding: 0;
}

/* 标签样式 */
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;
}

/* 响应式设计 */
@media (width <= 1200px) {
  .action-buttons {
    gap: 2px;
  }

  :deep(.el-button.is-circle) {
    width: 24px;
    height: 24px;
  }
}

@media (width <= 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}

/* 动画效果 */
.action-buttons .el-button {
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: scale(1.1);
}

/* 加载状态 */
:deep(.el-table__loading-wrapper) {
  background: rgb(255 255 255 / 80%);
  backdrop-filter: blur(2px);
}
</style>
