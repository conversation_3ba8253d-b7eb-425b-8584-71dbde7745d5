<script setup lang="ts">
import type { Coupon } from '../../baseConfig/types'
import { Download, Edit, Share } from '@element-plus/icons-vue'
import {
  ElButton,
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElProgress,
  ElTag,
  ElText,
} from 'element-plus'
import { computed } from 'vue'
import {
  formatAmount,
  formatTime,
  formatUsageRate,
  getCouponTypeDesc,
  getCouponTypeTagType,
  getGenerateStatusDesc,
  getGenerateStatusTagType,
  getServiceFeeReductionDesc,
  getServiceFeeReductionTagType,
  getUseOnDesc,
  getUseOnTagType,
  getUseStatusDesc,
  getUseStatusTagType,
} from '../../utils'

defineOptions({
  name: 'CouponDetailCard',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  coupon: Coupon
}

interface Emits {
  (e: 'edit', coupon: Coupon): void
  (e: 'export', coupon: Coupon): void
  (e: 'share', coupon: Coupon): void
}

// ==================== 计算属性 ====================

// 使用率百分比
const usagePercentage = computed(() => {
  const { usedCount = 0, bindCount = 0 } = props.coupon
  if (!bindCount) { return 0 }
  return Math.round((usedCount / bindCount) * 100)
})

// 状态颜色
const statusColor = computed(() => {
  return Number(props.coupon.useStatus) === 1 ? 'var(--el-color-success)' : 'var(--el-color-danger)'
})

// ==================== 事件处理 ====================

// 编辑
function handleEdit() {
  emit('edit', props.coupon)
}

// 导出
function handleExport() {
  emit('export', props.coupon)
}

// 分享
function handleShare() {
  emit('share', props.coupon)
}
</script>

<template>
  <div class="coupon-detail-card">
    <!-- 头部统计卡片 -->
    <div class="stats-grid">
      <ElCard class="stat-card" shadow="hover">
        <div class="stat-value" :style="{ color: statusColor }">
          {{ formatAmount(coupon.userServiceFee) }}
        </div>
        <div class="stat-label">
          用户服务费
        </div>
      </ElCard>

      <ElCard class="stat-card" shadow="hover">
        <div class="stat-value text-blue-600">
          {{ coupon.bindCount || 0 }}
        </div>
        <div class="stat-label">
          绑定数量
        </div>
      </ElCard>

      <ElCard class="stat-card" shadow="hover">
        <div class="stat-value text-orange-600">
          {{ coupon.usedCount || 0 }}
        </div>
        <div class="stat-label">
          使用数量
        </div>
      </ElCard>

      <ElCard class="stat-card" shadow="hover">
        <div class="stat-value text-purple-600">
          {{ formatUsageRate(coupon.usedCount || 0, coupon.bindCount || 0) }}
        </div>
        <div class="stat-label">
          使用率
        </div>
      </ElCard>
    </div>

    <!-- 使用率进度条 -->
    <ElCard class="progress-card" shadow="never">
      <div class="progress-header">
        <span class="progress-title">使用率详情</span>
        <span class="progress-value">
          {{ formatUsageRate(coupon.usedCount || 0, coupon.bindCount || 0) }}
        </span>
      </div>
      <ElProgress
        :percentage="usagePercentage"
        :stroke-width="16"
        :show-text="false"
        class="progress-bar"
      />
      <div class="progress-info">
        已使用 {{ coupon.usedCount || 0 }} / 已绑定 {{ coupon.bindCount || 0 }}
      </div>
    </ElCard>

    <!-- 基本信息 -->
    <ElCard class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">基本信息</span>
          <div class="status-tags">
            <ElTag :type="getUseStatusTagType(Number(coupon.useStatus)) as any" size="large">
              {{ getUseStatusDesc(Number(coupon.useStatus)) }}
            </ElTag>
            <ElTag :type="getGenerateStatusTagType(Number(coupon.generateStatus)) as any" size="large">
              {{ getGenerateStatusDesc(Number(coupon.generateStatus)) }}
            </ElTag>
          </div>
        </div>
      </template>

      <ElDescriptions :column="2" border>
        <ElDescriptionsItem label="优惠券ID">
          <ElText class="font-mono">
            {{ coupon.id }}
          </ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="优惠券名称" :span="3">
          <ElText class="text-lg font-medium">
            {{ coupon.name }}
          </ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="适用类型">
          <ElTag :type="getUseOnTagType(Number(coupon.useOn)) as any" size="small">
            {{ getUseOnDesc(Number(coupon.useOn)) }}
          </ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="优惠券类型">
          <ElTag :type="getCouponTypeTagType(Number(coupon.couponType)) as any" size="small">
            {{ getCouponTypeDesc(Number(coupon.couponType)) }}
          </ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户服务费">
          <ElText class="text-green-600 font-medium">
            {{ formatAmount(coupon.userServiceFee) }}
          </ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="服务费减免">
          <ElTag :type="getServiceFeeReductionTagType(Number(coupon.reduction)) as any" size="small">
            {{ getServiceFeeReductionDesc(Number(coupon.reduction)) }}
          </ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="创建时间">
          {{ formatTime(coupon.createTime) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="更新时间">
          {{ formatTime(coupon.updatedTime) }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElCard>

    <!-- 规则信息 -->
    <ElCard
      v-if="coupon.priceRule || coupon.channelRule || coupon.generateRule || coupon.settleRule || coupon.periodRule"
      class="info-card"
      shadow="never"
    >
      <template #header>
        <span class="card-title">规则信息</span>
      </template>

      <ElDescriptions :column="1" border>
        <ElDescriptionsItem v-if="coupon.priceRule" label="价格规则">
          <pre class="rule-code">{{ JSON.stringify(coupon.priceRule, null, 2) }}</pre>
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="coupon.channelRule" label="渠道规则">
          <pre class="rule-code">{{ JSON.stringify(coupon.channelRule, null, 2) }}</pre>
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="coupon.generateRule" label="生成规则">
          <pre class="rule-code">{{ JSON.stringify(coupon.generateRule, null, 2) }}</pre>
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="coupon.settleRule" label="结算规则">
          <pre class="rule-code">{{ JSON.stringify(coupon.settleRule, null, 2) }}</pre>
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="coupon.periodRule" label="时间规则">
          <pre class="rule-code">{{ JSON.stringify(coupon.periodRule, null, 2) }}</pre>
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElCard>

    <!-- 备注信息 -->
    <ElCard v-if="coupon.note || coupon.remark" class="info-card" shadow="never">
      <template #header>
        <span class="card-title">备注信息</span>
      </template>

      <ElDescriptions :column="1" border>
        <ElDescriptionsItem v-if="coupon.note" label="备注">
          <ElText>{{ coupon.note }}</ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="coupon.remark" label="说明">
          <ElText>{{ coupon.remark }}</ElText>
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElCard>

    <!-- 操作按钮 -->
    <ElCard class="action-card" shadow="never">
      <div class="action-buttons">
        <ElButton type="primary" :icon="Edit" @click="handleEdit">
          编辑优惠券
        </ElButton>
        <ElButton :icon="Download" @click="handleExport">
          导出数据
        </ElButton>
        <ElButton :icon="Share" @click="handleShare">
          分享优惠券
        </ElButton>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.coupon-detail-card {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  text-align: center;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--el-text-color-regular);
}

/* 进度卡片 */
.progress-card {
  border-radius: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.progress-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--el-color-primary);
}

.progress-bar {
  margin-bottom: 12px;
}

.progress-info {
  text-align: center;
  font-size: 0.875rem;
  color: var(--el-text-color-regular);
}

/* 信息卡片 */
.info-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.status-tags {
  display: flex;
  gap: 8px;
}

/* 操作卡片 */
.action-card {
  border-radius: 12px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* 规则代码样式 */
.rule-code {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

/* 工具类 */
.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.font-medium {
  font-weight: 500;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-blue-600 {
  color: #2563eb;
}

.text-orange-600 {
  color: #ea580c;
}

.text-purple-600 {
  color: #9333ea;
}

.text-green-600 {
  color: #16a34a;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 8px;
  background: var(--el-fill-color-light);
}

:deep(.el-progress-bar__inner) {
  border-radius: 8px;
  background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 描述列表样式 */
:deep(.el-descriptions__body .el-descriptions__table) {
  border-radius: 8px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 500;
  background: var(--el-fill-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .progress-header {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.coupon-detail-card-animation {
  animation: fade-in-up 0.6s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.info-card:hover,
.progress-card:hover,
.action-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}
</style>
