<script setup lang="ts">
import { ElDatePicker, ElFormItem, ElInputNumber, ElRadio, ElRadioGroup, ElText } from 'element-plus'
import { computed } from 'vue'

interface Props {
  validScope?: number
  startTime?: number
  endTime?: number
  overdueDay?: number
}

interface Emits {
  (e: 'update:validScope', value: number): void
  (e: 'update:startTime', value: number): void
  (e: 'update:endTime', value: number): void
  (e: 'update:overdueDay', value: number): void
}

const props = withDefaults(defineProps<Props>(), {
  validScope: 0,
  startTime: 0,
  endTime: 0,
  overdueDay: 30,
})

const emit = defineEmits<Emits>()

// 计算属性用于双向绑定
const validScopeModel = computed({
  get: () => props.validScope,
  set: (value: number) => emit('update:validScope', value),
})

const overdueDayModel = computed({
  get: () => props.overdueDay,
  set: (value: number) => emit('update:overdueDay', value),
})

// 日期范围的计算属性
const dateRangeModel = computed({
  get: () => {
    if (!props.startTime || !props.endTime) { return [] }
    return [
      new Date(props.startTime).toISOString().slice(0, 10),
      new Date(props.endTime).toISOString().slice(0, 10),
    ]
  },
  set: (value: string[]) => {
    if (value && value.length === 2) {
      emit('update:startTime', new Date(value[0]).getTime())
      emit('update:endTime', new Date(`${value[1]} 23:59:59`).getTime())
    }
    else {
      emit('update:startTime', 0)
      emit('update:endTime', 0)
    }
  },
})
</script>

<template>
  <div class="coupon-validity-period">
    <div class="section-title">
      <h3>有效期规则</h3>
      <p>设置优惠券的有效期类型和时间范围</p>
    </div>

    <div class="form-grid">
      <ElFormItem label="有效期方式" class="form-item-full">
        <ElRadioGroup v-model="validScopeModel">
          <ElRadio :value="0">
            <div class="radio-content">
              <div class="radio-title">
                固定时间
              </div>
              <div class="radio-desc">
                指定具体的开始和结束时间
              </div>
            </div>
          </ElRadio>
          <ElRadio :value="1">
            <div class="radio-content">
              <div class="radio-title">
                相对时间
              </div>
              <div class="radio-desc">
                绑定后N天内可用
              </div>
            </div>
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>

      <!-- 相对时间配置 -->
      <ElFormItem v-if="validScope === 1" style="width:600px;" label="有效天数">
        <div class="relative-time-config">
          <ElText>绑定后</ElText>
          <ElInputNumber
            v-model="overdueDayModel"
            :min="1"
            :max="365"
            :step="1"
            controls-position="right"
            placeholder="请输入天数"
            style="width: 120px; margin: 0 8px;"
          />
          <ElText>天内可用</ElText>
        </div>
        <ElText type="info" class="form-tip">
          设置用户绑定优惠券后的有效天数，建议不超过365天
        </ElText>
      </ElFormItem>

      <!-- 固定时间配置 -->
      <ElFormItem v-if="validScope === 0" label="有效期" class="form-item-full">
        <ElDatePicker
          v-model="dateRangeModel"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD"
          style="width: 400px"
        />
        <ElText type="info" class="form-tip">
          设置优惠券的具体有效时间范围
        </ElText>
      </ElFormItem>
    </div>
  </div>
</template>

<style scoped>
.coupon-validity-period {
  width: 100%;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  align-items: start;
}

.form-item-full {
  grid-column: 1 / -1;
}

.radio-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.radio-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.radio-desc {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.relative-time-config {
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-tip {
  display: block;
  margin-top: 4px;
  font-size: 12px;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.el-radio) {
  margin-right: 0;
  margin-bottom: 0;
  align-items: flex-start;
}

:deep(.el-radio__input) {
  margin-top: 2px;
}

/* 数字输入框样式 */
:deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  border-radius: 6px;
}

:deep(.el-date-editor .el-input__wrapper) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .relative-time-config {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  :deep(.el-date-editor) {
    width: 100% !important;
  }
}
</style>
