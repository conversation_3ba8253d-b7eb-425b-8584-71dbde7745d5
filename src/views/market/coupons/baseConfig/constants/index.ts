/**
 * 优惠券管理模块常量定义
 * 基于API文档：src/api/modules/coupons/优惠券.md
 */

import { ChannelScope, CouponType, GenerateStatus, MatchRule, ServiceFeeReduction, UseOnType, UseStatus } from '../types'

// ==================== 状态映射 ====================

/** 使用状态映射 */
export const USE_STATUS_MAP = {
  [UseStatus.DISABLED]: '已关闭',
  [UseStatus.ENABLED]: '已开启',
  [UseStatus.DRAFT]: '草稿',
} as const

/** 生成状态映射 */
export const GENERATE_STATUS_MAP = {
  [GenerateStatus.NOT_GENERATED]: '未生成',
  [GenerateStatus.GENERATED]: '已生成',
} as const

/** 适用类型映射 */
export const USE_ON_TYPE_MAP = {
  [UseOnType.MOVIE_TICKET]: '影票',
  [UseOnType.GOODS]: '卖品',
  [UseOnType.SHOW]: '演出',
  [UseOnType.EXHIBITION]: '展览',
  [UseOnType.ECOMMERCE]: '电商',
} as const

/** 优惠券类型映射 */
export const COUPON_TYPE_MAP = {
  [CouponType.FULL_REDUCTION]: '满减券',
  [CouponType.REDUCTION_TO]: '减至券',
  [CouponType.UNIVERSAL]: '通兑券',
  [CouponType.DISCOUNT]: '折扣券',
  [CouponType.MULTI_TO_ONE]: '多兑一券',
} as const

/** 服务费减免映射 */
export const SERVICE_FEE_REDUCTION_MAP = {
  [ServiceFeeReduction.REDUCE]: '减免',
  [ServiceFeeReduction.NO_REDUCE]: '不减免',
} as const

/** 匹配规则映射 */
export const MATCH_RULE_MAP = {
  [MatchRule.DISCOUNT_SUPPLEMENT]: '优惠补差',
  [MatchRule.LOWEST_PRICE]: '最低票价',
} as const

/** 渠道范围映射 */
export const CHANNEL_SCOPE_MAP = {
  [ChannelScope.SPECIFIED]: '指定渠道',
  [ChannelScope.ALL]: '全部渠道',
} as const

// ==================== 标签类型映射 ====================

/** 使用状态标签类型 */
export const USE_STATUS_TAG_TYPE = {
  [UseStatus.DISABLED]: 'danger',
  [UseStatus.ENABLED]: 'success',
  [UseStatus.DRAFT]: 'warning',
} as const

/** 生成状态标签类型 */
export const GENERATE_STATUS_TAG_TYPE = {
  [GenerateStatus.NOT_GENERATED]: 'warning',
  [GenerateStatus.GENERATED]: 'success',
} as const

/** 适用类型标签类型 */
export const USE_ON_TAG_TYPE = {
  [UseOnType.MOVIE_TICKET]: 'primary',
  [UseOnType.GOODS]: 'success',
  [UseOnType.SHOW]: 'warning',
  [UseOnType.EXHIBITION]: 'info',
  [UseOnType.ECOMMERCE]: 'danger',
} as const

/** 优惠券类型标签类型 */
export const COUPON_TYPE_TAG_TYPE = {
  [CouponType.FULL_REDUCTION]: 'primary',
  [CouponType.REDUCTION_TO]: 'success',
  [CouponType.UNIVERSAL]: 'warning',
  [CouponType.DISCOUNT]: 'info',
  [CouponType.MULTI_TO_ONE]: 'danger',
} as const

/** 服务费减免标签类型 */
export const SERVICE_FEE_REDUCTION_TAG_TYPE = {
  [ServiceFeeReduction.REDUCE]: 'success',
  [ServiceFeeReduction.NO_REDUCE]: 'info',
} as const

// ==================== 选项列表 ====================

/** 使用状态选项 */
export const USE_STATUS_OPTIONS = [
  { label: '已关闭', value: UseStatus.DISABLED },
  { label: '已开启', value: UseStatus.ENABLED },
  { label: '草稿', value: UseStatus.DRAFT },
]

/** 适用类型选项 */
export const USE_ON_TYPE_OPTIONS = [
  { label: '影票', value: UseOnType.MOVIE_TICKET, icon: 'fa-film' },
  { label: '卖品', value: UseOnType.GOODS, icon: 'fa-shopping-cart' },
  { label: '演出', value: UseOnType.SHOW, icon: 'fa-microphone' },
  { label: '展览', value: UseOnType.EXHIBITION, icon: 'fa-image' },
  { label: '电商', value: UseOnType.ECOMMERCE, icon: 'fa-globe' },
]

/** 优惠券类型选项 */
export const COUPON_TYPE_OPTIONS = [
  { label: '满减券', value: CouponType.FULL_REDUCTION },
  { label: '减至券', value: CouponType.REDUCTION_TO },
  { label: '通兑券', value: CouponType.UNIVERSAL },
  { label: '折扣券', value: CouponType.DISCOUNT },
  { label: '多兑一券', value: CouponType.MULTI_TO_ONE },
]

/** 服务费减免选项 */
export const SERVICE_FEE_REDUCTION_OPTIONS = [
  { label: '减免', value: ServiceFeeReduction.REDUCE },
  { label: '不减免', value: ServiceFeeReduction.NO_REDUCE },
]

/** 生成类型选项 */
export const GENERATE_TYPE_OPTIONS = [
  { label: '手动生成', value: 0 },
  { label: '自动生成', value: 1 },
]

/** 有效期类型选项 */
export const VALID_SCOPE_OPTIONS = [
  { label: '固定时间', value: 0 },
  { label: '相对时间', value: 1 },
]

/** 匹配规则选项 */
export const MATCH_RULE_OPTIONS = [
  { label: '优惠补差', value: MatchRule.DISCOUNT_SUPPLEMENT },
  { label: '最低票价', value: MatchRule.LOWEST_PRICE },
]

/** 渠道范围选项 */
export const CHANNEL_SCOPE_OPTIONS = [
  { label: '指定渠道', value: ChannelScope.SPECIFIED },
  { label: '全部渠道', value: ChannelScope.ALL },
]

// ==================== 默认配置 ====================

/** 默认分页配置 */
export const DEFAULT_PAGE_CONFIG = {
  page: 1,
  size: 20,
  total: 0,
}

/** 默认搜索参数 */
export const DEFAULT_SEARCH_PARAMS = {
  page: 0, // 后端从0开始
  size: 20,
}

/** 表格列配置 */
export const TABLE_COLUMNS = [
  { prop: 'id', label: '优惠券ID', width: 120, fixed: 'left' },
  { prop: 'name', label: '优惠券名称', width: 200, fixed: 'left' },
  { prop: 'useOn', label: '适用类型', width: 100 },
  { prop: 'couponType', label: '优惠券类型', width: 120 },
  { prop: 'userServiceFee', label: '用户服务费', width: 120 },
  { prop: 'reduction', label: '服务费减免', width: 120 },
  { prop: 'bindCount', label: '绑定数量', width: 100 },
  { prop: 'usedCount', label: '使用数量', width: 100 },
  { prop: 'useStatus', label: '使用状态', width: 100 },
  { prop: 'generateStatus', label: '生成状态', width: 100 },
  { prop: 'createTime', label: '创建时间', width: 160 },
  { prop: 'updatedTime', label: '更新时间', width: 160 },
  { prop: 'actions', label: '操作', width: 200, fixed: 'right' },
]

// ==================== 业务规则常量 ====================

/** 最大绑定数量 */
export const MAX_BIND_COUNT = 10000

/** 最小用户服务费 */
export const MIN_USER_SERVICE_FEE = 0

/** 最大用户服务费 */
export const MAX_USER_SERVICE_FEE = 999999

/** 最大优惠券名称长度 */
export const MAX_COUPON_NAME_LENGTH = 50

/** 最大备注长度 */
export const MAX_NOTE_LENGTH = 200

/** 最大说明长度 */
export const MAX_REMARK_LENGTH = 500

/** 日期格式 */
export const DATE_FORMAT = 'YYYY-MM-DD'

/** 日期时间格式 */
export const DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'

/** 时间格式 */
export const TIME_FORMAT = 'HH:mm:ss'

// ==================== API路径常量 ====================

/** API基础路径 */
export const API_BASE_PATH = '/adm_coupon'

/** API路径映射 */
export const API_PATHS = {
  LIST: `${API_BASE_PATH}/list`,
  DETAIL: `${API_BASE_PATH}/detail`,
  CREATE: `${API_BASE_PATH}/add`,
  UPDATE: `${API_BASE_PATH}/save`,
  DELETE: `${API_BASE_PATH}/delete`,
  STATUS: `${API_BASE_PATH}/status`,
  BIND: `${API_BASE_PATH}/bind`,
  CODES: `${API_BASE_PATH}/codes`,
  EXPORT: `${API_BASE_PATH}/export`,
} as const

// ==================== 错误消息常量 ====================

/** 错误消息 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络请求失败，请检查网络连接',
  SERVER_ERROR: '服务器错误，请稍后重试',
  VALIDATION_ERROR: '数据验证失败，请检查输入',
  PERMISSION_ERROR: '权限不足，无法执行此操作',
  NOT_FOUND: '未找到相关数据',
  DUPLICATE_NAME: '优惠券名称已存在',
  INVALID_DATE_RANGE: '时间范围无效',
  INVALID_AMOUNT: '金额格式无效',
} as const

/** 成功消息 */
export const SUCCESS_MESSAGES = {
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  STATUS_CHANGE_SUCCESS: '状态更新成功',
  BIND_SUCCESS: '绑定成功',
  EXPORT_SUCCESS: '导出成功',
} as const

// ==================== 快捷日期选项 ====================

/** 快捷日期选项 */
export const QUICK_DATE_OPTIONS = [
  {
    text: '今天',
    value: () => {
      const today = new Date()
      return [today, today]
    },
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      return [yesterday, yesterday]
    },
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      return [start, end]
    },
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const today = new Date()
      const start = new Date(today.getFullYear(), today.getMonth(), 1)
      const end = new Date(today.getFullYear(), today.getMonth() + 1, 0)
      return [start, end]
    },
  },
  {
    text: '上月',
    value: () => {
      const today = new Date()
      const start = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      const end = new Date(today.getFullYear(), today.getMonth(), 0)
      return [start, end]
    },
  },
]
