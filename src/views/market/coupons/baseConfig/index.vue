<script setup lang="ts">
import type { CreateCouponParams, UpdateCouponParams } from '@/api/modules/market/coupons'
// import type { CreateCouponParams } from '@/views/coupons/baseConfig/types'
import { Delete, InfoFilled, Lock, Plus } from '@element-plus/icons-vue'

import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'

// API导入
import { getAllChannel } from '@/api/modules/channel'
import { createCoupon, updateCoupon } from '@/api/modules/market/coupons'

// 常量导入
import {
  CHANNEL_SCOPE_OPTIONS,
  COUPON_TYPE_OPTIONS,
  MATCH_RULE_OPTIONS,
  SERVICE_FEE_REDUCTION_OPTIONS,
  USE_ON_TYPE_OPTIONS,
} from './constants'
import CouponSettleMethod from './CouponSettleMethod.vue'
// 类型导入

import CouponValidityPeriod from '@/views/market/coupons/baseConfig/CouponValidityPeriod.vue'

import { CouponType, ServiceFeeReduction, UseOnType } from '@/views/market/coupons/baseConfig/types'
// 子组件导入
import CouponGenerateType from './CouponGenerateType.vue'

defineOptions({
  name: 'CouponBaseConfig',
})

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
})

const emit = defineEmits<Emits>()

// ==================== Props & Emits ====================

interface Props {
  pageMode?: 'add' | 'edit' | 'view'
  couponData?: CreateCouponParams
}

interface Emits {
  (e: 'submit', data: CreateCouponParams): void
  (e: 'cancel'): void
  (e: 'next-step', couponId: string): void
  (e: 'refresh'): void
  (e: 'close'): void
  (e: 'success', data: any): void
}

// ==================== 响应式数据 ====================

// 表单引用
const formRef = ref()

// 渠道选项
const channelOptions = ref<Array<{ label: string, value: string }>>([])

// 加载状态
const loading = ref(false)

// 主表单数据 - 完全按照API接口结构
const formData = ref<CreateCouponParams>({
  name: '', // 优惠券名称
  useOn: 0, // 适用商品：影票0，卖品1，演出2，展览3，电商4
  reduction: 1, // 服务费减免：减免0，不减免1
  userServiceFee: 300, // 用户服务费(分)
  note: '', // 备注信息
  remark: '', // 备注说明
  couponType: 0, // 优惠券类型：满减0，减至1，通兑2，折扣3，多对一4
  priceRule: { // 价格规则
    priceTiers: [ // 价格层级数组
      {
        priceOrigin: 0, // 原始价格(分)
        priceReduce: 0, // 减免金额(分)
        priceDiff: 0, // 价格差值(分)
      },
    ],
    matchRule: 0, // 匹配规则 0 优惠补差 1 最低票价
  },
  channelRule: { // 渠道规则
    channelScope: 2, // 渠道范围 1-指定渠道 2-全部渠道
    channels: [], // 适用渠道列表
  },
  generateRule: { // 生成规则
    generateType: 1, // 生成类型：0手动，1自动
    num: 100, // 生成数量
    batchSize: 100, // 批次大小
  },
  settleRule: { // 结算规则
    method: 0, // 结算方式 0:定价规则 1:影院团购价 2.自定义结算
    priced: { // 自定义结算配置
      type: 0, // 计算方式 0-固定价格 1-最低票价
      money: 0, // 定价金额(单位：分)
    },
  },
  periodRule: { // 时间有效规则
    validScope: 0, // 有效期类型：0固定时间，1相对时间
    startTime: 0, // 开始时间（固定时间模式）
    endTime: 0, // 结束时间（固定时间模式）
    overdueDay: 30, // 有效天数（相对时间模式）
  },
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入优惠券名称', trigger: 'blur' },
    { min: 1, max: 50, message: '优惠券名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  useOn: [
    { required: true, message: '请选择适用商品类型', trigger: 'change' },
  ],
  couponType: [
    { required: true, message: '请选择优惠券类型', trigger: 'change' },
  ],
  reduction: [
    { required: true, message: '请选择服务费减免方式', trigger: 'change' },
  ],
  userServiceFee: [
    { required: true, message: '请输入用户服务费', trigger: 'blur' },
    { type: 'number', min: 0, max: 999999, message: '用户服务费范围为 0-9999.99 元', trigger: 'blur' },
  ],
}

// ==================== 计算属性 ====================

// 用户服务费（元）
const userServiceFeeYuan = computed({
  get: () => {
    return formData.value.userServiceFee / 100;
  },
  set: (value: number) => {
    formData.value.userServiceFee = Math.round(value * 100)
  },
})

// 价格规则标签
const priceRuleLabel = computed(() => {
  const { couponType } = formData.value
  switch (couponType) {
    case 0: return '满减规则'
    case 1: return '减至规则'
    case 2: return '通兑规则'
    case 3: return '折扣规则'
    case 4: return '多对一规则'
    default: return '价格规则'
  }
})

// 是否可以添加多条价格规则
const canAddMultiplePriceTiers = computed(() => {
  // 优惠补差或多对一券时可以添加多条
  // 条件为电影票 通兑券或者是多对一券
  console.log('canAddMultiplePriceTiers', formData.value.couponType, formData.value.useOn)
  return (formData.value.couponType === 2 || formData.value.couponType === 4) && formData.value.useOn === UseOnType.MOVIE_TICKET
  // return formData.value.priceRule?.matchRule === 0 || formData.value.couponType === 4 || formData.value.useOn === UseOnType.MOVIE
})

// 最大价格规则数量
const maxPriceTiers = computed(() => {
  if (formData.value.priceRule?.matchRule === 0) {
    return 10
  }
  // 多对一券最多可添加10条规则
  if (formData.value.couponType === 4) {
    return 10
  }
  return 1
})

// ==================== 业务逻辑函数 ====================

/**
 * 加载渠道选项
 */
async function loadChannelOptions() {
  try {
    loading.value = true
    const response = await getAllChannel()
    const { data } = response

    channelOptions.value = data.map((item: any) => ({
      label: item.channelName,
      value: item.id,
    }))
  }
  catch (error) {
    console.error('加载渠道选项失败:', error)
    ElMessage.error('加载渠道选项失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 根据优惠券类型设置价格规则默认值
 */
function setPriceRuleDefaults() {
  const { couponType } = formData.value

  // 重置价格规则
  formData.value.priceRule = {
    priceTiers: [
      {
        priceOrigin: 0,
        priceReduce: 0,
        priceDiff: 0,
      },
    ],
    matchRule: 0, // 默认为优惠补差
  }

  // 根据优惠券类型设置特定的默认值
  switch (couponType) {
    case 0: // 满减券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 1000, // 满1000分
        priceReduce: 100, // 减100分
        priceDiff: 0,
      }
      break
    case 1: // 减至券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 0,
        priceReduce: 500, // 减至500分
        priceDiff: 0,
      }
      break
    case 2: // 通兑券
      // 电影票 + 通兑券 + 最低价：只设置减免金额
      if (formData.value.useOn === 0 && formData.value.priceRule.matchRule === 1) {
        formData.value.priceRule.priceTiers[0] = {
          priceOrigin: 0,
          priceReduce: 500, // 减免500分（5元）
          priceDiff: 0,
        }
      }
      else {
        // 其他情况：设置完整的通兑券配置
        formData.value.priceRule.priceTiers[0] = {
          priceOrigin: 1000, // 最小1000分
          priceReduce: 2000, // 最大2000分
          priceDiff: 100, // 补差100分
        }
      }
      break
    case 3: // 折扣券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 1000, // 满1000分
        priceReduce: 80, // 8折(80%)
        priceDiff: 0,
      }
      break
    case 4: // 多对一券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 0,
        priceReduce: 2, // 2张兑1张
        priceDiff: 0,
      }
      // 如果是编辑模式，允许多条规则
      if (props.pageMode === 'edit') {
        // 保持现有规则不变
      }
      break
    default:
      break
  }
}

/**
 * 添加价格规则
 */
function addPriceTier() {
  if (!canAddMultiplePriceTiers.value) {
    ElMessage.warning('当前匹配规则只能设置一条价格规则')
    return
  }

  if (formData.value.priceRule!.priceTiers.length >= maxPriceTiers.value) {
    ElMessage.warning(`最多只能添加${maxPriceTiers.value}条价格规则`)
    return
  }

  formData.value.priceRule!.priceTiers.push({
    priceOrigin: 0,
    priceReduce: 0,
    priceDiff: 0,
  })
}

/**
 * 删除价格规则
 */
function removePriceTier(index: number) {
  if (formData.value.priceRule!.priceTiers.length <= 1) {
    ElMessage.warning('至少需要保留一条价格规则')
    return
  }

  formData.value.priceRule!.priceTiers.splice(index, 1)
}

/**
 * 处理匹配规则变化
 */
function handleMatchRuleChange() {
  const { matchRule } = formData.value.priceRule!

  if (matchRule === 1) { // 最低票价
    // 只保留第一条规则
    formData.value.priceRule!.priceTiers = [formData.value.priceRule!.priceTiers[0]]
  }
}

/**
 * 处理渠道范围变化
 */
function handleChannelScopeChange() {
  const { channelScope } = formData.value.channelRule!

  if (channelScope === 2) { // 全部渠道
    formData.value.channelRule!.channels = []
  }
}

/**
 * 验证表单数据
 */
async function validateForm(): Promise<boolean> {
  try {
    await formRef.value?.validate()

    // 验证价格规则
    const { priceTiers } = formData.value.priceRule!
    for (let i = 0; i < priceTiers.length; i++) {
      const tier = priceTiers[i]
      if (tier.priceOrigin < 0 || tier.priceReduce < 0 || tier.priceDiff < 0) {
        ElMessage.error(`第${i + 1}条价格规则的金额不能为负数`)
        return false
      }
    }

    // 验证渠道规则
    const { channelScope, channels } = formData.value.channelRule!
    if (channelScope === 1 && channels.length === 0) {
      ElMessage.error('指定渠道时必须选择至少一个渠道')
      return false
    }

    return true
  }
  catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

/**
 * 提交表单
 */
async function handleSubmit() {
  if (!await validateForm()) {
    return
  }

  try {
    loading.value = true

    // 构建提交数据，完全按照API接口格式
    const submitData: CreateCouponParams = {
      ...formData.value,
      // 确保数值类型正确
      userServiceFee: Number(formData.value.userServiceFee),
      useOn: Number(formData.value.useOn),
      couponType: Number(formData.value.couponType),
      reduction: Number(formData.value.reduction),
    }

    if (props.pageMode === 'add') {
      // 创建优惠券
      const response = await createCoupon(submitData as CreateCouponParams)
      const { data, code, msg } = response
      if (code === 0) {
        ElMessage.success('创建优惠券成功')
        emit('next-step', data?.id || data)
      }
      else {
        ElMessage.error(msg || '创建优惠券失败')
      }
    }
    else {
      console.log('编辑模式提交数据:', submitData)
      const response = await updateCoupon(submitData as UpdateCouponParams)
      const { data, code, msg } = response
      if (code === 0) {
        ElMessage.success('更新优惠券成功')
        emit('success', data)
      }
      else {
        ElMessage.error(msg || '更新优惠券失败')
      }
      // 编辑模式
      emit('submit', submitData)
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  }
  finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
function handleReset() {
  formRef.value?.resetFields()
  setPriceRuleDefaults()
}

// ==================== 监听器 ====================

// 监听优惠券类型变化，设置默认价格规则
watch(() => formData.value.couponType, () => {
  setPriceRuleDefaults()
})

// 监听匹配规则变化
watch(() => formData.value.priceRule?.matchRule, () => {
  handleMatchRuleChange()
})

// 监听渠道范围变化
watch(() => formData.value.channelRule?.channelScope, () => {
  handleChannelScopeChange()
})

// 监听props变化，初始化表单数据
watch(() => props.couponData, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true, deep: true })

// ==================== 生命周期 ====================

onMounted(() => {
  loadChannelOptions()
  setPriceRuleDefaults()
})

// ==================== 暴露方法 ====================

defineExpose({
  formData,
  handleSubmit,
  handleReset,
  validateForm,
})
</script>

<template>
  <div class="p-5 bg-white rounded-lg">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      class="max-w-4xl mx-auto"
    >
      <!-- 基本信息 -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <div class="mb-5 pb-3 border-b-2 border-blue-500">
          <h3 class="text-lg font-semibold text-gray-900 mb-1">基本信息</h3>
          <p class="text-sm text-gray-600">设置优惠券的基本信息和适用范围</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 items-start">
          <!-- 优惠券名称 -->
          <ElFormItem label="优惠券名称" prop="name" class="md:col-span-2">
            <ElInput
              v-model="formData.name"
              placeholder="请输入优惠券名称"
              maxlength="50"
              show-word-limit
              clearable
            />
          </ElFormItem>

          <!-- 适用商品 -->
          <ElFormItem label="适用商品" prop="useOn">
            <ElSelect
              v-model="formData.useOn"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              placeholder="请选择适用商品类型"
              class="w-full"
            >
              <ElOption
                v-for="option in USE_ON_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
            <ElText v-if="pageMode === 'edit'" type="warning" size="small" class="mt-1">
              <ElIcon><InfoFilled /></ElIcon>
              适用商品类型创建后不可修改
            </ElText>
          </ElFormItem>

          <!-- 优惠券类型 -->
          <ElFormItem label="优惠券类型" prop="couponType">
            <ElSelect
              v-model="formData.couponType"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              placeholder="请选择优惠券类型"
              class="w-full"
            >
              <ElOption
                v-for="option in COUPON_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
            <ElText v-if="pageMode === 'edit'" type="warning" size="small" class="mt-1">
              <ElIcon><InfoFilled /></ElIcon>
              优惠券类型创建后不可修改
            </ElText>
          </ElFormItem>

          <!-- 服务费减免 -->
          <ElFormItem label="服务费减免" prop="reduction">
            <ElSelect
              v-model="formData.reduction"
              placeholder="请选择服务费减免方式"
              class="w-full"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
            >
              <ElOption
                v-for="option in SERVICE_FEE_REDUCTION_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 用户服务费 -->
          <ElFormItem v-if="formData.reduction === ServiceFeeReduction.NO_REDUCE" label="用户服务费" prop="userServiceFee">
            <ElInputNumber
              v-model="userServiceFeeYuan"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              :min="0"
              :max="9999.99"
              :precision="2"
              :step="1"
              class="w-full"
            >
              <template #suffix>
                元
              </template>
            </ElInputNumber>
          </ElFormItem>
        </div>
      </div>

      <!-- 价格规则 -->
      <div
        class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200"
        :class="{ 'opacity-80 bg-gray-100 border-gray-300': pageMode === 'edit' || pageMode === 'view' }"
      >
        <div class="mb-5 pb-3 border-b-2 border-blue-500">
          <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ priceRuleLabel }}</h3>
          <p class="text-sm text-gray-600">根据优惠券类型设置相应的价格规则</p>
          <ElText v-if="pageMode === 'edit'" type="warning" size="small" class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs flex items-center">
            <ElIcon class="mr-1"><Lock /></ElIcon>
            价格规则创建后不可修改
          </ElText>
        </div>

        <!-- 匹配规则 -->
        <div v-if="formData.couponType === CouponType.UNIVERSAL && formData.useOn === UseOnType.MOVIE_TICKET" class="grid grid-cols-1 md:grid-cols-2 gap-5 items-start">
          <ElFormItem label="匹配规则" class="md:col-span-2">
            <ElSelect
              v-model="formData.priceRule!.matchRule"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              placeholder="请选择匹配规则"
              class="w-64"
            >
              <ElOption
                v-for="option in MATCH_RULE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
            <ElText type="info" class="ml-2">
              <ElIcon><InfoFilled /></ElIcon>
              {{ formData.priceRule!.matchRule === 0 ? '优惠补差时可添加多条规则' : '最低票价时只能有一条规则' }}
            </ElText>
          </ElFormItem>
        </div>

        <!-- 价格层级规则 -->
        <div class="mt-5">
          <!-- 如果是通兑券，需要支持补差规则，这里添加是添加多个补差规则-->
          <div v-if="formData.couponType === CouponType.UNIVERSAL" class="flex justify-between items-center mb-4 p-3 bg-gray-100 rounded">
            <span class="font-medium text-gray-700">价格层级规则</span>
            <ElButton
              v-if="canAddMultiplePriceTiers && formData.priceRule!.priceTiers.length < maxPriceTiers && pageMode !== 'edit' && pageMode !== 'view'"
              type="primary"
              :icon="Plus"
              size="small"
              @click="addPriceTier"
            >
              添加规则
            </ElButton>
          </div>
          <!-- 如果是多兑一，需要添加多个兑换一张的规则，这里添加 -->
          <div v-if="formData.couponType === CouponType.MULTI_TO_ONE" class="flex justify-between items-center mb-4 p-3 bg-gray-100 rounded">
            <span class="font-medium text-gray-700">兑换一张规则</span>
            <ElButton
              v-if="canAddMultiplePriceTiers && formData.priceRule!.priceTiers.length < maxPriceTiers && pageMode !== 'edit' && pageMode !== 'view'"
              type="primary"
              :icon="Plus"
              @click="addPriceTier"
            >
              添加规则多对一
            </ElButton>
          </div>


          <div
            v-for="(tier, index) in formData.priceRule!.priceTiers"
            :key="index"
            class="mb-4 p-4 bg-white border border-gray-200 rounded"
          >
            <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200">
              <span class="font-medium text-blue-500">规则 {{ index + 1 }}</span>
              <ElButton
                v-if="formData.priceRule!.priceTiers.length > 1 && pageMode !== 'edit' && pageMode !== 'view'"
                type="danger"
                :icon="Delete"
                size="small"
                text
                @click="removePriceTier(index)"
              >
                删除
              </ElButton>
            </div>

            <!-- 满减券规则 -->
            <div v-if="formData.couponType === 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ElFormItem label="满减金额">
                <ElInputNumber
                  :model-value="tier.priceOrigin / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  class="w-full"
                  @update:model-value="(val) => tier.priceOrigin = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
              <ElFormItem label="减免金额">
                <ElInputNumber
                  :model-value="tier.priceReduce / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  class="w-full"
                  @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>

            <!-- 减至券规则 -->
            <div v-else-if="formData.couponType === 1" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ElFormItem label="减至金额">
                <ElInputNumber
                  :model-value="tier.priceReduce / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  class="w-full"
                  @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>

            <!-- 通兑券规则 -->
            <div v-else-if="formData.couponType === 2" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 电影票 + 通兑券 + 最低价：只显示减免金额 -->
              <template v-if="formData.useOn === 0 && formData.priceRule?.matchRule === 1">
                <div class="md:col-span-2 mb-3 p-2 bg-blue-50 border border-blue-200 rounded">
                  <ElText type="info" size="small" class="flex items-center">
                    <ElIcon class="mr-1"><InfoFilled /></ElIcon>
                    电影票通兑券在最低价模式下，只需配置减免金额
                  </ElText>
                </div>
                <ElFormItem label="减免金额" class="w-64">
                  <ElInputNumber
                    :model-value="tier.priceReduce / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    class="w-full"
                    @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
              </template>

              <!-- 其他情况：显示完整的通兑券配置 -->
              <template v-else>
                <ElFormItem label="最小金额">
                  <ElInputNumber
                    :model-value="tier.priceOrigin / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    class="w-full"
                    @update:model-value="(val) => tier.priceOrigin = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
                <ElFormItem label="最大金额">
                  <ElInputNumber
                    :model-value="tier.priceReduce / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    class="w-full"
                    @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
                <ElFormItem label="补差金额" class="md:col-span-2">
                  <ElInputNumber
                    :model-value="tier.priceDiff / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    class="w-full"
                    @update:model-value="(val) => tier.priceDiff = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
              </template>
            </div>

            <!-- 折扣券规则 -->
            <div v-else-if="formData.couponType === 3" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ElFormItem label="满减金额">
                <ElInputNumber
                  :model-value="tier.priceOrigin / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  class="w-full"
                  @update:model-value="(val) => tier.priceOrigin = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
              <ElFormItem label="折扣率">
                <ElInputNumber
                  v-model="tier.priceReduce"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :max="100"
                  :precision="2"
                  class="w-full"
                >
                  <template #suffix>
                    %
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>

            <!-- 多对一券规则 -->
            <div v-else-if="formData.couponType === 4" class="grid grid-cols-1 gap-4">
              <div class="flex items-center gap-2">
                <span class="text-gray-700">兑换规则：</span>
                <ElText type="info" size="small">
                  <ElIcon><InfoFilled /></ElIcon>
                  设置多条兑换规则，例如：2张兑1张、3张兑1张等
                </ElText>
              </div>

              <div
                :key="index"
                class="flex items-center gap-2 p-3 bg-gray-50 rounded border"
              >
                <ElFormItem label="" class="flex-1 mb-0">
                  <div class="flex items-center">
                    <ElInputNumber
                      v-model="tier.priceReduce"
                      :disabled="pageMode === 'edit' || pageMode === 'view'"
                      :min="1"
                      :max="100"
                      :precision="0"
                      class="w-32"
                    >
                    </ElInputNumber>
                    <span class="text-gray-700">张</span>
                    <span class="text-gray-700">可兑换一张</span>

                  </div>
                </ElFormItem>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 渠道规则 -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <div class="mb-5 pb-3 border-b-2 border-blue-500">
          <h3 class="text-lg font-semibold text-gray-900 mb-1">渠道规则</h3>
          <p class="text-sm text-gray-600">设置优惠券的适用渠道范围</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 items-start">
          <!-- 渠道范围 -->
          <ElFormItem label="渠道范围" class="md:col-span-2">
            <ElSelect
              v-model="formData.channelRule!.channelScope"
              placeholder="请选择渠道范围"
              class="w-64"
            >
              <ElOption
                v-for="option in CHANNEL_SCOPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 指定渠道 -->
          <ElFormItem
            v-if="formData.channelRule!.channelScope === 1"
            label="适用渠道"
            class="md:col-span-2"
          >
            <ElSelect
              v-model="formData.channelRule!.channels"
              placeholder="请选择适用渠道"
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="w-full"
              :loading="loading"
              value-key="channelId"
            >
              <ElOption
                v-for="option in channelOptions"
                :key="option.value"
                :label="option.label"
                :value="{ channelId: option.value, channelName: option.label }"
              />
            </ElSelect>
          </ElFormItem>
        </div>
      </div>

      <!-- 生成规则 -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <CouponGenerateType
          v-model:generate-type="formData.generateRule!.generateType"
          v-model:num="formData.generateRule!.num"
          v-model:batch-size="formData.generateRule!.batchSize"
        />
      </div>

      <!-- 结算规则 -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <CouponSettleMethod
          v-model:method="formData.settleRule!.method"
          v-model:priced="formData.settleRule!.priced"
        />
      </div>

      <!-- 有效期规则 -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <CouponValidityPeriod
          v-model:valid-scope="formData.periodRule!.validScope"
          v-model:start-time="formData.periodRule!.startTime"
          v-model:end-time="formData.periodRule!.endTime"
          v-model:overdue-day="formData.periodRule!.overdueDay"
        />
      </div>

      <!-- 备注信息 -->
      <div class="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <div class="mb-5 pb-3 border-b-2 border-blue-500">
          <h3 class="text-lg font-semibold text-gray-900 mb-1">备注信息</h3>
          <p class="text-sm text-gray-600">添加优惠券的备注和说明信息</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 items-start">
          <ElFormItem label="备注" class="md:col-span-2">
            <ElInput
              v-model="formData.note"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="说明" class="md:col-span-2">
            <ElInput
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入说明信息"
              maxlength="500"
              show-word-limit
            />
          </ElFormItem>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center gap-4 mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
        <ElButton @click="handleReset">
          重置
        </ElButton>
        <ElButton @click="emit('cancel')">
          取消
        </ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          {{ pageMode === 'add' ? '创建优惠券' : '保存修改' }}
        </ElButton>
      </div>
    </ElForm>
    <pre class="hidden">
      {{ JSON.stringify(formData, null, 2) }}
    </pre>
  </div>
</template>
