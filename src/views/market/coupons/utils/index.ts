/**
 * 优惠券管理模块工具函数
 * 基于API文档：src/api/modules/coupons/优惠券.md
 */

import type { Coupon, SearchFormData } from '../baseConfig/types'
import dayjs from 'dayjs'

import {
  COUPON_TYPE_MAP,
  COUPON_TYPE_TAG_TYPE,
  DATETIME_FORMAT,
  GENERATE_STATUS_MAP,
  GENERATE_STATUS_TAG_TYPE,
  SERVICE_FEE_REDUCTION_MAP,
  SERVICE_FEE_REDUCTION_TAG_TYPE,
  USE_ON_TAG_TYPE,
  USE_ON_TYPE_MAP,
  USE_STATUS_MAP,
  USE_STATUS_TAG_TYPE,
} from '../baseConfig/constants'

// ==================== 格式化函数 ====================

/**
 * 格式化金额（分转元）
 * @param amount 金额（分）
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number): string {
  // eslint-disable-next-line style/max-statements-per-line
  if (Number.isNaN(amount)) { return '¥0.00' }
  return `¥${(amount / 100).toFixed(2)}`
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @param format 格式化字符串
 * @returns 格式化后的时间字符串
 */
export function formatTime(timestamp?: number, format: string = DATETIME_FORMAT): string {
  // eslint-disable-next-line style/max-statements-per-line
  if (!timestamp) { return '-' }
  return dayjs(timestamp).format(format)

  // try {
  //   const date = new Date(timestamp)
  //   if (isNaN(date.getTime())) { return '-' }
  //
  //   // 简单的日期格式化
  //   const year = date.getFullYear()
  //   const month = String(date.getMonth() + 1).padStart(2, '0')
  //   const day = String(date.getDate()).padStart(2, '0')
  //   const hours = String(date.getHours()).padStart(2, '0')
  //   const minutes = String(date.getMinutes()).padStart(2, '0')
  //   const seconds = String(date.getSeconds()).padStart(2, '0')
  //
  //   return format
  //     .replace('YYYY', year.toString())
  //     .replace('MM', month)
  //     .replace('DD', day)
  //     .replace('HH', hours)
  //     .replace('mm', minutes)
  //     .replace('ss', seconds)
  // }
  // catch (error) {
  //   console.error('时间格式化失败:', error)
  //   return '-'
  // }
}

/**
 * 格式化使用率
 * @param usedCount 已使用数量
 * @param bindCount 已绑定数量
 * @returns 使用率字符串
 */
export function formatUsageRate(usedCount: number, bindCount: number): string {
  // eslint-disable-next-line style/max-statements-per-line
  if (!bindCount || bindCount === 0) { return '0%' }
  const rate = Math.round((usedCount / bindCount) * 100)
  return `${rate}%`
}

// ==================== 描述获取函数 ====================

/**
 * 获取使用状态描述
 * @param status 使用状态
 * @returns 状态描述
 */
export function getUseStatusDesc(status: number): string {
  return USE_STATUS_MAP[status as keyof typeof USE_STATUS_MAP] || '未知状态'
}

/**
 * 获取生成状态描述
 * @param status 生成状态
 * @returns 状态描述
 */
export function getGenerateStatusDesc(status: number): string {
  return GENERATE_STATUS_MAP[status as keyof typeof GENERATE_STATUS_MAP] || '未知状态'
}

/**
 * 获取适用类型描述
 * @param type 适用类型
 * @returns 类型描述
 */
export function getUseOnDesc(type: number): string {
  return USE_ON_TYPE_MAP[type as keyof typeof USE_ON_TYPE_MAP] || '未知类型'
}

/**
 * 获取优惠券类型描述
 * @param type 优惠券类型
 * @returns 类型描述
 */
export function getCouponTypeDesc(type: number): string {
  return COUPON_TYPE_MAP[type as keyof typeof COUPON_TYPE_MAP] || '未知类型'
}

/**
 * 获取服务费减免描述
 * @param reduction 服务费减免
 * @returns 减免描述
 */
export function getServiceFeeReductionDesc(reduction: number): string {
  return SERVICE_FEE_REDUCTION_MAP[reduction as keyof typeof SERVICE_FEE_REDUCTION_MAP] || '未知'
}

// ==================== 标签类型获取函数 ====================

/**
 * 获取使用状态标签类型
 * @param status 使用状态
 * @returns Element Plus 标签类型
 */
export function getUseStatusTagType(status: number): string {
  return USE_STATUS_TAG_TYPE[status as keyof typeof USE_STATUS_TAG_TYPE] || 'info'
}

/**
 * 获取生成状态标签类型
 * @param status 生成状态
 * @returns Element Plus 标签类型
 */
export function getGenerateStatusTagType(status: number): string {
  return GENERATE_STATUS_TAG_TYPE[status as keyof typeof GENERATE_STATUS_TAG_TYPE] || 'info'
}

/**
 * 获取适用类型标签类型
 * @param type 适用类型
 * @returns Element Plus 标签类型
 */
export function getUseOnTagType(type: number): string {
  return USE_ON_TAG_TYPE[type as keyof typeof USE_ON_TAG_TYPE] || 'info'
}

/**
 * 获取优惠券类型标签类型
 * @param type 优惠券类型
 * @returns Element Plus 标签类型
 */
export function getCouponTypeTagType(type: number): string {
  return COUPON_TYPE_TAG_TYPE[type as keyof typeof COUPON_TYPE_TAG_TYPE] || 'info'
}

/**
 * 获取服务费减免标签类型
 * @param reduction 服务费减免
 * @returns Element Plus 标签类型
 */
export function getServiceFeeReductionTagType(reduction: number): string {
  return SERVICE_FEE_REDUCTION_TAG_TYPE[reduction as keyof typeof SERVICE_FEE_REDUCTION_TAG_TYPE] || 'info'
}

// ==================== 数据处理函数 ====================

/**
 * 适配优惠券数据格式
 * @param coupon 原始优惠券数据
 * @returns 适配后的优惠券数据
 */
export function adaptCouponData(coupon: any): Coupon {
  // eslint-disable-next-line style/max-statements-per-line
  if (!coupon) { return coupon }

  return {
    ...coupon,
    // 确保必需字段的类型正确
    id: String(coupon.id || ''),
    name: String(coupon.name || ''),
    useOn: Number(coupon.useOn || 0),
    reduction: Number(coupon.reduction || 0),
    userServiceFee: Number(coupon.userServiceFee || 0),
    couponType: Number(coupon.couponType || 0),
    createTime: Number(coupon.createTime || 0),
    updatedTime: Number(coupon.updatedTime || 0),
    bindCount: Number(coupon.bindCount || 0),
    usedCount: Number(coupon.usedCount || 0),
    useStatus: Number(coupon.useStatus || 0),
    generateStatus: Number(coupon.generateStatus || 0),
    deleted: Boolean(coupon.deleted || false),

    // 可选字段
    restrictionId: coupon.restrictionId || undefined,
    note: coupon.note || undefined,
    remark: coupon.remark || undefined,
    priceRule: coupon.priceRule || undefined,
    channelRule: coupon.channelRule || undefined,
    generateRule: coupon.generateRule || undefined,
    settleRule: coupon.settleRule || undefined,
    periodRule: coupon.periodRule || undefined,
  }
}

/**
 * 批量适配优惠券数据
 * @param coupons 优惠券数组
 * @returns 适配后的优惠券数组
 */
export function adaptCouponList(coupons: any[]): Coupon[] {
  // eslint-disable-next-line style/max-statements-per-line
  if (!Array.isArray(coupons)) { return [] }
  return coupons.map(adaptCouponData)
}

/**
 * 转换搜索参数
 * @param params 原始参数
 * @returns 转换后的参数
 */
export function transformSearchParams(params: SearchFormData): any {
  const transformed = { ...params }

  // 处理时间范围
  if (params.dateRange && Array.isArray(params.dateRange) && params.dateRange.length === 2) {
    transformed.startTime = params.dateRange[0]
    transformed.endTime = params.dateRange[1]
    delete transformed.dateRange
  }

  // 清理空值
  Object.keys(transformed).forEach((key) => {
    const value = transformed[key as keyof SearchFormData]
    if (value === '' || value === null || value === undefined) {
      delete transformed[key as keyof SearchFormData]
    }
  })

  return transformed
}

// ==================== 验证函数 ====================

/**
 * 验证优惠券名称
 * @param name 优惠券名称
 * @returns 验证结果
 */
export function validateCouponName(name: string): { valid: boolean, message?: string } {
  if (!name || name.trim() === '') {
    return { valid: false, message: '优惠券名称不能为空' }
  }

  if (name.length > 50) {
    return { valid: false, message: '优惠券名称不能超过50个字符' }
  }

  return { valid: true }
}

/**
 * 验证用户服务费
 * @param fee 用户服务费
 * @returns 验证结果
 */
export function validateUserServiceFee(fee: number): { valid: boolean, message?: string } {
  if (Number.isNaN(fee)) {
    return { valid: false, message: '用户服务费必须是数字' }
  }

  if (fee < 0) {
    return { valid: false, message: '用户服务费不能为负数' }
  }

  if (fee > 999999) {
    return { valid: false, message: '用户服务费不能超过9999.99元' }
  }

  return { valid: true }
}

/**
 * 验证时间范围
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 验证结果
 */
export function validateDateRange(startTime?: string, endTime?: string): { valid: boolean, message?: string } {
  if (!startTime || !endTime) {
    return { valid: false, message: '请选择完整的时间范围' }
  }

  const start = new Date(startTime)
  const end = new Date(endTime)

  if (Number.isNaN(start.getTime()) || Number.isNaN(end.getTime())) {
    return { valid: false, message: '时间格式无效' }
  }

  if (start >= end) {
    return { valid: false, message: '开始时间必须早于结束时间' }
  }

  return { valid: true }
}

// ==================== 导出函数 ====================

/**
 * 导出优惠券数据为CSV
 * @param coupons 优惠券数据
 * @returns CSV字符串
 */
export function exportCouponsToCSV(coupons: Coupon[]): string {
  const headers = [
    '优惠券ID',
    '优惠券名称',
    '适用类型',
    '优惠券类型',
    '用户服务费',
    '服务费减免',
    '绑定数量',
    '使用数量',
    '使用率',
    '使用状态',
    '生成状态',
    '创建时间',
    '更新时间',
  ]

  const rows = coupons.map(coupon => [
    coupon.id,
    coupon.name,
    getUseOnDesc(coupon.useOn),
    getCouponTypeDesc(coupon.couponType),
    formatAmount(coupon.userServiceFee),
    getServiceFeeReductionDesc(coupon.reduction),
    coupon.bindCount,
    coupon.usedCount,
    formatUsageRate(coupon.usedCount, coupon.bindCount),
    getUseStatusDesc(coupon.useStatus),
    getGenerateStatusDesc(coupon.generateStatus),
    formatTime(coupon.createTime),
    formatTime(coupon.updatedTime),
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')

  return csvContent
}

/**
 * 下载CSV文件
 * @param csvContent CSV内容
 * @param filename 文件名
 */
export function downloadCSV(csvContent: string, filename: string): void {
  const blob = new Blob([`\uFEFF${csvContent}`], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

// ==================== 其他工具函数 ====================

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      // eslint-disable-next-line prefer-spread
      func.apply(null, args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let lastTime = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()

    if (now - lastTime >= wait) {
      lastTime = now
      // eslint-disable-next-line prefer-spread
      func.apply(null, args)
    }
  }
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as T
  }

  if (typeof obj === 'object') {
    const cloned = {} as T
    Object.keys(obj).forEach((key) => {
      cloned[key as keyof T] = deepClone(obj[key as keyof T])
    })
    return cloned
  }

  return obj
}
