/**
 * 卡政策管理常量定义
 */

import { VipCardPolicyStatus, CardType, DisplayDiscountPrice } from '../types'

// 卡政策状态选项
export const VIP_CARD_POLICY_STATUS_OPTIONS = [
  { label: '全部', value: null },
  { label: '下架', value: VipCardPolicyStatus.DISABLED },
  { label: '上架', value: VipCardPolicyStatus.ENABLED },
  { label: '草稿', value: VipCardPolicyStatus.DRAFT },
]

// 卡政策状态标签映射
export const VIP_CARD_POLICY_STATUS_MAP = {
  [VipCardPolicyStatus.DISABLED]: { label: '下架', type: 'danger' },
  [VipCardPolicyStatus.ENABLED]: { label: '上架', type: 'success' },
  [VipCardPolicyStatus.DRAFT]: { label: '草稿', type: 'info' },
}

// 卡类型选项
export const CARD_TYPE_OPTIONS = [
  { label: '储值卡', value: CardType.STORE_VALUE },
]

// 卡类型标签映射
export const CARD_TYPE_MAP = {
  [CardType.STORE_VALUE]: { label: '储值卡', type: 'primary' },
}

// 是否显示优惠价选项
export const DISPLAY_DISCOUNT_PRICE_OPTIONS = [
  { label: '不显示', value: DisplayDiscountPrice.NOT_DISPLAY },
  { label: '显示', value: DisplayDiscountPrice.DISPLAY },
]

// 是否显示优惠价标签映射
export const DISPLAY_DISCOUNT_PRICE_MAP = {
  [DisplayDiscountPrice.NOT_DISPLAY]: { label: '不显示', type: 'info' },
  [DisplayDiscountPrice.DISPLAY]: { label: '显示', type: 'success' },
}

// 分页配置
export const PAGINATION_CONFIG = {
  page: 1,
  size: 20,
  pageSizes: [10, 20, 50, 100],
  total: 0,
}

// 表单验证规则
export const VIP_CARD_POLICY_FORM_RULES = {
  name: [
    { required: true, message: '请输入影城卡名称', trigger: 'blur' },
    { min: 2, max: 50, message: '影城卡名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入影城卡详情介绍', trigger: 'blur' },
    { min: 5, max: 500, message: '影城卡详情介绍长度在 5 到 500 个字符', trigger: 'blur' },
  ],
  cardType: [
    { required: true, message: '请选择卡类型', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
  sort: [
    { required: true, message: '请输入推荐排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '推荐排序范围 0-9999', trigger: 'blur' },
  ],
  displayDiscountPrice: [
    { required: true, message: '请选择是否显示优惠价', trigger: 'change' },
  ],
  displaySort: [
    { required: true, message: '请输入展示排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '展示排序范围 0-9999', trigger: 'blur' },
  ],
  effectiveDays: [
    { required: true, message: '请输入有效天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 9999, message: '有效天数范围 1-9999', trigger: 'blur' },
  ],
  vipFee: [
    { required: true, message: '请输入会费', trigger: 'blur' },
    { type: 'number', min: 0, max: 99999999, message: '会费范围 0-99999999', trigger: 'blur' },
  ],
  consumptionType: [
    { required: true, message: '请选择消费类型', trigger: 'change' },
  ],
  monies: [
    { required: true, message: '请至少添加一个充值档位', trigger: 'change' },
    { type: 'array', min: 1, message: '请至少添加一个充值档位', trigger: 'change' },
  ],
}

// 快速时间选择选项
export const QUICK_DATE_OPTIONS = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '明天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() + 1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setDate(end.getDate() + 1)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本周',
    value: () => {
      const start = new Date()
      const day = start.getDay()
      const diff = start.getDate() - day + (day === 0 ? -6 : 1)
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const start = new Date()
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setMonth(end.getMonth() + 1, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
]

// 默认表单数据
export const DEFAULT_VIP_CARD_POLICY_FORM = {
  name: '',
  description: '',
  consumptionType: 0, // 消费类型：电影演出、电商
  cardType: CardType.STORE_VALUE,
  status: VipCardPolicyStatus.DRAFT,
  sort: 0,
  displayDiscountPrice: DisplayDiscountPrice.NOT_DISPLAY,
  displaySort: 0,
  effectiveDays: 365,
  vipFee: 0,
  cardCoverText: '',
  discountText1: '',
  discountText2: '',
  discountText3: '',
  monies: [{
    id: '',
    internalVipCardId: 0,
    amount: 0,
  }], // 添加默认的充值档位数组
  rules: [],
}

// 工具函数
export const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

export const getVipCardPolicyStatusText = (status: VipCardPolicyStatus): string => {
  return VIP_CARD_POLICY_STATUS_MAP[status]?.label || '未知'
}

export const getVipCardPolicyStatusType = (status: VipCardPolicyStatus): string => {
  return VIP_CARD_POLICY_STATUS_MAP[status]?.type || 'info'
}

export const getCardTypeText = (cardType: CardType): string => {
  return CARD_TYPE_MAP[cardType]?.label || '未知'
}

export const getCardTypeType = (cardType: CardType): string => {
  return CARD_TYPE_MAP[cardType]?.type || 'info'
}

export const getDisplayDiscountPriceText = (displayDiscountPrice: DisplayDiscountPrice): string => {
  return DISPLAY_DISCOUNT_PRICE_MAP[displayDiscountPrice]?.label || '未知'
}

export const getDisplayDiscountPriceType = (displayDiscountPrice: DisplayDiscountPrice): string => {
  return DISPLAY_DISCOUNT_PRICE_MAP[displayDiscountPrice]?.type || 'info'
}
