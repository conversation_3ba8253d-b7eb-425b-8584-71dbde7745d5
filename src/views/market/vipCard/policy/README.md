# 卡政策管理模块

## 📋 模块概述

卡政策管理模块是会员系统的重要组成部分，提供完整的卡政策创建、编辑、查看和管理功能。基于API文档 `src/views/market/vipCard/会员 api.md` 实现，确保与后端接口完全兼容。

## 🏗️ 项目结构

```
src/views/market/vipCard/policy/
├── 📁 components/                    # 组件目录
│   ├── PolicySearchForm.vue         # 搜索表单组件
│   ├── PolicyTable.vue              # 卡政策列表表格组件
│   ├── PolicyConfigDialog.vue       # 卡政策配置对话框组件
│   └── PolicyDetailDialog.vue       # 卡政策详情对话框组件
├── 📁 types/                         # 类型定义
│   └── index.ts                     # 卡政策相关类型定义
├── 📁 constants/                     # 常量定义
│   └── index.ts                     # 卡政策相关常量和工具函数
├── index.vue                         # 卡政策管理主页面
└── README.md                         # 本文档

src/api/modules/market/vipCard/
├── index.ts                          # 卡政策API接口
└── 会员 api.md                       # API文档

src/router/modules/
└── vipCard.ts                        # 卡政策路由配置
```

## 🎯 核心功能

### 1. **卡政策列表管理**
- ✅ 卡政策列表展示（表格形式）
- ✅ 多条件搜索和筛选
- ✅ 分页显示
- ✅ 批量操作（删除）
- ✅ 状态管理（上架/下架）

### 2. **卡政策配置**
- ✅ 新增卡政策
- ✅ 编辑卡政策
- ✅ 卡政策详情查看
- ✅ 充值档位配置
- ✅ 权益规则配置

### 3. **数据管理**
- ✅ 完整的类型定义
- ✅ 表单验证
- ✅ 错误处理
- ✅ 加载状态管理

## 🔧 技术特性

### **前端技术栈**
- **Vue 3** - 组合式API
- **TypeScript** - 类型安全
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理（可选）

### **代码特性**
- 📝 **完整的TypeScript支持**
- 🎨 **响应式设计**
- 🔄 **组件化架构**
- 📱 **移动端适配**
- 🛡️ **错误边界处理**
- 🎯 **性能优化**

## 📊 数据结构

### **卡政策基础信息**
```typescript
interface VipCardPolicy {
  id: string                    // 政策ID
  name: string                  // 影城卡名称
  description: string           // 影城卡详情介绍
  cardType: number              // 卡类型：储值卡0
  status: number                // 状态: 下架0 上架1 草稿2
  sort: number                  // 推荐排序
  displayDiscountPrice: number  // 是否显示优惠价：不显示0 显示1
  displaySort: number           // 展示排序
  effectiveDays: number         // 开卡时间起365天有效（默认）
  vipFee: number                // 会费 分
  cardCoverText: string         // 卡片封面文案
  discountText1: string         // 影城卡权益1
  discountText2: string         // 影城卡权益2
  discountText3: string         // 影城卡权益3
  cinemas: number[]             // 适用影院
  monies: Money[]               // 充值档位
  createBy: string              // 创建人
  updateTime: number            // 最后修改时间
  rules: Rule[]                 // 权益规则
}
```

### **搜索参数**
```typescript
interface VipCardPolicyListParams {
  page?: number                 // 页码
  size?: number                 // 每页数量
  name?: string                 // 影城卡名称
  id?: string                   // 影城卡ID
  status?: number               // 状态
  startTime?: number            // 创建时间-开始时间
  endTime?: number              // 创建时间-结束时间
}
```

## 🎨 组件说明

### **1. PolicySearchForm.vue**
**功能**: 卡政策搜索和筛选表单
**特性**:
- 基础搜索（名称、状态、时间）
- 高级搜索（影城卡ID）
- 实时搜索
- 导出功能

### **2. PolicyTable.vue**
**功能**: 卡政策列表表格展示
**特性**:
- 表格展示卡政策信息
- 状态标签显示
- 操作按钮（查看、编辑、删除、状态切换）
- 批量选择

### **3. PolicyConfigDialog.vue**
**功能**: 卡政策配置对话框
**特性**:
- 支持新增、编辑、查看三种模式
- 表单验证
- 充值档位配置
- 权益规则配置
- 响应式布局

### **4. PolicyDetailDialog.vue**
**功能**: 卡政策详情展示对话框
**特性**:
- 完整的卡政策信息展示
- 充值档位详情展示
- 权益规则详情展示

## 🚀 使用指南

### **1. 页面访问**
```
卡政策列表: /market/vipCard/policy/list
新增卡政策: /market/vipCard/policy/create
编辑卡政策: /market/vipCard/policy/edit/:id
卡政策详情: /market/vipCard/policy/detail/:id
```

### **2. 组件使用**
```vue
<!-- 在其他页面中使用卡政策配置对话框 -->
<PolicyConfigDialog
  v-model="showDialog"
  :policy-id="policyId"
  :page-mode="pageMode"
  @submit="handleSubmit"
  @success="handleSuccess"
/>
```

### **3. API调用**

```typescript
import {getVipCardPolicyList, createVipCardPolicy} from 'src/api/modules/market/vipCard'

// 获取卡政策列表
const response = await getVipCardPolicyList({
    page: 1,
    size: 20,
    name: '政策名称'
})

// 创建卡政策
await createVipCardPolicy({
    name: '新政策',
    description: '政策详情',
    // ... 其他参数
})
```

## 🔄 开发模式

### **模拟数据**
开发阶段使用模拟数据，生产环境切换到真实API：

```typescript
// 开发环境
const response = await mockGetVipCardPolicyList(params)

// 生产环境
// const response = await getVipCardPolicyList(params)
```

### **切换方式**
1. 注释掉模拟API调用
2. 取消注释真实API调用
3. 确保后端接口可用

## 📋 待完善功能

### **短期计划**
- [ ] 卡政策新增/编辑功能
- [ ] 卡政策导出功能
- [ ] 卡政策批量操作

### **长期计划**
- [ ] 卡政策统计图表
- [ ] 卡政策模板功能
- [ ] 卡政策复制功能

## 🧪 测试建议

### **功能测试**
1. **基础功能**：创建、编辑、删除、查看卡政策
2. **搜索功能**：各种搜索条件组合测试
3. **表单验证**：必填字段、格式验证
4. **状态管理**：上架/下架状态切换

### **兼容性测试**
1. **浏览器兼容**：Chrome、Firefox、Safari、Edge
2. **设备兼容**：桌面端、平板、手机
3. **分辨率适配**：各种屏幕尺寸

### **性能测试**
1. **大数据量**：测试大量卡政策数据的加载性能
2. **内存使用**：长时间使用的内存泄漏检测

## 🔗 相关文档

- [API文档](../../../api/modules/market/vipCard/会员 api.md)
- [卡赠礼模块](../gift/README.md)
- [会员卡模块](../card/README.md)
- [路由配置](../../../router/modules/market/vipCard.ts)
- [类型定义](./types/index.ts)

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看控制台错误**：检查浏览器控制台的错误信息
2. **检查网络请求**：确认API请求是否正常
3. **验证数据格式**：确保传递的数据格式正确
4. **查看文档**：参考API文档和类型定义

---

**开发状态**: ✅ 已完成基础功能  
**维护状态**: 🔄 持续维护  
**版本**: v1.0.0