<template>
  <ElDialog
    v-model="dialogVisible"
    title="卡政策详情"
    :width="800"
    destroy-on-close
  >
    <div v-loading="loading" class="space-y-6">
      <div v-if="policyData" class="space-y-6">
        <!-- 基础信息 -->
        <div class="mb-6 p-5 bg-white border border-gray-200 rounded-lg last:mb-0">
          <h3 class="mb-4 text-base font-semibold text-gray-900 border-b border-gray-100 pb-2">
            基础信息
          </h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">影城卡名称：</label>
              <span class="text-gray-900">{{ policyData.name }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">消费类型：</label>
              <ElTag type="primary">
                {{ policyData.consumptionType === 0 ? '电影演出' : '电商' }}
              </ElTag>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">卡类型：</label>
              <ElTag type="primary">
                {{ getCardTypeText(policyData.cardType) }}
              </ElTag>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">状态：</label>
              <ElTag type="primary">
                {{ policyData.status === 0 ? '正常' : '停用' }}
              </ElTag>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">是否显示优惠价：</label>
              <ElTag >
                {{ policyData.displayDiscountPrice === 0 ? '不显示' : '显示' }}
              </ElTag>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">推荐排序：</label>
              <span class="text-gray-900">{{ policyData.sort }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">展示排序：</label>
              <span class="text-gray-900">{{ policyData.displaySort }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">有效天数：</label>
              <span class="text-gray-900">{{ policyData.effectiveDays }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">会费(分)：</label>
              <span class="text-gray-900">{{ policyData.vipFee }}</span>
            </div>
          </div>
        </div>

        <!-- 影城卡详情介绍 -->
        <div class="mb-6 p-5 bg-white border border-gray-200 rounded-lg last:mb-0">
          <h3 class="mb-4 text-base font-semibold text-gray-900 border-b border-gray-100 pb-2">
            影城卡详情介绍
          </h3>
          <div class="p-4 bg-gray-50 rounded-md leading-relaxed text-gray-900">
            {{ policyData.description }}
          </div>
        </div>

        <!-- 卡片文案 -->
        <div class="mb-6 p-5 bg-white border border-gray-200 rounded-lg last:mb-0">
          <h3 class="mb-4 text-base font-semibold text-gray-900 border-b border-gray-100 pb-2">
            卡片文案
          </h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">卡片封面文案：</label>
              <span class="text-gray-900">{{ policyData.cardCoverText }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">影城卡权益1：</label>
              <span class="text-gray-900">{{ policyData.discountText1 }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">影城卡权益2：</label>
              <span class="text-gray-900">{{ policyData.discountText2 }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">影城卡权益3：</label>
              <span class="text-gray-900">{{ policyData.discountText3 }}</span>
            </div>
          </div>
        </div>

        <!-- 充值档位 -->
        <div class="mb-6 p-5 bg-white border border-gray-200 rounded-lg last:mb-0">
          <h3 class="mb-4 text-base font-semibold text-gray-900 border-b border-gray-100 pb-2">
            充值档位
          </h3>
          <ElTable :data="policyData.monies" border>
            <ElTableColumn prop="amount" label="金额(分)" width="150" align="center" />
          </ElTable>
        </div>

        <!-- 权益规则 -->
        <div v-for="(rule, index) in policyData.rules" :key="rule.id" class="mb-6 p-5 bg-white border border-gray-200 rounded-lg last:mb-0">
          <h3 class="mb-4 text-base font-semibold text-gray-900 border-b border-gray-100 pb-2">
            权益规则 {{ index + 1 }}
          </h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">适用影院：</label>
              <span class="text-gray-900">{{ rule.canUseCinemaType === 0 ? '全部影院' : '指定影院' }}</span>
            </div>
            <div class="flex items-center">
              <label class="min-w-[120px] font-medium text-gray-600">影票优惠类型：</label>
              <span class="text-gray-900">{{ rule.ticketDiscountType === 0 ? '统一规则设置' : '按类型设置优惠' }}</span>
            </div>
          </div>

          <!-- 更详细的规则信息 -->
          <div class="mt-4 p-3 bg-gray-50 rounded-md" v-if="rule.hallType">
            <h4 class="mb-3 text-sm font-medium text-gray-900">影厅类型设置</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div class="flex items-center">
                <label class="min-w-[120px] font-medium text-gray-600">按影厅类型优惠：</label>
                <span class="text-gray-900">{{ rule.hallType.hallTypeDiscount === 1 ? '是' : '否' }}</span>
              </div>
              <div class="flex items-center">
                <label class="min-w-[120px] font-medium text-gray-600">按影片类型优惠：</label>
                <span class="text-gray-900">{{ rule.hallType.filmTypeDiscount === 1 ? '是' : '否' }}</span>
              </div>
            </div>
          </div>

          <div class="mt-4 p-3 bg-gray-50 rounded-md" v-if="rule.tickets && rule.tickets.length">
            <h4 class="mb-3 text-sm font-medium text-gray-900">影票优惠设置</h4>
            <ElTable :data="rule.tickets" border>
              <ElTableColumn prop="discountType" label="折扣类型" width="100">
                <template #default="{ row }">
                  {{ row.discountType === 0 ? '固定价' : '其他' }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="discountValue" label="折扣数值" width="120" />
            </ElTable>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-10">
        <ElEmpty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <ElButton @click="dialogVisible = false">
          关闭
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
// 类型导入
import type { VipCardPolicy } from '../types'
import { ElMessage } from 'element-plus'

import { computed, ref, watch } from 'vue'
// API 导入（后续需要实现）
import { getVipCardPolicyDetail } from '@/api/modules/market/cardVip/index'

// 常量导入
import {
  getVipCardPolicyStatusText,
  getVipCardPolicyStatusType,
  getCardTypeText,
  getCardTypeType
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  policyId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 加载状态
const loading = ref(false)

// 卡政策数据
const policyData = ref<VipCardPolicy | null>(null)

// ==================== 监听器 ====================

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.policyId) {
    fetchPolicyDetail()
  }
})

// ==================== 方法定义 ====================

/**
 * 获取卡政策详情
 */
async function fetchPolicyDetail() {
  if (!props.policyId) { return }

  try {
    loading.value = true

    // 生产环境使用真实API
    const response = await getVipCardPolicyDetail({ cardId: props.policyId })
    console.log('获取卡政策详情响应:', response)
    // 模拟数据
    policyData.value = response.data
  }
  catch (error) {
    console.error('获取卡政策详情失败:', error)
    ElMessage.error('获取卡政策详情失败')
    policyData.value = null
  }
  finally {
    loading.value = false
  }
}
</script>
