<script setup>
import { FullScreen } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import cinemaSelector from '@/components/cinemaSelector/index.vue'
import BasicInfoSection from './sections/BasicInfoSection.vue'
import CardTextSection from './sections/CardTextSection.vue'
import RechargeLevelsSection from './sections/RechargeLevelsSection.vue'
import RuleContainer from './sections/RuleContainer.vue'

const props = defineProps({
  modelValue: Boolean,
  policyId: String,
  pageMode: String,
})

const emit = defineEmits(['update:modelValue', 'submit', 'success', 'cancel'])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  consumptionType: 0,
  status: 1,
  cardType: 0,
  effectiveDays: 365,
  vipFee: 0,
  displayDiscountPrice: 1,
  displaySort: 0,
  sort: 0,
  discountText1: '',
  discountText2: '',
  discountText3: '',
  cardCoverText: '',
  // 适用影院
  cinemas: [],
  // 充值档位
  monies: [
    { amount: 0 },
  ],
  // 权益规则
  rules: [
    {
      id: 0,
      internalVipCardId: 0,
      canUseCinemaType: 0,
      ticketDiscountType: 0,
      generalSetting: 0,
      excludeFilm: 0,
      includeFilm: 0,
      generalEachSchedule: 0,
      generalEachScheduleNum: 0,
      generalEachFilm: 0,
      generalEachFilmNum: 0,
      generalEachDay: 0,
      generalEachDayNum: 0,
      includeFilmEachSchedule: 0,
      includeFilmEachScheduleNum: 0,
      includeFilmEachFilm: 0,
      includeFilmEachFilmNum: 0,
      includeFilmEachDay: 0,
      includeFilmEachDayNum: 0,
      servicePriceReduction: 0,
      servicePriceAmount: 0,
      goodsDiscountSetting: 0,
      goodsDiscountType: 0,
      hallType: {
        id: 0,
        internalVipCardId: 0,
        internalVipCardDiscountRuleId: 0,
        hallTypeDiscount: 0,
        filmTypeDiscount: 0,
        hallTypeSelect: [],
        filmTypeSelect: [],
      },
      // 统一规则设置
      unifiedRules: [
        {
          id: 0,
          priceType: 0, // 0: 固定价格
          fixedAmount: 0, // 固定金额（分）
          discountDays: [0, 1, 2, 3, 4, 5, 6], // 优惠日期（0-6代表周一到周日）
          startTime: '', // 开始时间
          endTime: '', // 结束时间
        },
      ],
      // 按类型设置
      typeSettings: {
        hallTypes: [
          'N',
          'V',
          'I',
          '4',
          'D',
        ],
        filmTypes: [
          '2D',
          '3D',
          'IMAX2D',
          'IMAX3D',
          '4D',
          'D',
        ],
      },
      cinemas: [],
      tickets: [],
      hallFilms: [],
      films: [],
      goodsTypes: [],
    },
  ],
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入影城卡名称', trigger: 'blur' },
    { min: 2, max: 50, message: '影城卡名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入影城卡详情介绍', trigger: 'blur' },
    { min: 1, max: 500, message: '影城卡详情介绍长度在 1 到 500 个字符', trigger: 'blur' },
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  cardType: [{ required: true, message: '请选择卡类型', trigger: 'change' }],
  effectiveDays: [
    { required: true, message: '请输入有效期', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: '有效期范围1-3650天', trigger: 'blur' },
  ],
  vipFee: [
    { required: true, message: '请输入会员费', trigger: 'blur' },
    { type: 'number', min: 0, max: 100000000, message: '会员费范围0-100000000分', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入推荐排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '推荐排序范围0-9999', trigger: 'blur' },
  ],
  displaySort: [
    { required: true, message: '请输入展示排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '展示排序范围0-9999', trigger: 'blur' },
  ],
  displayDiscountPrice: [{ required: true, message: '请选择是否显示优惠价', trigger: 'change' }],

})

// 对话框标题
const dialogTitle = computed(() => {
  const modeText = props.pageMode === 'add' ? '新增' : props.pageMode === 'edit' ? '编辑' : '查看'
  return `${modeText}卡政策`
})

// 表单引用
const formRef = ref()

// 全屏功能
const isFullscreen = ref(true)
const dialogRef = ref()

// 切换全屏
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) { return }

  try {
    // 基础表单验证
    await formRef.value.validate()

    // 验证权益规则
    for (let i = 0; i < formData.rules.length; i++) {
      const rule = formData.rules[i]

      // 验证适用影院类型
      if (rule.canUseCinemaType === undefined || rule.canUseCinemaType === null) {
        ElMessage.error(`权益规则 ${i + 1}：请选择适用影院类型`)
        return
      }

      // 验证适用影院（当选择指定影院时）
      if (rule.canUseCinemaType === 1 && (!rule.cinemas || rule.cinemas.length === 0)) {
        ElMessage.error(`权益规则 ${i + 1}：请选择适用影院`)
        return
      }

      // 验证影票优惠类型
      if (rule.ticketDiscountType === undefined || rule.ticketDiscountType === null) {
        ElMessage.error(`权益规则 ${i + 1}：请选择影票优惠类型`)
        return
      }

      // 验证服务费减免
      if (rule.servicePriceReduction === undefined || rule.servicePriceReduction === null) {
        ElMessage.error(`权益规则 ${i + 1}：请选择服务费减免方式`)
        return
      }

      // 验证服务费金额（当服务费减免时）
      if (rule.servicePriceReduction === 0 && (rule.servicePriceAmount === undefined || rule.servicePriceAmount === null)) {
        ElMessage.error(`权益规则 ${i + 1}：请输入服务费金额`)
        return
      }

      // 验证卖品优惠设置
      if (rule.goodsDiscountSetting === undefined || rule.goodsDiscountSetting === null) {
        ElMessage.error(`权益规则 ${i + 1}：请选择卖品优惠设置`)
        return
      }

      // 验证卖品优惠类型（当卖品有优惠时）
      if (rule.goodsDiscountSetting === 1 && (rule.goodsDiscountType === undefined || rule.goodsDiscountType === null)) {
        ElMessage.error(`权益规则 ${i + 1}：请选择卖品优惠类型`)
        return
      }

      // 验证影片购票优惠限制
      if (rule.generalSetting === undefined || rule.generalSetting === null) {
        ElMessage.error(`权益规则 ${i + 1}：请选择影片购票优惠限制设置`)
        return
      }

      // 验证排除影片和指定影片互斥
      if (rule.excludeFilm === 1 && rule.includeFilm === 1) {
        ElMessage.error(`权益规则 ${i + 1}：排除影片和指定影片不能同时勾选`)
        return
      }

      // 验证通用设置的数量字段
      if (rule.generalSetting === 1) {
        if (rule.generalEachSchedule === 1 && (rule.generalEachScheduleNum === undefined || rule.generalEachScheduleNum === null || rule.generalEachScheduleNum < 1)) {
          ElMessage.error(`权益规则 ${i + 1}：请填写通用设置-每场可优惠票数（至少1张）`)
          return
        }
        if (rule.generalEachFilm === 1 && (rule.generalEachFilmNum === undefined || rule.generalEachFilmNum === null || rule.generalEachFilmNum < 1)) {
          ElMessage.error(`权益规则 ${i + 1}：请填写通用设置-每部影片可优惠票数（至少1张）`)
          return
        }
        if (rule.generalEachDay === 1 && (rule.generalEachDayNum === undefined || rule.generalEachDayNum === null || rule.generalEachDayNum < 1)) {
          ElMessage.error(`权益规则 ${i + 1}：请填写通用设置-每天可优惠票数（至少1张）`)
          return
        }
      }

      // 验证指定影片设置的数量字段
      if (rule.includeFilm === 1) {
        if (rule.includeFilmEachSchedule === 1 && (rule.includeFilmEachScheduleNum === undefined || rule.includeFilmEachScheduleNum === null || rule.includeFilmEachScheduleNum < 1)) {
          ElMessage.error(`权益规则 ${i + 1}：请填写指定影片-每场可优惠票数（至少1张）`)
          return
        }
        if (rule.includeFilmEachFilm === 1 && (rule.includeFilmEachFilmNum === undefined || rule.includeFilmEachFilmNum === null || rule.includeFilmEachFilmNum < 1)) {
          ElMessage.error(`权益规则 ${i + 1}：请填写指定影片-每部影片可优惠票数（至少1张）`)
          return
        }
        if (rule.includeFilmEachDay === 1 && (rule.includeFilmEachDayNum === undefined || rule.includeFilmEachDayNum === null || rule.includeFilmEachDayNum < 1)) {
          ElMessage.error(`权益规则 ${i + 1}：请填写指定影片-每天可优惠票数（至少1张）`)
          return
        }
      }
    }

    // 构建提交数据
    const submitData = { ...formData }

    // 处理充值档位数据
    submitData.monies = submitData.monies.filter(money => money.amount > 0)
    if (submitData.monies.length === 0) {
      ElMessage.error('请至少设置一个有效的充值档位')
      return
    }

    // 处理权益规则数据
    submitData.rules = submitData.rules.map(rule => ({
      ...rule,
      // 如果服务费不减免，清空服务费金额
      servicePriceAmount: rule.servicePriceReduction === 1 ? 0 : rule.servicePriceAmount,
    }))

    // 处理每个规则的影院数据
    submitData.rules = submitData.rules.map((rule) => {
      // 如果规则选择全部影院，清空该规则的影院数据
      if (rule.canUseCinemaType === 0) {
        return {
          ...rule,
          cinemas: [],
        }
      }
      return rule
    })

    // 清空全局的cinemas数据，因为现在每个规则都有自己的影院数据
    submitData.cinemas = []

    // 构建符合接口要求的数据结构
    const apiData = {
      id: submitData.id || undefined,
      name: submitData.name,
      consumptionType: submitData.consumptionType,
      cardType: submitData.cardType,
      status: submitData.status,
      description: submitData.description,
      effectiveDays: submitData.effectiveDays,
      vipFee: submitData.vipFee,
      sort: submitData.sort,
      displaySort: submitData.displaySort,
      displayDiscountPrice: submitData.displayDiscountPrice,
      cardCoverText: submitData.cardCoverText,
      discountText1: submitData.discountText1,
      discountText2: submitData.discountText2,
      discountText3: submitData.discountText3,
      cinemas: submitData.cinemas,
      monies: submitData.monies,
      rules: submitData.rules,
    }

    // TODO: 调用保存接口
    console.log('提交数据:', apiData)

    ElMessage.success('保存成功')
    emit('success')
    dialogVisible.value = false
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
function handleCancel() {
  dialogVisible.value = false
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, newLocal)
}

function addUnifiedRule(ruleIndex) {
  console.log('添加统一规则', formData.rules[ruleIndex])
  formData.rules[ruleIndex].unifiedRules.push({
    id: 0,
    priceType: 0, // 0: 固定价格
    fixedAmount: 0, // 固定金额（分）
    discountDays: [], // 优惠日期（0-6代表周一到周日）
    startTime: '', // 开始时间
    endTime: '', // 结束时间
  })
}
// 新权益设置
const newLocal = {
  id: 0,
  internalVipCardId: 0,
  canUseCinemaType: 0,
  ticketDiscountType: 0,
  generalSetting: 0,
  excludeFilm: 0,
  includeFilm: 0,
  generalEachSchedule: 0,
  generalEachScheduleNum: 0,
  generalEachFilm: 0,
  generalEachFilmNum: 0,
  generalEachDay: 0,
  generalEachDayNum: 0,
  includeFilmEachSchedule: 0,
  includeFilmEachScheduleNum: 0,
  includeFilmEachFilm: 0,
  includeFilmEachFilmNum: 0,
  includeFilmEachDay: 0,
  includeFilmEachDayNum: 0,
  servicePriceReduction: 0,
  servicePriceAmount: 0,
  goodsDiscountSetting: 0,
  goodsDiscountType: 0,
  hallType: {
    id: 0,
    internalVipCardId: 0,
    internalVipCardDiscountRuleId: 0,
    hallTypeDiscount: 0,
    filmTypeDiscount: 0,
    hallTypeSelect: {
      isSelectAll: true,
      types: [],
    },
    filmTypeSelect: {
      isSelectAll: true,
      types: [],
    },
  },
  // 统一规则设置
  unifiedRules: [
    {
      id: 0,
      priceType: 0, // 0: 固定价格
      fixedAmount: 0, // 固定金额（分）
      discountDays: [], // 优惠日期（0-6代表周一到周日）
      startTime: '', // 开始时间
      endTime: '', // 结束时间
    },
  ],
  // 按类型设置
  typeSettings: {
    hallTypes: [
      { id: 1, name: '普通厅', selected: false },
      { id: 2, name: 'VIP厅', selected: false },
      { id: 3, name: 'IMAX厅', selected: false },
      { id: 4, name: '4DX厅', selected: false },
      { id: 5, name: '杜比全景声厅', selected: false },
    ],
    filmTypes: [
      { id: 1, name: '2D', selected: false },
      { id: 2, name: '3D', selected: false },
      { id: 3, name: 'IMAX 2D', selected: false },
      { id: 4, name: 'IMAX 3D', selected: false },
      { id: 5, name: '4DX', selected: false },
      { id: 6, name: '杜比全景声', selected: false },
    ],
  },
  cinemas: [],
  tickets: [],
  hallFilms: [],
  films: [],
  goodsTypes: [],
}
// 更新表单数据
function updateFormData(newData) {
  Object.assign(formData, newData)
}

// 添加权益规则
function addRule() {
  if (formData.rules.length >= 8) {
    ElMessage.warning('最多只能添加8条权益规则')
    return
  }
  formData.rules.push(newLocal)
}

// 删除权益规则
function removeRule(index) {
  if (formData.rules.length > 1) {
    formData.rules.splice(index, 1)
  }
  else {
    ElMessage.warning('至少保留一条权益规则')
  }
}

// 监听编辑数据变化
watch(
  () => props.editData,
  (newData) => {
    if (newData) {
      console.log('formData', formData)
      // 不再处理全局的cinemas数据，因为现在每个规则都有自己的影院数据
    }
    else {
      // 重置表单数据
      resetForm()
    }
  },
  { immediate: true, deep: true },
)
</script>

<template>
  <ElDialog
    ref="dialogRef" v-model="dialogVisible" :title="dialogTitle"
    :width="isFullscreen ? '100%' : '80%'" :fullscreen="isFullscreen" :close-on-click-modal="false" @close="handleCancel"
  >
    <template #header>
      <div class="w-full flex items-center justify-between pr-8">
        <span>{{ dialogTitle }}</span>
        <ElButton link size="small" class="ml-2 hover:bg-gray-100" @click="toggleFullscreen">
          <FullScreen class="h-4 w-4" />
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </ElButton>
      </div>
    </template>
    <div class="overflow-y-auto p-5" :class="[isFullscreen ? 'max-h-[calc(100vh-120px)]' : 'max-h-[70vh]']">
      <!-- 配置表单 -->
      <ElForm ref="formRef" :model="formData" :rules="rules" label-width="140px" label-position="right">
        <ElRow :gutter="20">
          <pre class="h-100 w-full overflow-auto text-blue">
        {{ formData }}
      </pre>
          <!-- 基本信息 -->
          <BasicInfoSection v-model:form-data="formData" />

          <!-- 充值档位 -->
          <RechargeLevelsSection v-model:form-data="formData" />

          <!-- 卡片文案 -->
          <CardTextSection v-model:form-data="formData" />

          <!-- 权益规则配置 -->
          <ElCol :span="24">
            <div class="mb-4 flex items-center justify-between">
              <h3 class="text-lg font-semibold">
                权益规则配置
              </h3>
            </div>
            <ElDivider />
          </ElCol>

          <!-- 多组权益规则 -->
          <ElCol :span="24">
            <RuleContainer
              v-for="(rule, ruleIndex) in formData.rules" :key="ruleIndex" :rule="rule"
              :rule-index="ruleIndex" :show-delete="formData.rules.length > 1" @remove-rule="removeRule"
              @add-unified-rule="addUnifiedRule"
            />
          </ElCol>

          <!-- 底部添加规则按钮 -->
          <div class="mt-4 flex justify-center">
            <ElButton type="primary" @click="addRule">
              <i class="i-ri:add-line mr-1" />
              增加一组规则
            </ElButton>
          </div>
        </ElRow>
      </ElForm>
    </div>

    <template #footer>
      <span class="flex justify-center gap-5">
        <ElButton @click="handleCancel">取 消</ElButton>
        <ElButton v-if="pageMode !== 'view'" type="primary" @click="handleSubmit">确 定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>
