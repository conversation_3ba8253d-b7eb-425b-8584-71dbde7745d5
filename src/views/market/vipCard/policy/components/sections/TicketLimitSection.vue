<script setup lang="ts">
interface Rule {
  generalSetting: number
  excludeFilm: number
  includeFilm: number
  generalEachSchedule: number
  generalEachScheduleNum: number
  generalEachFilm: number
  generalEachFilmNum: number
  generalEachDay: number
  generalEachDayNum: number
  includeFilmEachSchedule: number
  includeFilmEachScheduleNum: number
  includeFilmEachFilm: number
  includeFilmEachFilmNum: number
  includeFilmEachDay: number
  includeFilmEachDayNum: number
}

interface Props {
  rule: Rule
  ruleIndex: number
}

defineProps<Props>()
</script>

<template>
  <div>
    <h5 class="text-md mb-3 font-medium">
      影片购票优惠限制
    </h5>

    <ElRow :gutter="20">
      <ElCol :span="12">
        <ElFormItem label="通用设置" :prop="`rules[${ruleIndex}].generalSetting`">
          <ElSelect v-model="rule.generalSetting" placeholder="请选择通用设置">
            <ElOption label="不勾选" :value="0" />
            <ElOption label="勾选" :value="1" />
          </ElSelect>
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="排除影片" :prop="`rules[${ruleIndex}].excludeFilm`">
          <ElSelect
            v-model="rule.excludeFilm"
            placeholder="请选择排除影片"
            @change="(val) => val === 1 && (rule.includeFilm = 0)"
          >
            <ElOption label="不勾选" :value="0" />
            <ElOption label="勾选" :value="1" />
          </ElSelect>
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="指定影片" :prop="`rules[${ruleIndex}].includeFilm`">
          <ElSelect
            v-model="rule.includeFilm"
            placeholder="请选择指定影片"
            @change="(val) => val === 1 && (rule.excludeFilm = 0)"
          >
            <ElOption label="不勾选" :value="0" />
            <ElOption label="勾选" :value="1" />
          </ElSelect>
        </ElFormItem>
      </ElCol>
    </ElRow>

    <!-- 通用设置详情 -->
    <div v-if="rule.generalSetting === 1">
      <h5 class="text-md mb-3 font-medium">
        通用设置详情
      </h5>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="每场可优惠票数" :prop="`rules[${ruleIndex}].generalEachSchedule`">
            <ElSelect
              v-model="rule.generalEachSchedule"
              placeholder="请选择每场可优惠票数"
              @change="(val) => val === 0 && (rule.generalEachScheduleNum = 0)"
            >
              <ElOption label="不勾选" :value="0" />
              <ElOption label="勾选" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol v-if="rule.generalEachSchedule === 1" :span="12">
          <ElFormItem
            label="每场可优惠票数(张)"
            :prop="`rules[${ruleIndex}].generalEachScheduleNum`"
            :rules="[{ required: true, message: '请输入每场可优惠票数', trigger: 'blur' }]"
          >
            <ElInputNumber
              v-model="rule.generalEachScheduleNum"
              :min="1"
              :max="9999"
              placeholder="请输入每场可优惠票数"
              style="width: 100%;"
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="12">
          <ElFormItem label="每部影片可优惠票数" :prop="`rules[${ruleIndex}].generalEachFilm`">
            <ElSelect
              v-model="rule.generalEachFilm"
              placeholder="请选择每部影片可优惠票数"
              @change="(val) => val === 0 && (rule.generalEachFilmNum = 0)"
            >
              <ElOption label="不勾选" :value="0" />
              <ElOption label="勾选" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol v-if="rule.generalEachFilm === 1" :span="12">
          <ElFormItem
            label="每部影片可优惠票数(张)"
            :prop="`rules[${ruleIndex}].generalEachFilmNum`"
            :rules="[{ required: true, message: '请输入每部影片可优惠票数', trigger: 'blur' }]"
          >
            <ElInputNumber
              v-model="rule.generalEachFilmNum"
              :min="1"
              :max="9999"
              placeholder="请输入每部影片可优惠票数"
              style="width: 100%;"
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="12">
          <ElFormItem label="每天可优惠票数" :prop="`rules[${ruleIndex}].generalEachDay`">
            <ElSelect
              v-model="rule.generalEachDay"
              placeholder="请选择每天可优惠票数"
              @change="(val) => val === 0 && (rule.generalEachDayNum = 0)"
            >
              <ElOption label="不勾选" :value="0" />
              <ElOption label="勾选" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol v-if="rule.generalEachDay === 1" :span="12">
          <ElFormItem
            label="每天可优惠票数(张)"
            :prop="`rules[${ruleIndex}].generalEachDayNum`"
            :rules="[{ required: true, message: '请输入每天可优惠票数', trigger: 'blur' }]"
          >
            <ElInputNumber
              v-model="rule.generalEachDayNum"
              :min="1"
              :max="9999"
              placeholder="请输入每天可优惠票数"
              style="width: 100%;"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </div>

    <!-- 指定影片设置详情 -->
    <div v-if="rule.includeFilm === 1">
      <h5 class="text-md mb-3 font-medium">
        指定影片设置详情
      </h5>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="每场可优惠票数" :prop="`rules[${ruleIndex}].includeFilmEachSchedule`">
            <ElSelect
              v-model="rule.includeFilmEachSchedule"
              placeholder="请选择每场可优惠票数"
              @change="(val) => val === 0 && (rule.includeFilmEachScheduleNum = 0)"
            >
              <ElOption label="不勾选" :value="0" />
              <ElOption label="勾选" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol v-if="rule.includeFilmEachSchedule === 1" :span="12">
          <ElFormItem
            label="每场可优惠票数(张)"
            :prop="`rules[${ruleIndex}].includeFilmEachScheduleNum`"
            :rules="[{ required: true, message: '请输入每场可优惠票数', trigger: 'blur' }]"
          >
            <ElInputNumber
              v-model="rule.includeFilmEachScheduleNum"
              :min="1"
              :max="9999"
              placeholder="请输入每场可优惠票数"
              style="width: 100%;"
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="12">
          <ElFormItem label="每部影片可优惠票数" :prop="`rules[${ruleIndex}].includeFilmEachFilm`">
            <ElSelect
              v-model="rule.includeFilmEachFilm"
              placeholder="请选择每部影片可优惠票数"
              @change="(val) => val === 0 && (rule.includeFilmEachFilmNum = 0)"
            >
              <ElOption label="不勾选" :value="0" />
              <ElOption label="勾选" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol v-if="rule.includeFilmEachFilm === 1" :span="12">
          <ElFormItem
            label="每部影片可优惠票数(张)"
            :prop="`rules[${ruleIndex}].includeFilmEachFilmNum`"
            :rules="[{ required: true, message: '请输入每部影片可优惠票数', trigger: 'blur' }]"
          >
            <ElInputNumber
              v-model="rule.includeFilmEachFilmNum"
              :min="1"
              :max="9999"
              placeholder="请输入每部影片可优惠票数"
              style="width: 100%;"
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="12">
          <ElFormItem label="每天可优惠票数" :prop="`rules[${ruleIndex}].includeFilmEachDay`">
            <ElSelect
              v-model="rule.includeFilmEachDay"
              placeholder="请选择每天可优惠票数"
              @change="(val) => val === 0 && (rule.includeFilmEachDayNum = 0)"
            >
              <ElOption label="不勾选" :value="0" />
              <ElOption label="勾选" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol v-if="rule.includeFilmEachDay === 1" :span="12">
          <ElFormItem
            label="每天可优惠票数(张)"
            :prop="`rules[${ruleIndex}].includeFilmEachDayNum`"
            :rules="[{ required: true, message: '请输入每天可优惠票数', trigger: 'blur' }]"
          >
            <ElInputNumber
              v-model="rule.includeFilmEachDayNum"
              :min="1"
              :max="9999"
              placeholder="请输入每天可优惠票数"
              style="width: 100%;"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </div>
  </div>
</template>
