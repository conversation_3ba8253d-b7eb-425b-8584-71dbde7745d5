<script setup lang="ts">
import CinemaConfigSection from './CinemaConfigSection.vue'
import ServiceAndGoodsSection from './ServiceAndGoodsSection.vue'
import TicketDiscountSection from './TicketDiscountSection.vue'
import TicketLimitSection from './TicketLimitSection.vue'

interface Rule {
  canUseCinemaType: number
  cinemas: any[]
  ticketDiscountType: number
  unifiedRules?: any[]
  typeSettings?: any
  generalSetting: number
  excludeFilm: number
  includeFilm: number
  generalEachSchedule: number
  generalEachScheduleNum: number
  generalEachFilm: number
  generalEachFilmNum: number
  generalEachDay: number
  generalEachDayNum: number
  includeFilmEachSchedule: number
  includeFilmEachScheduleNum: number
  includeFilmEachFilm: number
  includeFilmEachFilmNum: number
  includeFilmEachDay: number
  includeFilmEachDayNum: number
  servicePriceReduction: number
  servicePriceAmount: number
  goodsDiscountSetting: number
  goodsDiscountType: number
}

interface Props {
  rule: Rule
  ruleIndex: number
  showDelete?: boolean
}

withDefaults(defineProps<Props>(), {
  showDelete: true,
})

defineEmits<{
  removeRule: [ruleIndex: number]
  addUnifiedRule: [ruleIndex: number]
  removeUnifiedRule: [ruleIndex: number, unifiedIndex: number]
  toggleHallTypeAll: [ruleIndex: number, value: boolean]
  updateHallTypeAllStatus: [ruleIndex: number]
  toggleFilmTypeAll: [ruleIndex: number, value: boolean]
}>()
</script>

<template>
  <div class="mb-6 border border-gray-200 rounded-lg p-4">
    <div class="mb-4 flex items-center justify-between">
      <h4 class="text-md font-medium">
        权益规则 {{ ruleIndex + 1 }}
      </h4>
      <ElButton
        v-if="showDelete"
        type="danger"
        @click="$emit('removeRule', ruleIndex)"
      >
        删除规则
      </ElButton>
    </div>

    <ElRow :gutter="20">
      <ElCol :span="24">
        <CinemaConfigSection :rule="rule" :rule-index="ruleIndex" />
      </ElCol>

      <ElCol :span="24">
        <TicketDiscountSection
          :rule="rule"
          :rule-index="ruleIndex"
          @add-unified-rule="$emit('addUnifiedRule', $event)"
        />
      </ElCol>

      <!-- 限制规则 -->
      <ElCol :span="24">
        <TicketLimitSection :rule="rule" :rule-index="ruleIndex" />
      </ElCol>

      <ElCol :span="24">
        <ServiceAndGoodsSection :rule="rule" :rule-index="ruleIndex" />
      </ElCol>
    </ElRow>
  </div>
</template>
