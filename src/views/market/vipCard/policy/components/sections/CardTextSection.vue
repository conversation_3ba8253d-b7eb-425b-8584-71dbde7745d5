<template>
  <div>
    <ElRow :gutter="20">
      <ElCol :span="24">
        <h3 class="text-lg font-semibold mb-4">卡片文案</h3>
        <ElDivider />
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="卡片封面文案" prop="cardCoverText">
          <ElInput type="textarea" v-model="formData.cardCoverText" placeholder="请输入卡片封面文案" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="影城卡权益1" prop="discountText1">
          <ElInput type="textarea" v-model="formData.discountText1" placeholder="请输入影城卡权益1" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="影城卡权益2" prop="discountText2">
          <ElInput type="textarea" v-model="formData.discountText2" placeholder="请输入影城卡权益2" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="影城卡权益3" prop="discountText3">
          <ElInput type="textarea" v-model="formData.discountText3" placeholder="请输入影城卡权益3" />
        </ElFormItem>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup>
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})
</script>
