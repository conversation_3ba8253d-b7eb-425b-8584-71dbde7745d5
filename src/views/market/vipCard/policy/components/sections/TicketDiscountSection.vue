<script setup lang="ts">
import { cinemaHallTypeList, filmVersionList } from '@/config'

interface UnifiedRule {
  priceType: number
  fixedAmount: number
  discountDays: number[]
  startTime: string
  endTime: string
}

interface TypeSettings {
  hallTypes: any[]
  filmTypes: any[]
}

interface filmTypeSelect {
  isSelectAll?: boolean
  types?: {
    typeKey: string
    typeName: string
  }[]
}

interface HallType {
  id?: number
  internalVipCardId?: number
  internalVipCardDiscountRuleId?: number
  hallTypeDiscount?: number
  filmTypeDiscount?: number
  hallTypeSelect?: string[]
  filmTypeSelect?: filmTypeSelect
}

interface Rule {
  hallType?: HallType
  ticketDiscountType: number
  hallTypeDiscount: number
  filmTypeDiscount: number
  unifiedRules?: UnifiedRule[]
  typeSettings?: TypeSettings
}

interface Props {
  rule: Rule
  ruleIndex: number
  hallTypes: HallType[]
}

defineProps<Props>()

defineEmits<{
  addUnifiedRule: [ruleIndex: number] // 添加统一规则
  removeUnifiedRule: [ruleIndex: number, unifiedIndex: number] // 删除统一规则
}>()

// 为每个统一规则创建金额显示转换
function createAmountConverter(unifiedRule: UnifiedRule) {
  return computed({
    get: () => unifiedRule.fixedAmount / 100, // 分转元
    set: (value: number) => {
      unifiedRule.fixedAmount = Math.round(value * 100) // 元转分
    },
  })
}
</script>

<template>
  <div>
    <ElFormItem label="影票优惠类型" :prop="`rules[${ruleIndex}].ticketDiscountType`">
      <ElSelect v-model="rule.ticketDiscountType" placeholder="请选择影票优惠类型">
        <ElOption label="统一规则设置" :value="0" />
        <ElOption label="按类型设置优惠" :value="1" />
      </ElSelect>
    </ElFormItem>

    <!-- 统一规则设置 -->
    <div v-if="rule.ticketDiscountType === 0">
      <h5 class="text-md mb-3 font-medium">
        统一规则设置
      </h5>
      <ElFormItem label="影票优惠设置">
        <div class="space-y-4">
          <div
            v-for="(unifiedRule, unifiedIndex) in (rule.unifiedRules || [])" :key="unifiedIndex"
            class="border border-gray-200 rounded-lg p-4"
          >
            <div class="mb-3 flex items-center justify-between">
              <h6 class="text-sm font-medium">
                优惠设置 {{ unifiedIndex + 1 }}
              </h6>
              <ElButton
                v-if="rule.unifiedRules && rule.unifiedRules.length > 1" type="danger" size="small"
                @click="$emit('removeUnifiedRule', ruleIndex, unifiedIndex)"
              >
                删除
              </ElButton>
            </div>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="价格类型" :prop="`rules[${ruleIndex}].unifiedRules[${unifiedIndex}].priceType`">
                  <ElSelect v-model="unifiedRule.priceType" placeholder="请选择价格类型">
                    <ElOption label="固定价格" :value="0" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>

              <ElCol :span="12">
                <ElFormItem
                  label="固定金额(元)" :prop="`rules[${ruleIndex}].unifiedRules[${unifiedIndex}].fixedAmount`"
                  :rules="[{ required: true, message: '请输入固定金额', trigger: 'blur' }]"
                >
                  <ElInputNumber
                    v-model="createAmountConverter(unifiedRule).value" :min="0" :max="1000" :precision="2"
                    placeholder="请输入固定金额" style="width: 100%;"
                  />
                </ElFormItem>
              </ElCol>

              <ElCol :span="24">
                <ElFormItem label="优惠日期" :prop="`rules[${ruleIndex}].unifiedRules[${unifiedIndex}].discountDays`">
                  <ElCheckboxGroup v-model="unifiedRule.discountDays">
                    <ElCheckbox :value="0">
                      周一
                    </ElCheckbox>
                    <ElCheckbox :value="1">
                      周二
                    </ElCheckbox>
                    <ElCheckbox :value="2">
                      周三
                    </ElCheckbox>
                    <ElCheckbox :value="3">
                      周四
                    </ElCheckbox>
                    <ElCheckbox :value="4">
                      周五
                    </ElCheckbox>
                    <ElCheckbox :value="5">
                      周六
                    </ElCheckbox>
                    <ElCheckbox :value="6">
                      周日
                    </ElCheckbox>
                  </ElCheckboxGroup>
                  <!-- <div class="text-sm text-gray-500 mt-1">
                    请选择优惠适用的日期
                  </div> -->
                </ElFormItem>
              </ElCol>

              <ElCol :span="12">
                <ElFormItem
                  label="开始时间" :prop="`rules[${ruleIndex}].unifiedRules[${unifiedIndex}].startTime`"
                  :rules="[{ required: true, message: '请选择开始时间', trigger: 'change' }]"
                >
                  <ElTimePicker
                    v-model="unifiedRule.startTime" placeholder="请选择开始时间" format="HH:mm"
                    value-format="HH:mm" style="width: 100%;"
                  />
                </ElFormItem>
              </ElCol>

              <ElCol :span="12">
                <ElFormItem
                  label="结束时间" :prop="`rules[${ruleIndex}].unifiedRules[${unifiedIndex}].endTime`"
                  :rules="[{ required: true, message: '请选择结束时间', trigger: 'change' }]"
                >
                  <ElTimePicker
                    v-model="unifiedRule.endTime" placeholder="请选择结束时间" format="HH:mm" value-format="HH:mm"
                    style="width: 100%;"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>

          <ElButton type="primary" size="small" @click="$emit('addUnifiedRule', ruleIndex)">
            <i class="i-ri:add-line mr-1" />
            添加优惠设置
          </ElButton>
        </div>
      </ElFormItem>
    </div>

    <!-- 按类型设置 -->
    <div v-if="rule.ticketDiscountType === 1">
      <h5 class="text-md mb-3 font-medium">
        按类型设置
      </h5>
      <div class="flex flex-col space-y-4">
        {{ rule.hallType }}
      </div>
      <ElFormItem label="影厅类型">
        <ElSwitch
          v-model="rule.hallType.hallTypeDiscount" active-text="指定" inactive-text="不限" :active-value="1"
          :inactive-value="0"
        />

        <div v-if="rule.hallType.hallTypeDiscount" class="m-1 m-l-10 b-l-1 b-color-red p-l-10 space-y-2">
          <div class="grid grid-cols-3 gap-2">
            <!-- <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
              <el-checkbox v-for="city in cities" :key="city" :label="city" :value="city">
                {{ city }}
              </el-checkbox>
            </el-checkbox-group> -->
            <div class="flex items-center">
              <ElCheckboxGroup v-model="rule.hallType.hallTypeSelect">
                <ElCheckbox v-for="hallType in filmVersionList" :key="hallType.value" :value="hallType.value">
                  {{ hallType.name }}
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </div>
        </div>
      </ElFormItem>

      <ElFormItem label="影片类型">
        <ElSwitch
          v-model="rule.hallType.filmTypeDiscount" active-text="指定" inactive-text="不限" :active-value="1"
          :inactive-value="0"
        />
        <div v-if="rule.hallType.filmTypeDiscount" class="m-1 m-l-10 b-l-1 b-color-red p-l-10 space-y-2">
          <ElCheckbox v-model="rule.hallType.filmTypeDiscount" :value="1">
            <!-- TODO 全选 -->
            全选
          </ElCheckbox>
          <div class="grid grid-cols-3 gap-2">
            <div v-for="filmType in cinemaHallTypeList" :key="filmType.value" class="flex items-center">
              <ElCheckbox :value="filmType.value">
                {{ filmType.name }}
              </ElCheckbox>
            </div>
          </div>
        </div>
      </ElFormItem>
    </div>
  </div>
</template>
