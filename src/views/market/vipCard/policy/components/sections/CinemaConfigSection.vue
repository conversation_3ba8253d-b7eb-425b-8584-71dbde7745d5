<template>
  <div>
    <ElFormItem :label="'适用影院类型'" :prop="`rules[${ruleIndex}].canUseCinemaType`">
      <ElSelect v-model="rule.canUseCinemaType" placeholder="请选择适用影院类型">
        <ElOption label="全部影院" :value="0" />
        <ElOption label="指定影院" :value="1" />
      </ElSelect>
      <div class="text-sm text-gray-500 mt-1">
        选择"指定影院"后，下方将显示适用影院选择器
      </div>
    </ElFormItem>

    <!-- 当前规则的适用影院选择 -->
    <ElFormItem v-if="rule.canUseCinemaType === 1" :label="'选择适用影院'" :prop="`rules[${ruleIndex}].cinemas`">
      <cinemaSelector v-model="rule.cinemas" multiple />
      <div class="flex flex-wrap gap-2">
        <el-tag v-for="cinema in rule.cinemas" :key="cinema.id" type="warning">{{ cinema.cinemaName }}</el-tag>
      </div>
      <div class="text-sm text-gray-500 mt-1">
        为当前权益规则选择适用的影院
      </div>
    </ElFormItem>
  </div>
</template>

<script setup lang="ts">
interface Rule {
  canUseCinemaType: number
  cinemas: any[]
}

interface Props {
  rule: Rule
  ruleIndex: number
}

defineProps<Props>()
</script>
