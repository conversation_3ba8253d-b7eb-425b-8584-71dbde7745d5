<template>
  <div>
    <ElRow :gutter="20">
      <ElCol :span="24">
        <h3 class="text-lg font-semibold mb-4">充值档位</h3>
        <ElDivider />
      </ElCol>

      <ElCol :span="24">
        <ElFormItem label="充值金额配置">
          <div class="space-y-3">
            <div v-for="(money, index) in formData.monies" :key="index" class="flex items-center gap-3">
              <ElInputNumber
                v-model="money.amount"
                :min="0"
                :max="100000000"
                placeholder="金额（分）"
                class="w-100"
              />
              <ElButton type="danger" size="small" @click="removeMoney(index)">删除</ElButton>
            </div>
            <ElButton type="primary" size="small" @click="addMoney">添加充值档位</ElButton>
          </div>
        </ElFormItem>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup>
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:formData'])

// 添加充值档位
const addMoney = () => {
  props.formData.monies.push({ amount: 0 })
}

// 删除充值档位
const removeMoney = (index) => {
  if (props.formData.monies.length > 1) {
    props.formData.monies.splice(index, 1)
  }
}
</script>
