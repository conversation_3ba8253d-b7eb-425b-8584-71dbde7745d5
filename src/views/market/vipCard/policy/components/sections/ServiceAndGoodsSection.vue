<template>
  <div>
    <ElFormItem :label="'服务费减免'" :prop="`rules[${ruleIndex}].servicePriceReduction`">
      <ElSelect v-model="rule.servicePriceReduction" placeholder="请选择服务费减免方式">
        <ElOption label="减免" :value="0" />
        <ElOption label="不减免" :value="1" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem v-if="rule.servicePriceReduction === 0" :label="'服务费金额(分)'" :prop="`rules[${ruleIndex}].servicePriceAmount`">
      <ElInputNumber
        v-model="rule.servicePriceAmount"
        :min="0"
        :max="100000000"
        placeholder="请输入服务费金额"
        style="width: 100%;"
      />
    </ElFormItem>

    <ElFormItem :label="'卖品优惠设置'" :prop="`rules[${ruleIndex}].goodsDiscountSetting`">
      <ElSelect v-model="rule.goodsDiscountSetting" placeholder="请选择卖品优惠设置">
        <ElOption label="无优惠" :value="0" />
        <ElOption label="优惠" :value="1" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem v-if="rule.goodsDiscountSetting === 1" :label="'卖品优惠类型'" :prop="`rules[${ruleIndex}].goodsDiscountType`">
      <ElSelect v-model="rule.goodsDiscountType" placeholder="请选择卖品优惠类型">
        <ElOption label="全部卖品分类" :value="0" />
        <ElOption label="指定卖品分类可用" :value="1" />
        <ElOption label="指定卖品分类不可用" :value="2" />
      </ElSelect>
    </ElFormItem>
  </div>
</template>

<script setup lang="ts">
interface Rule {
  servicePriceReduction: number
  servicePriceAmount: number
  goodsDiscountSetting: number
  goodsDiscountType: number
}

interface Props {
  rule: Rule
  ruleIndex: number
}

defineProps<Props>()
</script>