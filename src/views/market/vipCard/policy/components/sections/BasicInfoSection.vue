<template>
  <div>
    <ElRow :gutter="20">
      <ElCol :span="24">
        <h3 class="text-lg font-semibold mb-4">基本信息</h3>
        <ElDivider />
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="影城卡名称" prop="name">
          <ElInput v-model="localFormData.name" placeholder="请输入影城卡名称" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="消费类型" prop="consumptionType">
          <ElSelect v-model="localFormData.consumptionType" placeholder="请选择消费类型">
            <ElOption label="电影演出" :value="0" />
            <ElOption label="电商" :value="1" />
          </ElSelect>
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="卡类型" prop="cardType">
          <ElSelect v-model="localFormData.cardType" placeholder="请选择卡类型">
            <ElOption label="储值卡" :value="0" />
          </ElSelect>
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="状态" prop="status">
          <ElSelect v-model="localFormData.status" placeholder="请选择状态">
            <ElOption label="下架" :value="0" />
            <ElOption label="上架" :value="1" />
            <ElOption label="草稿" :value="2" />
          </ElSelect>
        </ElFormItem>
      </ElCol>

      <ElCol :span="24">
        <ElFormItem label="影城卡详情" prop="description">
          <ElInput
            v-model="localFormData.description"
            type="textarea"
            placeholder="请输入影城卡详情介绍"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="有效期(天)" prop="effectiveDays">
          <ElInputNumber
            v-model="localFormData.effectiveDays"
            :min="1"
            :max="3650"
            placeholder="开卡时间起365天有效（默认）"
            style="width: 100%;"
          />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="会费(分)" prop="vipFee">
          <ElInputNumber
            v-model="localFormData.vipFee"
            :min="0"
            :max="100000000"
            placeholder="请输入会费"
            style="width: 100%;"
          />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="推荐排序" prop="sort">
          <ElInputNumber
            v-model="localFormData.sort"
            :min="0"
            :max="9999"
            placeholder="请输入推荐排序"
            style="width: 100%;"
          />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="展示排序" prop="displaySort">
          <ElInputNumber
            v-model="localFormData.displaySort"
            :min="0"
            :max="9999"
            placeholder="请输入展示排序"
            style="width: 100%;"
          />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="是否显示优惠价" prop="displayDiscountPrice">
          <ElSelect v-model="localFormData.displayDiscountPrice" placeholder="请选择">
            <ElOption label="不显示" :value="0" />
            <ElOption label="显示" :value="1" />
          </ElSelect>
        </ElFormItem>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  // rules: {
  //   type: Object,
  //   required: true
  // }
})

const emit = defineEmits(['update:formData'])

// 创建可写的computed属性来实现双向绑定
const localFormData = computed({
  get: () => props.formData,
  set: (value) => {
    emit('update:formData', value)
  }
})
</script>
