
<script setup lang="ts">
import { ref } from 'vue'
import type { ElTable } from 'element-plus'

// 类型导入
import type { VipCardPolicy } from '../types'

// 常量导入
import {
  formatTimestamp,
  getVipCardPolicyStatusText,
  getVipCardPolicyStatusType,
  getCardTypeText,
  getCardTypeType,
  getDisplayDiscountPriceText,
  getDisplayDiscountPriceType,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  data: VipCardPolicy[]
  loading?: boolean
  selectedIds?: string[]
}

interface Emits {
  (e: 'selection-change', ids: string[]): void
  (e: 'edit', policy: VipCardPolicy): void
  (e: 'delete', policy: VipCardPolicy): void
  (e: 'view', policy: VipCardPolicy): void
  (e: 'status-change', policy: VipCardPolicy, status: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedIds: () => [],
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>()

// ==================== 方法定义 ====================

/**
 * 选择变更
 */
function handleSelectionChange(selection: VipCardPolicy[]) {
  const ids = selection.map(item => item.id)
  emit('selection-change', ids)
}

/**
 * 查看
 */
function handleView(policy: VipCardPolicy) {
  emit('view', policy)
}

/**
 * 编辑
 */
function handleEdit(policy: VipCardPolicy) {
  emit('edit', policy)
}

/**
 * 删除
 */
function handleDelete(policy: VipCardPolicy) {
  emit('delete', policy)
}

/**
 * 状态变更
 */
function handleStatusChange(policy: VipCardPolicy) {
  const newStatus = policy.status === 1 ? 0 : 1
  emit('status-change', policy, newStatus)
}

// ==================== 暴露方法 ====================

defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: VipCardPolicy, selected?: boolean) =>
      tableRef.value?.toggleRowSelection(row, selected),
})
</script>
<template>
  <div class="overflow-hidden bg-white border border-gray-200 rounded-lg">
    <ElTable
      ref="tableRef"
      :data="data"
      :loading="loading"
      row-key="id"
      stripe
      border
      class="w-full"
      :cell-class-name="'py-3 px-2'"
      :header-cell-class-name="'bg-gray-50'"
      :row-class-name="'hover:bg-gray-50'"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <ElTableColumn type="selection" width="55" align="center" />

      <!-- 影城卡名称 -->
      <ElTableColumn prop="name" label="影城卡名称" min-width="200" show-overflow-tooltip />

      <!-- 消费类型 -->
      <ElTableColumn prop="consumptionType" label="消费类型" width="100" align="center">
        <template #default="{ row }">
          <ElTag >
            {{ row.consumptionType === 0 ? '电影演出' : '电商' }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 卡类型 -->
      <ElTableColumn prop="cardType" label="卡类型" width="120" align="center">
        <template #default="{ row }">
          <ElTag >
            {{ getCardTypeText(row.cardType) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 状态 -->
      <ElTableColumn prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <ElTag >
            {{ getVipCardPolicyStatusText(row.status) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 是否显示优惠价 -->
      <ElTableColumn prop="displayDiscountPrice" label="显示优惠价" width="120" align="center">
        <template #default="{ row }">
          <ElTag >
            {{ getDisplayDiscountPriceText(row.displayDiscountPrice) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 会费 -->
      <ElTableColumn prop="vipFee" label="会费(分)" width="120" align="center" />

      <!-- 创建时间 -->
      <ElTableColumn prop="updateTime" label="最后修改时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.updateTime) }}
        </template>
      </ElTableColumn>

      <!-- 操作列 -->
      <ElTableColumn label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <div class="flex flex-wrap justify-center gap-2">
            <ElButton type="primary" size="small" text @click="handleView(row)">
              查看
            </ElButton>
            <ElButton type="warning" size="small" text @click="handleEdit(row)">
              编辑
            </ElButton>
            <ElButton
              :type="row.status === 1 ? 'danger' : 'success'"
              size="small"
              text
              @click="handleStatusChange(row)"
            >
              {{ row.status === 1 ? '下架' : '上架' }}
            </ElButton>
            <ElButton type="danger" size="small" text @click="handleDelete(row)">
              删除
            </ElButton>
          </div>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<style scoped>
/* 响应式设计 */
@media (width <= 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 1px;
  }

  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>
