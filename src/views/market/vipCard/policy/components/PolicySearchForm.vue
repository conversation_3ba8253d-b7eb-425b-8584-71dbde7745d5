<template>
  <div class="policy-search-form">
    <ElForm
      ref="formRef"
      :model="formData"
      inline
      label-width="auto"
      class="search-form"
    >
      <!-- 基础搜索 -->
      <div class="search-row">
        <ElFormItem label="影城卡名称" prop="name">
          <ElInput
            v-model="formData.name"
            placeholder="请输入影城卡名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </ElFormItem>

        <ElFormItem label="状态" prop="status">
          <ElSelect
            v-model="formData.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <ElOption
              v-for="option in VIP_CARD_POLICY_STATUS_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="创建时间" prop="dateRange">
          <ElDatePicker
            v-model="formData.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="QUICK_DATE_OPTIONS"
            style="width: 350px"
          />
        </ElFormItem>

        <!-- 操作按钮 -->
        <ElFormItem>
          <ElButton type="primary" :loading="loading" @click="handleSearch">
            搜索
          </ElButton>
          <ElButton @click="handleReset">
            重置
          </ElButton>
          <ElButton @click="toggleAdvanced">
            {{ showAdvanced ? '收起' : '展开' }}
            <ElIcon class="ml-1">
              <ArrowDown v-if="!showAdvanced" />
              <ArrowUp v-else />
            </ElIcon>
          </ElButton>
        </ElFormItem>
      </div>

      <!-- 高级搜索 -->
      <div v-show="showAdvanced" class="advanced-row">
        <ElFormItem label="影城卡ID" prop="id">
          <ElInput
            v-model="formData.id"
            placeholder="请输入影城卡ID"
            clearable
            style="width: 200px"
          />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="success" :icon="Download" @click="handleExport">
            导出
          </ElButton>
        </ElFormItem>
      </div>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ArrowDown, ArrowUp, Download } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'

// 类型导入
import type { VipCardPolicySearchForm } from '../types'

// 常量导入
import {
  VIP_CARD_POLICY_STATUS_OPTIONS,
  QUICK_DATE_OPTIONS,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  loading?: boolean
  modelValue: VipCardPolicySearchForm
}

interface Emits {
  (e: 'update:modelValue', value: VipCardPolicySearchForm): void
  (e: 'search', params: VipCardPolicySearchForm): void
  (e: 'reset'): void
  (e: 'export'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<VipCardPolicySearchForm>({
  name: '',
  id: '',
  status: null,
  dateRange: [],
})

// 高级搜索展开状态
const showAdvanced = ref(false)

// ==================== 监听器 ====================

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    Object.assign(formData, newValue)
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// ==================== 方法定义 ====================

/**
 * 搜索
 */
function handleSearch() {
  const params = { ...formData }

  // 处理时间范围
  if (params.dateRange && params.dateRange.length === 2) {
    params.startTime = new Date(params.dateRange[0]).getTime()
    params.endTime = new Date(params.dateRange[1]).getTime()
  }

  // 清理空值
  Object.keys(params).forEach((key) => {
    const value = params[key as keyof VipCardPolicySearchForm]
    if (value === '' || value === null || value === undefined) {
      delete params[key as keyof VipCardPolicySearchForm]
    }
  })

  emit('search', params)
}

/**
 * 重置
 */
function handleReset() {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: '',
    id: '',
    status: null,
    dateRange: [],
  })
  emit('reset')
}

/**
 * 导出
 */
function handleExport() {
  emit('export')
}

/**
 * 切换高级搜索
 */
function toggleAdvanced() {
  showAdvanced.value = !showAdvanced.value
}

/**
 * 回车搜索
 */
function handleKeyup(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleSearch()
  }
}
</script>

<style scoped lang="scss">
.policy-search-form {
  padding: 20px;
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  margin-bottom: 20px;
}

.search-form {
  .search-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
  }

  .advanced-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
    animation: slide-down 0.3s ease-out;
  }
}

.ml-1 {
  margin-left: 4px;
}

/* 动画效果 */
@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    .search-row,
    .advanced-row {
      flex-direction: column;
      align-items: stretch;
    }

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-input,
    .el-select,
    .el-date-picker {
      width: 100% !important;
    }
  }
}
</style>