<script setup lang="ts">

// api导入
import { getVipCardPolicyList } from '@/api/modules/market/cardVip/index'
// 类型导入
import type { VipCardPolicy, VipCardPolicyListParams } from './types'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref } from 'vue'

// PolicyConfigDialog
import PolicyConfigDialog from './components/PolicyConfigFormDialog.vue'
import PolicyDetailDialog from './components/PolicyDetailDialog.vue'
import PolicySearchForm from './components/PolicySearchForm.vue'
import PolicyTable from './components/PolicyTable.vue'
// 新增的PolicyConfigFormDialog组件
// import PolicyConfigFormDialog from './components/PolicyConfigFormDialog.vue'
// 组件导入
import { PAGINATION_CONFIG } from './constants'

const props = defineProps<{
  modelValue: boolean
  policyId?: string
  pageMode: 'add' | 'edit' | 'view'
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: any): void
  (e: 'success'): void
  (e: 'cancel'): void
}>()

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 表单数据
const formData = ref({
  id: '',
  name: '',
  consumptionType: 0, // 添加消费类型字段
  status: 1,
  cardType: 0,
  effectiveDays: 365,
  vipFee: 0,
  displayDiscountPrice: 1,
  sort: 0,
  discountText1: '',
  discountText2: '',
  discountText3: '',
  cardCoverText: '',
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入政策名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择政策状态', trigger: 'change' }],
  cardType: [{ required: true, message: '请选择卡类型', trigger: 'change' }],
  effectiveDays: [
    { required: true, message: '请输入有效期', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: '有效期范围1-3650天', trigger: 'blur' },
  ],
  vipFee: [
    { required: true, message: '请输入会员费', trigger: 'blur' },
    { type: 'number', min: 0, max: 100000000, message: '会员费范围0-100000000分', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序值范围0-999', trigger: 'blur' },
  ],
}

// 对话框标题
const dialogTitle = computed(() => {
  const modeText = props.pageMode === 'add' ? '新增' : props.pageMode === 'edit' ? '编辑' : '查看'
  return `${modeText}卡政策`
})

// 表单引用
const formRef = ref()

// 提交表单
function handleSubmit() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('submit', { ...formData.value })
      ElMessage.success('提交成功')
      emit('success')
    }
    else {
      ElMessage.error('请检查表单错误')
      return false
    }
  })
}

// 取消操作
function handleCancel() {
  emit('cancel')
}

// 重置表单
function resetForm() {
  formData.value = {
    id: '',
    name: '',
    consumptionType: 0, // 添加消费类型字段
    status: 1,
    cardType: 0,
    effectiveDays: 365,
    vipFee: 0,
    displayDiscountPrice: 1,
    sort: 0,
    discountText1: '',
    discountText2: '',
    discountText3: '',
    cardCoverText: '',
  }

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 监听对话框关闭
watch(
  () => visible.value,
  (newVal) => {
    if (!newVal) {
      handleCancel()
    }
  },
)

// 监听policyId变化
watch(
  () => props.policyId,
  (newVal) => {
    if (newVal && props.pageMode !== 'add') {
      // 这里应该调用API获取政策详情
      // 模拟数据加载
      const mockData = {
        id: newVal,
        name: props.pageMode === 'edit' ? '编辑政策名称' : '查看政策名称',
        consumptionType: 0, // 添加消费类型字段
        status: props.pageMode === 'edit' ? 1 : 0,
        cardType: 0,
        effectiveDays: 365,
        vipFee: props.pageMode === 'edit' ? 10000 : 5000,
        displayDiscountPrice: 1,
        sort: props.pageMode === 'edit' ? 1 : 2,
        discountText1: '观影9折',
        discountText2: '免费爆米花',
        discountText3: '生日特权',
        cardCoverText: '金卡专享',
      }

      formData.value = { ...mockData }
    }
    else {
      resetForm()
    }
  },
  { immediate: true },
)

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)

// 卡政策列表
const policyList = ref<VipCardPolicy[]>([])

// 搜索参数
const searchParams = ref<VipCardPolicyListParams>({
  page: 1,
  size: 20,
  name: '',
  status: null, // 修改为null而不是undefined
})

// 分页配置
const pagination = reactive({
  ...PAGINATION_CONFIG,
})

// 选中的卡政策ID
const selectedIds = ref<string[]>([])

// 对话框状态
const showDetailDialog = ref(false)
const showConfigDialog = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const viewPolicyId = ref('')
const editPolicyId = ref('')

// ==================== 生命周期 ====================

onMounted(() => {
  fetchPolicyList()
})

// ==================== 方法定义 ====================

/**
 * 获取卡政策列表
 */
async function fetchPolicyList() {
  try {
    loading.value = true

    // 生产环境使用真实API
    const response = await getVipCardPolicyList({
      ...searchParams.value,
      // ...PAGINATION_CONFIG
    }) as any

    console.log('获取卡政策列表响应:', response)
    const { code, data } = response
    if (code === 0) {
      policyList.value = data.content || []
      pagination.total = data.total || 0
    }
  }
  catch (error) {
    console.error('获取卡政策列表失败:', error)
    ElMessage.error('获取卡政策列表失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
function handleSearch(params: VipCardPolicyListParams) {
  searchParams.value = { ...params }
  pagination.page = 1
  fetchPolicyList()
}

/**
 * 重置搜索
 */
function handleReset() {
  searchParams.value = {
    page: 1,
    size: 20,
    name: '',
    status: null, // 修改为null而不是undefined
  }
  pagination.page = 1
  fetchPolicyList()
}

/**
 * 导出
 */
async function handleExport() {
  try {
    loading.value = true
    // await exportVipCardPolicyList(searchParams.value)
    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 新增卡政策
 */
function handleCreate() {
  dialogMode.value = 'add'
  editPolicyId.value = ''
  showConfigDialog.value = true
}

/**
 * 编辑卡政策
 */
function handleEdit(policy: VipCardPolicy) {
  dialogMode.value = 'edit'
  editPolicyId.value = policy.id
  showConfigDialog.value = true
}

/**
 * 查看卡政策详情
 */
function handleView(policy: VipCardPolicy) {
  viewPolicyId.value = policy.id
  showDetailDialog.value = true
}

/**
 * 删除卡政策
 */
async function handleDelete(policy: VipCardPolicy) {
  try {
    await ElMessageBox.confirm(`确定要删除政策"${policy.name}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    // await deleteVipCardPolicy(policy.id)
    ElMessage.success('删除成功')
    await fetchPolicyList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除卡政策失败:', error)
      ElMessage.error('删除卡政策失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 批量删除
 */
async function handleBatchDelete() {
  if (!selectedIds.value.length) {
    ElMessage.warning('请选择要删除的政策')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个政策吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    // await batchDeleteVipCardPolicies(selectedIds.value)
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    fetchPolicyList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 状态变更
 */
async function handleStatusChange(policy: VipCardPolicy, status: number) {
  try {
    loading.value = true
    // await updateVipCardPolicyStatus({
    //   cardId: policy.id,
    //   status,
    // })
    ElMessage.success('状态更新成功')
    await fetchPolicyList()
  }
  catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 选择变更
 */
function handleSelectionChange(ids: string[]) {
  selectedIds.value = ids
}

/**
 * 分页大小变更
 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchPolicyList()
}

/**
 * 当前页变更
 */
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchPolicyList()
}

/**
 * 配置对话框提交
 */
function handleConfigSubmit(data: any) {
  console.log('配置提交:', data)
}

/**
 * 配置对话框成功
 */
function handleConfigSuccess() {
  showConfigDialog.value = false
  fetchPolicyList()
}

/**
 * 配置对话框取消
 */
function handleConfigCancel() {
  showConfigDialog.value = false
}
</script>

<template>
  <div>
    <FaPageHeader title="卡政策管理" description="管理会员卡的政策设置" />

    <FaPageMain>
      <!-- 搜索表单 -->
      <PolicySearchForm
        v-model="searchParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @export="handleExport"
      />

      <!-- 操作按钮 -->
      <div class="flex gap-3 my-5 p-4 bg-white rounded-lg border border-gray-200">
        <ElButton type="primary" :icon="Plus" @click="handleCreate">
          新增政策
        </ElButton>
        <ElButton
          type="danger"
          :icon="Delete"
          :disabled="!selectedIds.length"
          @click="handleBatchDelete"
        >
          批量删除
        </ElButton>
      </div>

      <!-- 卡政策列表 -->
      <PolicyTable
        :data="policyList"
        :loading="loading"
        :selected-ids="selectedIds"
        @selection-change="handleSelectionChange"
        @edit="handleEdit"
        @delete="handleDelete"
        @view="handleView"
        @status-change="handleStatusChange"
      />

      <!-- 分页 -->
      <div class="flex justify-center mt-5 p-4 bg-white rounded-lg border border-gray-200">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 卡政策配置对话框 -->
      <PolicyConfigDialog
        v-model="showConfigDialog"
        :policy-id="editPolicyId"
        :page-mode="dialogMode"
        @submit="handleConfigSubmit"
        @success="handleConfigSuccess"
        @cancel="handleConfigCancel"
      />

      <!-- 卡政策详情对话框 -->
      <PolicyDetailDialog
        v-model="showDetailDialog"
        :policy-id="viewPolicyId"
      />

      <!-- 卡政策表单配置对话框 -->
      <!-- 这个对话框现在被封装在独立的组件中 -->
    </FaPageMain>
  </div>
</template>
