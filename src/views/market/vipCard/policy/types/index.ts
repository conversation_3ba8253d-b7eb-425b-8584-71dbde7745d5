/**
 * 卡政策管理类型定义
 */

// 枚举定义
export enum VipCardPolicyStatus {
  DISABLED = 0, // 下架
  ENABLED = 1, // 上架
  DRAFT = 2, // 草稿
}

export enum CardType {
  STORE_VALUE = 0, // 储值卡
}

export enum DisplayDiscountPrice {
  NOT_DISPLAY = 0, // 不显示
  DISPLAY = 1, // 显示
}

// 基础类型定义
export interface VipCardPolicyListParams {
  page: number
  size: number
  name?: string
  status: VipCardPolicyStatus | null // 修改为允许null值
  dateRange?: string[]
}

export interface VipCardPolicyMoney {
  id?: string
  internalVipCardId?: number
  amount: number
}

export interface VipCardPolicyRuleSelect {
  isSelectAll: boolean
  types: any[]
}

export interface VipCardPolicyRuleHallType {
  id: number
  internalVipCardId: number
  internalVipCardDiscountRuleId: number
  hallTypeDiscount: number
  filmTypeDiscount: number
  hallTypeSelect: VipCardPolicyRuleSelect
  filmTypeSelect: VipCardPolicyRuleSelect
}

export interface VipCardPolicyRuleCinema {
  id: number
  internalVipCardId: number
  internalVipCardDiscountRuleId: number
  cinemaId: number
  includeCinema: number
  cinemaName: string
}

export interface VipCardPolicyRuleTicket {
  id: number
  internalVipCardId: number
  internalVipCardDiscountRuleId: number
  discountType: number
  discountValue: number
  discountTimeInterval: {
    days: number[]
    timeIntervals: Array<{
      startTime: string
      endTime: string
    }>
  }
}

export interface VipCardPolicyRuleHallFilm {
  id: number
  internalVipCardId: number
  internalVipCardDiscountRuleId: number
  hallType: string
  filmType: string
  discountType: number
  discountValue: number
  groupIndex: number
  discountTimeInterval: Array<{
    days: number[]
    timeIntervals: Array<{
      startTime: string
      endTime: string
    }>
  }>
}

export interface VipCardPolicyRuleFilm {
  id: number
  internalVipCardId: number
  internalVipCardDiscountRuleId: number
  filmId: number
  includeFilm: number
  filmName: string
}

export interface VipCardPolicyRuleGoodsType {
  id: number
  internalVipCardId: number
  internalVipCardDiscountRuleId: number
  goodsTypeId: number
  discountType: number
  discountValue: number
  goodsTypeName: string
}

export interface VipCardPolicyRule {
  id: number
  internalVipCardId: number
  canUseCinemaType: number
  ticketDiscountType: number
  generalSetting: number
  excludeFilm: number
  includeFilm: number
  generalEachSchedule: number
  generalEachScheduleNum: number
  generalEachFilm: number
  generalEachFilmNum: number
  generalEachDay: number
  generalEachDayNum: number
  includeFilmEachSchedule: number
  includeFilmEachScheduleNum: number
  includeFilmEachFilm: number
  includeFilmEachFilmNum: number
  includeFilmEachDay: number
  includeFilmEachDayNum: number
  servicePriceReduction: number
  servicePriceAmount: number
  goodsDiscountSetting: number
  goodsDiscountType: number
  hallType: VipCardPolicyRuleHallType
  cinemas: VipCardPolicyRuleCinema[]
  tickets: VipCardPolicyRuleTicket[]
  hallFilms: VipCardPolicyRuleHallFilm[]
  films: VipCardPolicyRuleFilm[]
  goodsTypes: VipCardPolicyRuleGoodsType[]
}

export interface VipCardPolicyFormData {
  id?: string
  name: string
  description: string
  consumptionType: number // 消费类型：电影演出、电商
  cardType: CardType
  status: VipCardPolicyStatus
  sort: number
  displaySort: number
  effectiveDays: number
  vipFee: number
  displayDiscountPrice: DisplayDiscountPrice
  cardCoverText: string
  discountText1: string
  discountText2: string
  discountText3: string
  monies: VipCardPolicyMoney[]
}

export interface VipCardPolicy {
  id: string
  name: string
  description: string
  consumptionType: number // 消费类型：电影演出、电商
  cardType: CardType
  status: VipCardPolicyStatus
  sort: number
  displayDiscountPrice: DisplayDiscountPrice
  displaySort: number
  effectiveDays: number
  vipFee: number
  cardCoverText: string
  discountText1: string
  discountText2: string
  discountText3: string
  cinemas: number[]
  monies: VipCardPolicyMoney[]
  createBy: string
  updateTime: number
  rules: VipCardPolicyRule[]
}

export interface CreateVipCardPolicyParams extends VipCardPolicyFormData {}

export interface UpdateVipCardPolicyParams extends VipCardPolicyFormData {
  id: string
}

// 卡政策列表响应
export interface VipCardPolicyListResponse {
  total: number
  content: VipCardPolicy[]
  result: VipCardPolicy[]
}

// 卡政策状态变更参数
export interface VipCardPolicyStatusParams {
  cardId: string
  status: number
}

// 获取卡政策详情参数
export interface VipCardPolicyDetailParams {
  cardId: string
}

// 卡政策搜索表单数据
export interface VipCardPolicySearchForm {
  name: string
  id: string
  status?: number
  dateRange?: [string, string]
  startTime?: number
  endTime?: number
}
