<route lang="yaml">
meta:
  title: 卡赠礼管理
  icon: i-ep:present
  auth: /market/vipCardActivity/query
</route>

<script setup lang="ts">
// 类型导入
import type { VipCardGift, VipCardGiftListParams } from './types'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref } from 'vue'
// API 导入（后续需要实现）
// import {
//   getVipCardGiftList,
//   deleteVipCardGift,
//   updateVipCardGiftStatus,
// } from '@/api/modules/market/vipCard'

// 组件导入
import GiftSearchForm from './components/GiftSearchForm.vue'
import GiftTable from './components/GiftTable.vue'
import GiftDetailDialog from './components/GiftDetailDialog.vue'
// import GiftConfigDialog from './components/GiftConfigDialog.vue'

import { PAGINATION_CONFIG } from './constants'

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)

// 卡赠礼列表
const giftList = ref<VipCardGift[]>([])

// 搜索参数
const searchParams = ref<VipCardGiftListParams>({
  page: 1,
  size: 20,
  name: '',
  status: undefined,
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  ...PAGINATION_CONFIG,
})

// 选中的卡赠礼ID
const selectedIds = ref<string[]>([])

// 对话框状态
const showDetailDialog = ref(false)
// const showConfigDialog = ref(false)
// const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const viewGiftId = ref('')

// ==================== 生命周期 ====================

onMounted(() => {
  fetchGiftList()
})

// ==================== 方法定义 ====================

/**
 * 获取卡赠礼列表
 */
async function fetchGiftList() {
  try {
    loading.value = true

    // 生产环境使用真实API
    // const response = await getVipCardGiftList({
    //   ...searchParams.value,
    //   page: pagination.page,
    //   size: pagination.size,
    // })

    // 模拟数据
    const response = {
      code: 0,
      total: 2,
      content: [
        {
          id: 'gift001',
          internalVipCardId: 'card001',
          internalVipCardName: '金卡',
          name: '开卡赠礼活动',
          type: 0,
          startTime: Date.now() - 86400000,
          endTime: Date.now() + 86400000 * 7,
          status: 1,
          createBy: 'admin',
          updateTime: '2023-01-01 12:00:00',
          gifts: [
            {
              giftType: 0,
              coupons: [
                {
                  couponId: 1,
                  couponName: '电影票优惠券',
                  couponNum: 2
                }
              ]
            },
            {
              giftType: 1,
              amount: 10000
            }
          ]
        },
        {
          id: 'gift002',
          internalVipCardId: 'card002',
          internalVipCardName: '银卡',
          name: '充值赠礼活动',
          type: 1,
          startTime: Date.now() - 86400000 * 2,
          endTime: Date.now() + 86400000 * 5,
          status: 0,
          createBy: 'admin',
          updateTime: '2023-01-02 12:00:00',
          gifts: [
            {
              giftType: 0,
              coupons: [
                {
                  couponId: 2,
                  couponName: '爆米花优惠券',
                  couponNum: 1
                }
              ]
            }
          ]
        }
      ]
    }

    console.log('获取卡赠礼列表响应:', response)
    const { code, content, total } = response
    if (code === 0) {
      giftList.value = content || []
      pagination.total = total || 0
    }
  }
  catch (error) {
    console.error('获取卡赠礼列表失败:', error)
    ElMessage.error('获取卡赠礼列表失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
function handleSearch(params: VipCardGiftListParams) {
  searchParams.value = { ...params }
  pagination.page = 1
  fetchGiftList()
}

/**
 * 重置搜索
 */
function handleReset() {
  searchParams.value = {
    page: 1,
    size: 20,
    name: '',
    status: undefined,
  }
  pagination.page = 1
  fetchGiftList()
}

/**
 * 导出
 */
async function handleExport() {
  try {
    loading.value = true
    // await exportVipCardGiftList(searchParams.value)
    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 新增卡赠礼
 */
// function handleCreate() {
//   dialogMode.value = 'add'
//   showConfigDialog.value = true
// }

/**
 * 编辑卡赠礼
 */
// function handleEdit(gift: VipCardGift) {
//   dialogMode.value = 'edit'
//   showConfigDialog.value = true
// }

/**
 * 查看卡赠礼详情
 */
function handleView(gift: VipCardGift) {
  viewGiftId.value = gift.id
  showDetailDialog.value = true
}

/**
 * 删除卡赠礼
 */
async function handleDelete(gift: VipCardGift) {
  try {
    await ElMessageBox.confirm(
      `确定要删除活动"${gift.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    // await deleteVipCardGift(gift.id)
    ElMessage.success('删除成功')
    await fetchGiftList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除卡赠礼失败:', error)
      ElMessage.error('删除卡赠礼失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 批量删除
 */
async function handleBatchDelete() {
  if (!selectedIds.value.length) {
    ElMessage.warning('请选择要删除的活动')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个活动吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    // await batchDeleteVipCardGifts(selectedIds.value)
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    fetchGiftList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 状态变更
 */
async function handleStatusChange(gift: VipCardGift, status: number) {
  try {
    loading.value = true
    // await updateVipCardGiftStatus({
    //   cardId: gift.id,
    //   status,
    // })
    ElMessage.success('状态更新成功')
    await fetchGiftList()
  }
  catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 选择变更
 */
function handleSelectionChange(ids: string[]) {
  selectedIds.value = ids
}

/**
 * 分页大小变更
 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchGiftList()
}

/**
 * 当前页变更
 */
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchGiftList()
}

/**
 * 配置对话框提交
 */
// function handleConfigSubmit(data: any) {
//   console.log('配置提交:', data)
// }

/**
 * 配置对话框成功
 */
// function handleConfigSuccess() {
//   showConfigDialog.value = false
//   fetchGiftList()
// }

/**
 * 配置对话框取消
 */
// function handleConfigCancel() {
//   showConfigDialog.value = false
// }
</script>

<template>
  <div>
    <FaPageHeader title="卡赠礼管理" description="管理会员卡的赠礼活动" />

    <FaPageMain>
      <!-- 搜索表单 -->
      <GiftSearchForm
        v-model="searchParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @export="handleExport"
      />

      <!-- 操作按钮 -->
      <div class="actions-bar">
        <!-- <ElButton type="primary" :icon="Plus" @click="handleCreate">
          新增活动
        </ElButton> -->
        <ElButton
          type="danger"
          :icon="Delete"
          :disabled="!selectedIds.length"
          @click="handleBatchDelete"
        >
          批量删除
        </ElButton>
      </div>

      <!-- 卡赠礼列表 -->
      <GiftTable
        :data="giftList"
        :loading="loading"
        :selected-ids="selectedIds"
        @selection-change="handleSelectionChange"
        @edit="handleEdit"
        @delete="handleDelete"
        @view="handleView"
        @status-change="handleStatusChange"
      />

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          :layout="pagination.layout"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 卡赠礼配置对话框 -->
      <!-- <GiftConfigDialog
        v-model="showConfigDialog"
        :gift-id="editGiftId"
        :page-mode="dialogMode"
        @submit="handleConfigSubmit"
        @success="handleConfigSuccess"
        @cancel="handleConfigCancel"
      /> -->

      <!-- 卡赠礼详情对话框 -->
      <GiftDetailDialog
        v-model="showDetailDialog"
        :gift-id="viewGiftId"
      />
    </FaPageMain>
  </div>
</template>

<style scoped lang="scss">
.actions-bar {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  padding: 16px;
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .actions-bar {
    flex-direction: column;
    gap: 8px;
  }

  .actions-bar .el-button {
    width: 100%;
  }
}
</style>