/**
 * 卡赠礼管理相关类型定义
 */

// 优惠券信息
export interface Coupon {
  couponId?: number
  couponName?: string
  couponNum?: number
}

// 赠礼信息
export interface Gift {
  internalVipCardId?: string
  internalVipCardActivityId?: string
  internalVipCardActivityMoneyId?: string
  type?: number // 活动类型 开卡赠礼0 充值赠礼1
  giftType?: number // 赠礼类型 0:优惠券 1:金额 2:其他
  amount?: number // 金额 单位：分
  coupons?: Coupon[]
}

// 卡赠礼基础信息
export interface VipCardGift {
  id: string
  internalVipCardId: string
  internalVipCardName: string
  name: string
  type: number // 活动类型 开卡赠礼0 充值赠礼1
  startTime: number
  endTime: number
  status: number // 使用状态 0-关闭 1-开启
  createBy: string
  updateTime: string // yyyy-MM-dd HH:mm:ss
  gifts: Gift[]
}

// 卡赠礼列表查询参数
export interface VipCardGiftListParams {
  page?: number
  size?: number
  name?: string
  internalVipCardId?: string
  status?: number
  startTime?: number
  endTime?: number
}

// 卡赠礼列表响应
export interface VipCardGiftListResponse {
  total: number
  content: VipCardGift[]
  result: VipCardGift[]
}

// 创建卡赠礼参数
export interface CreateVipCardGiftParams {
  id?: string
  internalVipCardId: string
  internalVipCardName: string
  name: string
  type: number
  startTime: number
  endTime: number
  status: number
  createBy?: string
  updateTime?: string
  gifts: Gift[]
}

// 编辑卡赠礼参数
export interface UpdateVipCardGiftParams extends CreateVipCardGiftParams {
  id: string
}

// 卡赠礼状态变更参数
export interface VipCardGiftStatusParams {
  cardId: string
  status: number
}

// 获取卡赠礼详情参数
export interface VipCardGiftDetailParams {
  cardId: string
}

// 卡赠礼状态枚举
export enum VipCardGiftStatus {
  DISABLED = 0, // 关闭
  ENABLED = 1, // 开启
}

// 活动类型枚举
export enum ActivityType {
  OPEN_CARD = 0, // 开卡赠礼
  RECHARGE = 1, // 充值赠礼
}

// 赠礼类型枚举
export enum GiftType {
  COUPON = 0, // 优惠券
  AMOUNT = 1, // 金额
  OTHER = 2, // 其他
}

// 卡赠礼表单数据
export interface VipCardGiftFormData {
  id?: string
  internalVipCardId: string
  internalVipCardName: string
  name: string
  type: number
  startTime: string // 用于表单显示的时间格式
  endTime: string // 用于表单显示的时间格式
  status: number
  gifts: Gift[]
}

// 卡赠礼搜索表单数据
export interface VipCardGiftSearchForm {
  name: string
  internalVipCardId: string
  status?: number
  dateRange?: [string, string]
}