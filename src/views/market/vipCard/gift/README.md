# 卡赠礼管理模块

## 📋 模块概述

卡赠礼管理模块是会员系统的重要组成部分，提供完整的卡赠礼活动创建、编辑、查看和管理功能。基于API文档 `src/views/market/vipCard/会员 api.md` 实现，确保与后端接口完全兼容。

## 🏗️ 项目结构

```
src/views/market/vipCard/gift/
├── 📁 components/                    # 组件目录
│   ├── GiftSearchForm.vue           # 搜索表单组件
│   ├── GiftTable.vue                # 卡赠礼列表表格组件
│   ├── GiftConfigDialog.vue         # 卡赠礼配置对话框组件
│   └── GiftDetailDialog.vue         # 卡赠礼详情对话框组件
├── 📁 types/                         # 类型定义
│   └── index.ts                     # 卡赠礼相关类型定义
├── 📁 constants/                     # 常量定义
│   └── index.ts                     # 卡赠礼相关常量和工具函数
├── index.vue                         # 卡赠礼管理主页面
└── README.md                         # 本文档

src/api/modules/market/vipCard/
├── index.ts                          # 卡赠礼API接口
└── 会员 api.md                       # API文档

src/router/modules/
└── vipCard.ts                        # 卡赠礼路由配置
```

## 🎯 核心功能

### 1. **卡赠礼列表管理**
- ✅ 卡赠礼列表展示（表格形式）
- ✅ 多条件搜索和筛选
- ✅ 分页显示
- ✅ 批量操作（删除）
- ✅ 状态管理（启用/停用）

### 2. **卡赠礼配置**
- ✅ 新增卡赠礼活动
- ✅ 编辑卡赠礼活动
- ✅ 卡赠礼详情查看
- ✅ 活动时间设置
- ✅ 赠礼方案配置（优惠券、金额等）

### 3. **数据管理**
- ✅ 完整的类型定义
- ✅ 表单验证
- ✅ 错误处理
- ✅ 加载状态管理

## 🔧 技术特性

### **前端技术栈**
- **Vue 3** - 组合式API
- **TypeScript** - 类型安全
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理（可选）

### **代码特性**
- 📝 **完整的TypeScript支持**
- 🎨 **响应式设计**
- 🔄 **组件化架构**
- 📱 **移动端适配**
- 🛡️ **错误边界处理**
- 🎯 **性能优化**

## 📊 数据结构

### **卡赠礼基础信息**
```typescript
interface VipCardGift {
  id: string                    // 活动ID
  internalVipCardId: string     // 内部会员卡id
  internalVipCardName: string   // 内部会员卡名称
  name: string                  // 活动名称
  type: number                  // 活动类型 开卡赠礼0 充值赠礼1
  startTime: number             // 活动开始日期
  endTime: number               // 活动结束日期
  status: number                // 使用状态 0-关闭 1-开启
  createBy: string              // 创建者
  updateTime: string            // 更新时间
  gifts: Gift[]                 // 活动方案
}
```

### **赠礼信息**
```typescript
interface Gift {
  internalVipCardId?: string
  internalVipCardActivityId?: string
  internalVipCardActivityMoneyId?: string
  type?: number                 // 活动类型 开卡赠礼0 充值赠礼1
  giftType?: number             // 赠礼类型 0:优惠券 1:金额 2:其他
  amount?: number               // 金额 单位：分
  coupons?: Coupon[]            // 优惠券
}
```

### **搜索参数**
```typescript
interface VipCardGiftListParams {
  page?: number                 // 页码
  size?: number                 // 每页数量
  name?: string                 // 活动名称
  internalVipCardId?: string    // 影城卡ID
  status?: number               // 活动状态
  startTime?: number            // 创建时间-开始时间
  endTime?: number              // 创建时间-结束时间
}
```

## 🎨 组件说明

### **1. GiftSearchForm.vue**
**功能**: 卡赠礼搜索和筛选表单
**特性**:
- 基础搜索（名称、状态、时间）
- 高级搜索（影城卡ID）
- 实时搜索
- 导出功能

### **2. GiftTable.vue**
**功能**: 卡赠礼列表表格展示
**特性**:
- 表格展示卡赠礼信息
- 状态标签显示
- 操作按钮（查看、编辑、删除、状态切换）
- 批量选择

### **3. GiftConfigDialog.vue**
**功能**: 卡赠礼配置对话框
**特性**:
- 支持新增、编辑、查看三种模式
- 表单验证
- 活动方案配置
- 响应式布局

### **4. GiftDetailDialog.vue**
**功能**: 卡赠礼详情展示对话框
**特性**:
- 完整的卡赠礼信息展示
- 活动方案详情展示
- 时间状态显示

## 🚀 使用指南

### **1. 页面访问**
```
卡赠礼列表: /market/vipCard/gift/list
新增卡赠礼: /market/vipCard/gift/create
编辑卡赠礼: /market/vipCard/gift/edit/:id
卡赠礼详情: /market/vipCard/gift/detail/:id
```

### **2. 组件使用**
```vue
<!-- 在其他页面中使用卡赠礼配置对话框 -->
<GiftConfigDialog
  v-model="showDialog"
  :gift-id="giftId"
  :page-mode="pageMode"
  @submit="handleSubmit"
  @success="handleSuccess"
/>
```

### **3. API调用**

```typescript
import {getVipCardGiftList, createVipCardGift} from 'src/api/modules/market/vipCard'

// 获取卡赠礼列表
const response = await getVipCardGiftList({
    page: 1,
    size: 20,
    name: '活动名称'
})

// 创建卡赠礼
await createVipCardGift({
    name: '新活动',
    startTime: Date.now(),
    endTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
    // ... 其他参数
})
```

## 🔄 开发模式

### **模拟数据**
开发阶段使用模拟数据，生产环境切换到真实API：

```typescript
// 开发环境
const response = await mockGetVipCardGiftList(params)

// 生产环境
// const response = await getVipCardGiftList(params)
```

### **切换方式**
1. 注释掉模拟API调用
2. 取消注释真实API调用
3. 确保后端接口可用

## 📋 待完善功能

### **短期计划**
- [ ] 卡赠礼新增/编辑功能
- [ ] 卡赠礼导出功能
- [ ] 卡赠礼批量操作

### **长期计划**
- [ ] 卡赠礼统计图表
- [ ] 卡赠礼模板功能
- [ ] 卡赠礼复制功能

## 🧪 测试建议

### **功能测试**
1. **基础功能**：创建、编辑、删除、查看卡赠礼
2. **搜索功能**：各种搜索条件组合测试
3. **表单验证**：必填字段、格式验证
4. **状态管理**：启用/停用状态切换

### **兼容性测试**
1. **浏览器兼容**：Chrome、Firefox、Safari、Edge
2. **设备兼容**：桌面端、平板、手机
3. **分辨率适配**：各种屏幕尺寸

### **性能测试**
1. **大数据量**：测试大量卡赠礼数据的加载性能
2. **内存使用**：长时间使用的内存泄漏检测

## 🔗 相关文档

- [API文档](../../../api/modules/market/vipCard/会员 api.md)
- [卡政策模块](../policy/README.md)
- [会员卡模块](../card/README.md)
- [路由配置](../../../router/modules/market/vipCard.ts)
- [类型定义](./types/index.ts)

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看控制台错误**：检查浏览器控制台的错误信息
2. **检查网络请求**：确认API请求是否正常
3. **验证数据格式**：确保传递的数据格式正确
4. **查看文档**：参考API文档和类型定义

---

**开发状态**: ✅ 已完成基础功能  
**维护状态**: 🔄 持续维护  
**版本**: v1.0.0