/**
 * 卡赠礼管理常量定义
 */

import { VipCardGiftStatus, ActivityType, GiftType } from '../types'

// 卡赠礼状态选项
export const VIP_CARD_GIFT_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '已关闭', value: VipCardGiftStatus.DISABLED },
  { label: '已开启', value: VipCardGiftStatus.ENABLED },
]

// 卡赠礼状态标签映射
export const VIP_CARD_GIFT_STATUS_MAP = {
  [VipCardGiftStatus.DISABLED]: { label: '已关闭', type: 'danger' },
  [VipCardGiftStatus.ENABLED]: { label: '已开启', type: 'success' },
}

// 活动类型选项
export const ACTIVITY_TYPE_OPTIONS = [
  { label: '开卡赠礼', value: ActivityType.OPEN_CARD },
  { label: '充值赠礼', value: ActivityType.RECHARGE },
]

// 活动类型标签映射
export const ACTIVITY_TYPE_MAP = {
  [ActivityType.OPEN_CARD]: { label: '开卡赠礼', type: 'primary' },
  [ActivityType.RECHARGE]: { label: '充值赠礼', type: 'success' },
}

// 赠礼类型选项
export const GIFT_TYPE_OPTIONS = [
  { label: '优惠券', value: GiftType.COUPON },
  { label: '金额', value: GiftType.AMOUNT },
  { label: '其他', value: GiftType.OTHER },
]

// 赠礼类型标签映射
export const GIFT_TYPE_MAP = {
  [GiftType.COUPON]: { label: '优惠券', type: 'success' },
  [GiftType.AMOUNT]: { label: '金额', type: 'warning' },
  [GiftType.OTHER]: { label: '其他', type: 'info' },
}

// 分页配置
export const PAGINATION_CONFIG = {
  page: 1,
  size: 20,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
}

// 表单验证规则
export const VIP_CARD_GIFT_FORM_RULES = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 2, max: 50, message: '活动名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  internalVipCardId: [
    { required: true, message: '请选择影城卡', trigger: 'change' },
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
  ],
}

// 快速时间选择选项
export const QUICK_DATE_OPTIONS = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '明天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() + 1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setDate(end.getDate() + 1)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本周',
    value: () => {
      const start = new Date()
      const day = start.getDay()
      const diff = start.getDate() - day + (day === 0 ? -6 : 1)
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const start = new Date()
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setMonth(end.getMonth() + 1, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
]

// 默认表单数据
export const DEFAULT_VIP_CARD_GIFT_FORM: Partial<VipCardGiftFormData> = {
  name: '',
  internalVipCardId: '',
  internalVipCardName: '',
  type: ActivityType.OPEN_CARD,
  startTime: '',
  endTime: '',
  status: VipCardGiftStatus.ENABLED,
  gifts: [],
}

// 工具函数
export const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

export const getVipCardGiftStatusText = (status: number): string => {
  return VIP_CARD_GIFT_STATUS_MAP[status]?.label || '未知'
}

export const getVipCardGiftStatusType = (status: number): string => {
  return VIP_CARD_GIFT_STATUS_MAP[status]?.type || 'info'
}

export const getActivityTypeText = (type: number): string => {
  return ACTIVITY_TYPE_MAP[type]?.label || '未知'
}

export const getActivityTypeType = (type: number): string => {
  return ACTIVITY_TYPE_MAP[type]?.type || 'info'
}

export const getGiftTypeText = (giftType: number): string => {
  return GIFT_TYPE_MAP[giftType]?.label || '未知'
}

export const getGiftTypeType = (giftType: number): string => {
  return GIFT_TYPE_MAP[giftType]?.type || 'info'
}