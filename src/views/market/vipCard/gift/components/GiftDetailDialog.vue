<template>
  <ElDialog
    v-model="dialogVisible"
    title="卡赠礼详情"
    :width="800"
    destroy-on-close
  >
    <div v-loading="loading" class="gift-detail">
      <div v-if="giftData" class="detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            基础信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>活动名称：</label>
              <span>{{ giftData.name }}</span>
            </div>
            <div class="info-item">
              <label>影城卡名称：</label>
              <span>{{ giftData.internalVipCardName }}</span>
            </div>
            <div class="info-item">
              <label>活动类型：</label>
              <ElTag :type="getActivityTypeType(giftData.type)">
                {{ getActivityTypeText(giftData.type) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>活动状态：</label>
              <ElTag :type="getVipCardGiftStatusType(giftData.status)">
                {{ getVipCardGiftStatusText(giftData.status) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>开始时间：</label>
              <span>{{ formatTimestamp(giftData.startTime) }}</span>
            </div>
            <div class="info-item">
              <label>结束时间：</label>
              <span>{{ formatTimestamp(giftData.endTime) }}</span>
            </div>
            <div class="info-item">
              <label>创建者：</label>
              <span>{{ giftData.createBy }}</span>
            </div>
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ giftData.updateTime }}</span>
            </div>
          </div>
        </div>

        <!-- 活动方案 -->
        <div class="detail-section">
          <h3 class="section-title">
            活动方案
          </h3>
          <ElTable :data="giftData.gifts" border>
            <ElTableColumn prop="giftType" label="赠礼类型" width="120">
              <template #default="{ row }">
                <ElTag :type="getGiftTypeType(row.giftType!)">
                  {{ getGiftTypeText(row.giftType!) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="amount" label="金额(分)" width="120">
              <template #default="{ row }">
                <span v-if="row.giftType === 1">{{ row.amount }}</span>
                <span v-else>-</span>
              </template>
            </ElTableColumn>
            <ElTableColumn label="优惠券信息">
              <template #default="{ row }">
                <div v-if="row.giftType === 0 && row.coupons && row.coupons.length > 0">
                  <div v-for="coupon in row.coupons" :key="coupon.couponId" class="coupon-item">
                    <span>{{ coupon.couponName }} × {{ coupon.couponNum }}</span>
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>
      </div>

      <div v-else class="empty-state">
        <ElEmpty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">
          关闭
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
// 类型导入
import type { VipCardGift } from '../types'
import { ElMessage } from 'element-plus'

import { computed, ref, watch } from 'vue'
// API 导入（后续需要实现）
// import { getVipCardGiftDetail } from '@/api/modules/market/vipCard'

// 常量导入
import {
  formatTimestamp,
  getVipCardGiftStatusText,
  getVipCardGiftStatusType,
  getActivityTypeText,
  getActivityTypeType,
  getGiftTypeText,
  getGiftTypeType,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  giftId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 加载状态
const loading = ref(false)

// 卡赠礼数据
const giftData = ref<VipCardGift | null>(null)

// ==================== 监听器 ====================

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.giftId) {
    fetchGiftDetail()
  }
})

// ==================== 方法定义 ====================

/**
 * 获取卡赠礼详情
 */
async function fetchGiftDetail() {
  if (!props.giftId) { return }

  try {
    loading.value = true

    // 使用模拟数据（开发阶段）
    // const response = await mockGetVipCardGiftDetail(props.giftId)

    // 生产环境使用真实API
    // const response = await getVipCardGiftDetail({ cardId: props.giftId })

    // 模拟数据
    giftData.value = {
      id: props.giftId,
      internalVipCardId: 'card001',
      internalVipCardName: '金卡',
      name: '开卡赠礼活动',
      type: 0,
      startTime: Date.now() - 86400000,
      endTime: Date.now() + 86400000 * 7,
      status: 1,
      createBy: 'admin',
      updateTime: '2023-01-01 12:00:00',
      gifts: [
        {
          giftType: 0,
          coupons: [
            {
              couponId: 1,
              couponName: '电影票优惠券',
              couponNum: 2
            }
          ]
        },
        {
          giftType: 1,
          amount: 10000
        }
      ]
    }
  }
  catch (error) {
    console.error('获取卡赠礼详情失败:', error)
    ElMessage.error('获取卡赠礼详情失败')
    giftData.value = null
  }
  finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.gift-detail {
  .detail-content {
    .detail-section {
      margin-bottom: 24px;
      padding: 20px;
      background: var(--el-fill-color-blank);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        label {
          min-width: 100px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        span {
          color: var(--el-text-color-primary);
        }
      }
    }

    .coupon-item {
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.dialog-footer {
  text-align: center;
}
</style>