<template>
  <div class="gift-table">
    <ElTable
      ref="tableRef"
      :data="data"
      :loading="loading"
      row-key="id"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <ElTableColumn type="selection" width="55" align="center" />

      <!-- 活动名称 -->
      <ElTableColumn prop="name" label="活动名称" min-width="200" show-overflow-tooltip />

      <!-- 影城卡名称 -->
      <ElTableColumn prop="internalVipCardName" label="影城卡名称" min-width="150" show-overflow-tooltip />

      <!-- 活动类型 -->
      <ElTableColumn prop="type" label="活动类型" width="120" align="center">
        <template #default="{ row }">
          <ElTag :type="getActivityTypeType(row.type)">
            {{ getActivityTypeText(row.type) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 活动状态 -->
      <ElTableColumn prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <ElTag :type="getVipCardGiftStatusType(row.status)">
            {{ getVipCardGiftStatusText(row.status) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 开始时间 -->
      <ElTableColumn prop="startTime" label="开始时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.startTime) }}
        </template>
      </ElTableColumn>

      <!-- 结束时间 -->
      <ElTableColumn prop="endTime" label="结束时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.endTime) }}
        </template>
      </ElTableColumn>

      <!-- 创建时间 -->
      <ElTableColumn prop="updateTime" label="更新时间" width="180" align="center" />

      <!-- 操作列 -->
      <ElTableColumn label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <ElButton type="primary" size="small" text @click="handleView(row)">
              查看
            </ElButton>
            <ElButton type="warning" size="small" text @click="handleEdit(row)">
              编辑
            </ElButton>
            <ElButton
              :type="row.status === 1 ? 'danger' : 'success'"
              size="small"
              text
              @click="handleStatusChange(row)"
            >
              {{ row.status === 1 ? '停用' : '启用' }}
            </ElButton>
            <ElButton type="danger" size="small" text @click="handleDelete(row)">
              删除
            </ElButton>
          </div>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ElTable } from 'element-plus'

// 类型导入
import type { VipCardGift } from '../types'

// 常量导入
import {
  formatTimestamp,
  getVipCardGiftStatusText,
  getVipCardGiftStatusType,
  getActivityTypeText,
  getActivityTypeType,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  data: VipCardGift[]
  loading?: boolean
  selectedIds?: string[]
}

interface Emits {
  (e: 'selection-change', ids: string[]): void
  (e: 'edit', gift: VipCardGift): void
  (e: 'delete', gift: VipCardGift): void
  (e: 'view', gift: VipCardGift): void
  (e: 'status-change', gift: VipCardGift, status: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedIds: () => [],
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>()

// ==================== 方法定义 ====================

/**
 * 选择变更
 */
function handleSelectionChange(selection: VipCardGift[]) {
  const ids = selection.map(item => item.id)
  emit('selection-change', ids)
}

/**
 * 查看
 */
function handleView(gift: VipCardGift) {
  emit('view', gift)
}

/**
 * 编辑
 */
function handleEdit(gift: VipCardGift) {
  emit('edit', gift)
}

/**
 * 删除
 */
function handleDelete(gift: VipCardGift) {
  emit('delete', gift)
}

/**
 * 状态变更
 */
function handleStatusChange(gift: VipCardGift) {
  const newStatus = gift.status === 1 ? 0 : 1
  emit('status-change', gift, newStatus)
}

// ==================== 暴露方法 ====================

defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: VipCardGift, selected?: boolean) => 
    tableRef.value?.toggleRowSelection(row, selected),
})
</script>

<style scoped lang="scss">
.gift-table {
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 表格样式优化 */
:deep(.el-table) {
  .el-table__header {
    background: var(--el-fill-color-light);
  }

  .el-table__row {
    &:hover {
      background: var(--el-fill-color-lighter);
    }
  }

  .el-table__cell {
    padding: 12px 8px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>