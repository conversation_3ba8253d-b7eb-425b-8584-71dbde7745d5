/**
 * 会员卡管理相关类型定义
 */

// 会员卡基础信息
export interface VipCard {
  id: string
  internalVipCardId: string
  batchId: string
  cardNumber: string
  name: string
  cardType: string
  cardTypeName: string
  cinemaId: number
  cinemaName: string
  userId: number
  nickName: string
  phoneNumber: string
  cardAmount: number
  vipFee: number
  startTime: number
  endTime: number
  effective: boolean
  displayDiscountPrice: number
  canUserCurrentCinema: boolean
  sort: number
  status: number
  cardStatus: number
  acquisitionMethod: number
  cardCreationDate: number
}

// 会员卡列表查询参数
export interface VipCardListParams {
  page?: number
  size?: number
  cardNumber?: string
  internalVipCardId?: string
  cardType?: string
  cinemaName?: string
  phoneNumber?: string
  status?: number
  startTime?: number
  endTime?: number
}

// 会员卡列表响应
export interface VipCardListResponse {
  total: number
  content: VipCard[]
  result: VipCard[]
}

// 创建会员卡参数
export interface CreateVipCardParams {
  id?: string
  internalVipCardId: string
  batchId: string
  cardNumber: string
  name: string
  cardType: string
  cardTypeName: string
  cinemaId: number
  cinemaName: string
  userId: number
  nickName: string
  phoneNumber: string
  cardAmount: number
  vipFee: number
  startTime: number
  endTime: number
  effective: boolean
  displayDiscountPrice: number
  canUserCurrentCinema: boolean
  sort: number
  status: number
  cardStatus: number
  acquisitionMethod: number
  cardCreationDate: number
}

// 编辑会员卡参数
export interface UpdateVipCardParams extends CreateVipCardParams {
  id: string
}

// 会员卡状态变更参数
export interface VipCardStatusParams {
  cardId: string
  status: number
}

// 获取会员卡详情参数
export interface VipCardDetailParams {
  cardId: string
}

// 卡政策状态枚举
export enum VipCardPolicyStatus {
  DISABLED = 0, // 下架
  ENABLED = 1,  // 上架
  DRAFT = 2,    // 草稿
}

// 卡状态枚举
export enum CardStatus {
  INIT = -1,     // 初始化
  INACTIVE = 0,  // 未激活
  NORMAL = 1,    // 正常
  FROZEN = 2,    // 冻结
  EXPIRED = 3,   // 过期
  INVALID = 4,   // 作废
}

// 获得方式枚举
export enum AcquisitionMethod {
  OLD_CARD = 0,      // 云智老卡
  PERSONAL_BUY = 1,  // 个人购买电子卡
}

// 会员卡表单数据
export interface VipCardFormData {
  id?: string
  internalVipCardId: string
  batchId: string
  cardNumber: string
  name: string
  cardType: string
  cardTypeName: string
  cinemaId: number
  cinemaName: string
  userId: number
  nickName: string
  phoneNumber: string
  cardAmount: number
  vipFee: number
  startTime: string // 用于表单显示的时间格式
  endTime: string   // 用于表单显示的时间格式
  effective: boolean
  displayDiscountPrice: number
  canUserCurrentCinema: boolean
  sort: number
  status: number
  cardStatus: number
  acquisitionMethod: number
  cardCreationDate: number
}

// 会员卡搜索表单数据
export interface VipCardSearchForm {
  cardNumber: string
  internalVipCardId: string
  cardType: string
  cinemaName: string
  phoneNumber: string
  status?: number
  dateRange?: [string, string]
}