<template>
  <ElDialog
    v-model="dialogVisible"
    title="会员卡详情"
    :width="800"
    destroy-on-close
  >
    <div v-loading="loading" class="card-detail">
      <div v-if="cardData" class="detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            基础信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>卡号：</label>
              <span>{{ cardData.cardNumber }}</span>
            </div>
            <div class="info-item">
              <label>影城卡名称：</label>
              <span>{{ cardData.name }}</span>
            </div>
            <div class="info-item">
              <label>影城卡类型：</label>
              <span>{{ cardData.cardType }}</span>
            </div>
            <div class="info-item">
              <label>影城名称：</label>
              <span>{{ cardData.cinemaName }}</span>
            </div>
            <div class="info-item">
              <label>会员名称：</label>
              <span>{{ cardData.nickName }}</span>
            </div>
            <div class="info-item">
              <label>手机号码：</label>
              <span>{{ cardData.phoneNumber }}</span>
            </div>
            <div class="info-item">
              <label>卡内余额(分)：</label>
              <span>{{ cardData.cardAmount }}</span>
            </div>
            <div class="info-item">
              <label>会费(分)：</label>
              <span>{{ cardData.vipFee }}</span>
            </div>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            状态信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>卡政策状态：</label>
              <ElTag :type="getVipCardPolicyStatusType(cardData.status)">
                {{ getVipCardPolicyStatusText(cardData.status) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>卡状态：</label>
              <ElTag :type="getCardStatusType(cardData.cardStatus)">
                {{ getCardStatusText(cardData.cardStatus) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>是否有效：</label>
              <ElTag :type="cardData.effective ? 'success' : 'danger'">
                {{ cardData.effective ? '有效' : '无效' }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>是否支持当前影院使用：</label>
              <ElTag :type="cardData.canUserCurrentCinema ? 'success' : 'danger'">
                {{ cardData.canUserCurrentCinema ? '支持' : '不支持' }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>获得方式：</label>
              <ElTag :type="getAcquisitionMethodType(cardData.acquisitionMethod)">
                {{ getAcquisitionMethodText(cardData.acquisitionMethod) }}
              </ElTag>
            </div>
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            时间信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>有效期开始时间：</label>
              <span>{{ formatTimestamp(cardData.startTime) }}</span>
            </div>
            <div class="info-item">
              <label>有效期结束时间：</label>
              <span>{{ formatTimestamp(cardData.endTime) }}</span>
            </div>
            <div class="info-item">
              <label>制卡日期：</label>
              <span>{{ formatTimestamp(cardData.cardCreationDate) }}</span>
            </div>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            其他信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>批次号：</label>
              <span>{{ cardData.batchId }}</span>
            </div>
            <div class="info-item">
              <label>推荐排序：</label>
              <span>{{ cardData.sort }}</span>
            </div>
            <div class="info-item">
              <label>是否显示优惠价：</label>
              <span>{{ cardData.displayDiscountPrice === 1 ? '显示' : '不显示' }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <ElEmpty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">
          关闭
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
// 类型导入
import type { VipCard } from '../types'
import { ElMessage } from 'element-plus'

import { computed, ref, watch } from 'vue'
// API 导入（后续需要实现）
// import { getVipCardDetail } from '@/api/modules/market/vipCard'

// 常量导入
import {
  formatTimestamp,
  getVipCardPolicyStatusText,
  getVipCardPolicyStatusType,
  getCardStatusText,
  getCardStatusType,
  getAcquisitionMethodText,
  getAcquisitionMethodType,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  cardId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 加载状态
const loading = ref(false)

// 会员卡数据
const cardData = ref<VipCard | null>(null)

// ==================== 监听器 ====================

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.cardId) {
    fetchCardDetail()
  }
})

// ==================== 方法定义 ====================

/**
 * 获取会员卡详情
 */
async function fetchCardDetail() {
  if (!props.cardId) { return }

  try {
    loading.value = true

    // 使用模拟数据（开发阶段）
    // const response = await mockGetVipCardDetail(props.cardId)

    // 生产环境使用真实API
    // const response = await getVipCardDetail({ cardId: props.cardId })

    // 模拟数据
    cardData.value = {
      id: props.cardId,
      internalVipCardId: 'card001',
      batchId: 'batch001',
      cardNumber: '123456789012345678',
      name: '金卡',
      cardType: '储值卡',
      cardTypeName: '储值卡',
      cinemaId: 1,
      cinemaName: '万达影城',
      userId: 1001,
      nickName: '张三',
      phoneNumber: '13800138000',
      cardAmount: 50000,
      vipFee: 10000,
      startTime: Date.now() - 86400000 * 30,
      endTime: Date.now() + 86400000 * 335,
      effective: true,
      displayDiscountPrice: 1,
      canUserCurrentCinema: true,
      sort: 1,
      status: 1,
      cardStatus: 1,
      acquisitionMethod: 1,
      cardCreationDate: Date.now() - 86400000 * 40,
    }
  }
  catch (error) {
    console.error('获取会员卡详情失败:', error)
    ElMessage.error('获取会员卡详情失败')
    cardData.value = null
  }
  finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.card-detail {
  .detail-content {
    .detail-section {
      margin-bottom: 24px;
      padding: 20px;
      background: var(--el-fill-color-blank);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        label {
          min-width: 150px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        span {
          color: var(--el-text-color-primary);
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.dialog-footer {
  text-align: center;
}
</style>