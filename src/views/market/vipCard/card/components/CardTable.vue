<template>
  <div class="card-table">
    <ElTable
      ref="tableRef"
      :data="data"
      :loading="loading"
      row-key="id"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <ElTableColumn type="selection" width="55" align="center" />

      <!-- 卡号 -->
      <ElTableColumn prop="cardNumber" label="卡号" min-width="150" show-overflow-tooltip />

      <!-- 影城卡名称 -->
      <ElTableColumn prop="name" label="影城卡名称" min-width="150" show-overflow-tooltip />

      <!-- 影城名称 -->
      <ElTableColumn prop="cinemaName" label="影城名称" min-width="150" show-overflow-tooltip />

      <!-- 会员名称 -->
      <ElTableColumn prop="nickName" label="会员名称" min-width="120" show-overflow-tooltip />

      <!-- 手机号码 -->
      <ElTableColumn prop="phoneNumber" label="手机号码" width="120" align="center" />

      <!-- 卡内余额 -->
      <ElTableColumn prop="cardAmount" label="卡内余额(分)" width="120" align="center" />

      <!-- 卡状态 -->
      <ElTableColumn prop="cardStatus" label="卡状态" width="100" align="center">
        <template #default="{ row }">
          <ElTag :type="getCardStatusType(row.cardStatus)">
            {{ getCardStatusText(row.cardStatus) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 有效期 -->
      <ElTableColumn label="有效期" width="380" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.startTime) }} 至 {{ formatTimestamp(row.endTime) }}
        </template>
      </ElTableColumn>

      <!-- 操作列 -->
      <ElTableColumn label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <ElButton type="primary" size="small" text @click="handleView(row)">
              查看
            </ElButton>
            <ElButton type="warning" size="small" text @click="handleEdit(row)">
              编辑
            </ElButton>
            <ElButton
              :type="row.cardStatus === 1 ? 'danger' : 'success'"
              size="small"
              text
              @click="handleStatusChange(row)"
            >
              {{ row.cardStatus === 1 ? '冻结' : '解冻' }}
            </ElButton>
            <ElButton type="danger" size="small" text @click="handleDelete(row)">
              删除
            </ElButton>
          </div>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ElTable } from 'element-plus'

// 类型导入
import type { VipCard } from '../types'

// 常量导入
import {
  formatTimestamp,
  getCardStatusText,
  getCardStatusType,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  data: VipCard[]
  loading?: boolean
  selectedIds?: string[]
}

interface Emits {
  (e: 'selection-change', ids: string[]): void
  (e: 'edit', card: VipCard): void
  (e: 'delete', card: VipCard): void
  (e: 'view', card: VipCard): void
  (e: 'status-change', card: VipCard, status: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedIds: () => [],
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>()

// ==================== 方法定义 ====================

/**
 * 选择变更
 */
function handleSelectionChange(selection: VipCard[]) {
  const ids = selection.map(item => item.id)
  emit('selection-change', ids)
}

/**
 * 查看
 */
function handleView(card: VipCard) {
  emit('view', card)
}

/**
 * 编辑
 */
function handleEdit(card: VipCard) {
  emit('edit', card)
}

/**
 * 删除
 */
function handleDelete(card: VipCard) {
  emit('delete', card)
}

/**
 * 状态变更
 */
function handleStatusChange(card: VipCard) {
  const newStatus = card.cardStatus === 1 ? 2 : 1
  emit('status-change', card, newStatus)
}

// ==================== 暴露方法 ====================

defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: VipCard, selected?: boolean) => 
    tableRef.value?.toggleRowSelection(row, selected),
})
</script>

<style scoped lang="scss">
.card-table {
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 表格样式优化 */
:deep(.el-table) {
  .el-table__header {
    background: var(--el-fill-color-light);
  }

  .el-table__row {
    &:hover {
      background: var(--el-fill-color-lighter);
    }
  }

  .el-table__cell {
    padding: 12px 8px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>