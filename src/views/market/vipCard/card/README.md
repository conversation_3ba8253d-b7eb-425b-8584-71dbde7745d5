# 会员卡管理模块

## 📋 模块概述

会员卡管理模块是会员系统的重要组成部分，提供完整的会员卡创建、编辑、查看和管理功能。基于API文档 `src/views/market/vipCard/会员 api.md` 实现，确保与后端接口完全兼容。

## 🏗️ 项目结构

```
src/views/market/vipCard/card/
├── 📁 components/                    # 组件目录
│   ├── CardSearchForm.vue           # 搜索表单组件
│   ├── CardTable.vue                # 会员卡列表表格组件
│   ├── CardConfigDialog.vue         # 会员卡配置对话框组件
│   └── CardDetailDialog.vue         # 会员卡详情对话框组件
├── 📁 types/                         # 类型定义
│   └── index.ts                     # 会员卡相关类型定义
├── 📁 constants/                     # 常量定义
│   └── index.ts                     # 会员卡相关常量和工具函数
├── index.vue                         # 会员卡管理主页面
└── README.md                         # 本文档

src/api/modules/market/vipCard/
├── index.ts                          # 会员卡API接口
└── 会员 api.md                       # API文档

src/router/modules/
└── vipCard.ts                        # 会员卡路由配置
```

## 🎯 核心功能

### 1. **会员卡列表管理**
- ✅ 会员卡列表展示（表格形式）
- ✅ 多条件搜索和筛选
- ✅ 分页显示
- ✅ 批量操作（删除）
- ✅ 状态管理（正常/冻结）

### 2. **会员卡配置**
- ✅ 新增会员卡
- ✅ 编辑会员卡
- ✅ 会员卡详情查看
- ✅ 会员卡状态变更

### 3. **数据管理**
- ✅ 完整的类型定义
- ✅ 表单验证
- ✅ 错误处理
- ✅ 加载状态管理

## 🔧 技术特性

### **前端技术栈**
- **Vue 3** - 组合式API
- **TypeScript** - 类型安全
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理（可选）

### **代码特性**
- 📝 **完整的TypeScript支持**
- 🎨 **响应式设计**
- 🔄 **组件化架构**
- 📱 **移动端适配**
- 🛡️ **错误边界处理**
- 🎯 **性能优化**

## 📊 数据结构

### **会员卡基础信息**
```typescript
interface VipCard {
  id: string                    // id
  internalVipCardId: string     // 内部会员卡id
  batchId: string               // 批次号 生成规则8位数字 自增
  cardNumber: string            // 内部会员卡卡号
  name: string                  // 影城卡名称
  cardType: string              // 影城卡类型
  cardTypeName: string          // 影城卡类型名称
  cinemaId: number              // 影城id
  cinemaName: string            // 影城名称
  userId: number                // 用户id
  nickName: string              // 会员名称
  phoneNumber: string           // 手机号码
  cardAmount: number            // 卡内余额
  vipFee: number                // 会费，单位：分
  startTime: number             // 有效期开始时间
  endTime: number               // 有效期结束时间
  effective: boolean            // 是否有效 true：有效，false：无效
  displayDiscountPrice: number  // 是否显示优惠价：不显示0 显示1
  canUserCurrentCinema: boolean // 是否支持当前影院使用
  sort: number                  // 推荐排序
  status: number                // 卡政策状态: 下架0 上架1 草稿2
  cardStatus: number            // 卡状态：初始化-1 未激活 0 正常1 冻结2 过期3 作废 4
  acquisitionMethod: number     // 获得方式：云智老卡0 个人购买电子卡1
  cardCreationDate: number      // 制卡日期
}
```

### **搜索参数**
```typescript
interface VipCardListParams {
  page?: number                 // 页码
  size?: number                 // 每页数量
  cardNumber?: string           // 卡号
  internalVipCardId?: string    // 卡政策ID
  cardType?: string             // 卡类型
  cinemaName?: string           // 发卡影院
  phoneNumber?: string          // 手机号码
  status?: number               // 状态
  startTime?: number            // 有效日期-开始时间
  endTime?: number              // 有效日期-结束时间
}
```

## 🎨 组件说明

### **1. CardSearchForm.vue**
**功能**: 会员卡搜索和筛选表单
**特性**:
- 基础搜索（卡号、卡状态、有效日期）
- 高级搜索（影城卡ID、卡类型、发卡影院、手机号码）
- 实时搜索
- 导出功能

### **2. CardTable.vue**
**功能**: 会员卡列表表格展示
**特性**:
- 表格展示会员卡信息
- 状态标签显示
- 操作按钮（查看、编辑、删除、状态切换）
- 批量选择

### **3. CardConfigDialog.vue**
**功能**: 会员卡配置对话框
**特性**:
- 支持新增、编辑、查看三种模式
- 表单验证
- 响应式布局

### **4. CardDetailDialog.vue**
**功能**: 会员卡详情展示对话框
**特性**:
- 完整的会员卡信息展示
- 时间信息展示
- 状态信息展示

## 🚀 使用指南

### **1. 页面访问**
```
会员卡列表: /market/vipCard/card/list
新增会员卡: /market/vipCard/card/create
编辑会员卡: /market/vipCard/card/edit/:id
会员卡详情: /market/vipCard/card/detail/:id
```

### **2. 组件使用**
```vue
<!-- 在其他页面中使用会员卡配置对话框 -->
<CardConfigDialog
  v-model="showDialog"
  :card-id="cardId"
  :page-mode="pageMode"
  @submit="handleSubmit"
  @success="handleSuccess"
/>
```

### **3. API调用**

```typescript
import {getVipCardList, createVipCard} from 'src/api/modules/market/vipCard'

// 获取会员卡列表
const response = await getVipCardList({
    page: 1,
    size: 20,
    cardNumber: '卡号'
})

// 创建会员卡
await createVipCard({
    cardNumber: '卡号',
    name: '金卡',
    // ... 其他参数
})
```

## 🔄 开发模式

### **模拟数据**
开发阶段使用模拟数据，生产环境切换到真实API：

```typescript
// 开发环境
const response = await mockGetVipCardList(params)

// 生产环境
// const response = await getVipCardList(params)
```

### **切换方式**
1. 注释掉模拟API调用
2. 取消注释真实API调用
3. 确保后端接口可用

## 📋 待完善功能

### **短期计划**
- [ ] 会员卡新增/编辑功能
- [ ] 会员卡导出功能
- [ ] 会员卡批量操作

### **长期计划**
- [ ] 会员卡统计图表
- [ ] 会员卡模板功能
- [ ] 会员卡复制功能

## 🧪 测试建议

### **功能测试**
1. **基础功能**：创建、编辑、删除、查看会员卡
2. **搜索功能**：各种搜索条件组合测试
3. **表单验证**：必填字段、格式验证
4. **状态管理**：正常/冻结状态切换

### **兼容性测试**
1. **浏览器兼容**：Chrome、Firefox、Safari、Edge
2. **设备兼容**：桌面端、平板、手机
3. **分辨率适配**：各种屏幕尺寸

### **性能测试**
1. **大数据量**：测试大量会员卡数据的加载性能
2. **内存使用**：长时间使用的内存泄漏检测

## 🔗 相关文档

- [API文档](../../../api/modules/market/vipCard/会员 api.md)
- [卡赠礼模块](../gift/README.md)
- [卡政策模块](../policy/README.md)
- [路由配置](../../../router/modules/market/vipCard.ts)
- [类型定义](./types/index.ts)

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看控制台错误**：检查浏览器控制台的错误信息
2. **检查网络请求**：确认API请求是否正常
3. **验证数据格式**：确保传递的数据格式正确
4. **查看文档**：参考API文档和类型定义

---

**开发状态**: ✅ 已完成基础功能  
**维护状态**: 🔄 持续维护  
**版本**: v1.0.0