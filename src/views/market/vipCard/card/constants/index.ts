/**
 * 会员卡管理常量定义
 */

import { VipCardPolicyStatus, CardStatus, AcquisitionMethod } from '../types'

// 卡政策状态选项
export const VIP_CARD_POLICY_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '下架', value: VipCardPolicyStatus.DISABLED },
  { label: '上架', value: VipCardPolicyStatus.ENABLED },
  { label: '草稿', value: VipCardPolicyStatus.DRAFT },
]

// 卡政策状态标签映射
export const VIP_CARD_POLICY_STATUS_MAP = {
  [VipCardPolicyStatus.DISABLED]: { label: '下架', type: 'danger' },
  [VipCardPolicyStatus.ENABLED]: { label: '上架', type: 'success' },
  [VipCardPolicyStatus.DRAFT]: { label: '草稿', type: 'info' },
}

// 卡状态选项
export const CARD_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '初始化', value: CardStatus.INIT },
  { label: '未激活', value: CardStatus.INACTIVE },
  { label: '正常', value: CardStatus.NORMAL },
  { label: '冻结', value: CardStatus.FROZEN },
  { label: '过期', value: CardStatus.EXPIRED },
  { label: '作废', value: CardStatus.INVALID },
]

// 卡状态标签映射
export const CARD_STATUS_MAP = {
  [CardStatus.INIT]: { label: '初始化', type: 'info' },
  [CardStatus.INACTIVE]: { label: '未激活', type: 'warning' },
  [CardStatus.NORMAL]: { label: '正常', type: 'success' },
  [CardStatus.FROZEN]: { label: '冻结', type: 'danger' },
  [CardStatus.EXPIRED]: { label: '过期', type: 'danger' },
  [CardStatus.INVALID]: { label: '作废', type: 'danger' },
}

// 获得方式选项
export const ACQUISITION_METHOD_OPTIONS = [
  { label: '云智老卡', value: AcquisitionMethod.OLD_CARD },
  { label: '个人购买电子卡', value: AcquisitionMethod.PERSONAL_BUY },
]

// 获得方式标签映射
export const ACQUISITION_METHOD_MAP = {
  [AcquisitionMethod.OLD_CARD]: { label: '云智老卡', type: 'primary' },
  [AcquisitionMethod.PERSONAL_BUY]: { label: '个人购买电子卡', type: 'success' },
}

// 分页配置
export const PAGINATION_CONFIG = {
  page: 1,
  size: 20,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
}

// 表单验证规则
export const VIP_CARD_FORM_RULES = {
  name: [
    { required: true, message: '请输入影城卡名称', trigger: 'blur' },
    { min: 2, max: 50, message: '影城卡名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  cardNumber: [
    { required: true, message: '请输入卡号', trigger: 'blur' },
  ],
  cardType: [
    { required: true, message: '请输入影城卡类型', trigger: 'blur' },
  ],
  cinemaName: [
    { required: true, message: '请输入影城名称', trigger: 'blur' },
  ],
  nickName: [
    { required: true, message: '请输入会员名称', trigger: 'blur' },
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
  ],
}

// 快速时间选择选项
export const QUICK_DATE_OPTIONS = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '明天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() + 1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setDate(end.getDate() + 1)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本周',
    value: () => {
      const start = new Date()
      const day = start.getDay()
      const diff = start.getDate() - day + (day === 0 ? -6 : 1)
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const start = new Date()
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setMonth(end.getMonth() + 1, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
]

// 默认表单数据
export const DEFAULT_VIP_CARD_FORM: Partial<VipCardFormData> = {
  internalVipCardId: '',
  batchId: '',
  cardNumber: '',
  name: '',
  cardType: '',
  cardTypeName: '',
  cinemaId: 0,
  cinemaName: '',
  userId: 0,
  nickName: '',
  phoneNumber: '',
  cardAmount: 0,
  vipFee: 0,
  startTime: '',
  endTime: '',
  effective: true,
  displayDiscountPrice: 0,
  canUserCurrentCinema: true,
  sort: 0,
  status: 0,
  cardStatus: 0,
  acquisitionMethod: 0,
  cardCreationDate: Date.now(),
}

// 工具函数
export const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

export const getVipCardPolicyStatusText = (status: number): string => {
  return VIP_CARD_POLICY_STATUS_MAP[status]?.label || '未知'
}

export const getVipCardPolicyStatusType = (status: number): string => {
  return VIP_CARD_POLICY_STATUS_MAP[status]?.type || 'info'
}

export const getCardStatusText = (cardStatus: number): string => {
  return CARD_STATUS_MAP[cardStatus]?.label || '未知'
}

export const getCardStatusType = (cardStatus: number): string => {
  return CARD_STATUS_MAP[cardStatus]?.type || 'info'
}

export const getAcquisitionMethodText = (acquisitionMethod: number): string => {
  return ACQUISITION_METHOD_MAP[acquisitionMethod]?.label || '未知'
}

export const getAcquisitionMethodType = (acquisitionMethod: number): string => {
  return ACQUISITION_METHOD_MAP[acquisitionMethod]?.type || 'info'
}