<route lang="yaml">
meta:
  title: 会员卡管理
  icon: i-ep:credit-card
  auth: /market/vipCardUser/query
</route>

<script setup lang="ts">
// 类型导入
import type { VipCard, VipCardListParams } from './types'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref } from 'vue'
// API 导入（后续需要实现）
// import {
//   getVipCardList,
//   deleteVipCard,
//   updateVipCardStatus,
// } from '@/api/modules/market/vipCard'

// 组件导入
import CardSearchForm from './components/CardSearchForm.vue'
import CardTable from './components/CardTable.vue'
import CardDetailDialog from './components/CardDetailDialog.vue'
// import CardConfigDialog from './components/CardConfigDialog.vue'

import { PAGINATION_CONFIG } from './constants'

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)

// 会员卡列表
const cardList = ref<VipCard[]>([])

// 搜索参数
const searchParams = ref<VipCardListParams>({
  page: 1,
  size: 20,
  cardNumber: '',
  status: undefined,
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  ...PAGINATION_CONFIG,
})

// 选中的会员卡ID
const selectedIds = ref<string[]>([])

// 对话框状态
const showDetailDialog = ref(false)
// const showConfigDialog = ref(false)
// const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const viewCardId = ref('')

// ==================== 生命周期 ====================

onMounted(() => {
  fetchCardList()
})

// ==================== 方法定义 ====================

/**
 * 获取会员卡列表
 */
async function fetchCardList() {
  try {
    loading.value = true

    // 生产环境使用真实API
    // const response = await getVipCardList({
    //   ...searchParams.value,
    //   page: pagination.page,
    //   size: pagination.size,
    // })

    // 模拟数据
    const response = {
      code: 0,
      total: 2,
      content: [
        {
          id: 'card001',
          internalVipCardId: 'card001',
          batchId: 'batch001',
          cardNumber: '123456789012345678',
          name: '金卡',
          cardType: '储值卡',
          cardTypeName: '储值卡',
          cinemaId: 1,
          cinemaName: '万达影城',
          userId: 1001,
          nickName: '张三',
          phoneNumber: '13800138000',
          cardAmount: 50000,
          vipFee: 10000,
          startTime: Date.now() - 86400000 * 30,
          endTime: Date.now() + 86400000 * 335,
          effective: true,
          displayDiscountPrice: 1,
          canUserCurrentCinema: true,
          sort: 1,
          status: 1,
          cardStatus: 1,
          acquisitionMethod: 1,
          cardCreationDate: Date.now() - 86400000 * 40,
        },
        {
          id: 'card002',
          internalVipCardId: 'card002',
          batchId: 'batch002',
          cardNumber: '234567890123456789',
          name: '银卡',
          cardType: '储值卡',
          cardTypeName: '储值卡',
          cinemaId: 2,
          cinemaName: 'CGV影城',
          userId: 1002,
          nickName: '李四',
          phoneNumber: '13900139000',
          cardAmount: 30000,
          vipFee: 5000,
          startTime: Date.now() - 86400000 * 20,
          endTime: Date.now() + 86400000 * 345,
          effective: true,
          displayDiscountPrice: 0,
          canUserCurrentCinema: true,
          sort: 2,
          status: 1,
          cardStatus: 2,
          acquisitionMethod: 1,
          cardCreationDate: Date.now() - 86400000 * 30,
        }
      ]
    }

    console.log('获取会员卡列表响应:', response)
    const { code, content, total } = response
    if (code === 0) {
      cardList.value = content || []
      pagination.total = total || 0
    }
  }
  catch (error) {
    console.error('获取会员卡列表失败:', error)
    ElMessage.error('获取会员卡列表失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
function handleSearch(params: VipCardListParams) {
  searchParams.value = { ...params }
  pagination.page = 1
  fetchCardList()
}

/**
 * 重置搜索
 */
function handleReset() {
  searchParams.value = {
    page: 1,
    size: 20,
    cardNumber: '',
    status: undefined,
  }
  pagination.page = 1
  fetchCardList()
}

/**
 * 导出
 */
async function handleExport() {
  try {
    loading.value = true
    // await exportVipCardList(searchParams.value)
    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 新增会员卡
 */
// function handleCreate() {
//   dialogMode.value = 'add'
//   showConfigDialog.value = true
// }

/**
 * 编辑会员卡
 */
// function handleEdit(card: VipCard) {
//   dialogMode.value = 'edit'
//   showConfigDialog.value = true
// }

/**
 * 查看会员卡详情
 */
function handleView(card: VipCard) {
  viewCardId.value = card.id
  showDetailDialog.value = true
}

/**
 * 删除会员卡
 */
async function handleDelete(card: VipCard) {
  try {
    await ElMessageBox.confirm(
      `确定要删除会员卡"${card.cardNumber}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    // await deleteVipCard(card.id)
    ElMessage.success('删除成功')
    await fetchCardList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除会员卡失败:', error)
      ElMessage.error('删除会员卡失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 批量删除
 */
async function handleBatchDelete() {
  if (!selectedIds.value.length) {
    ElMessage.warning('请选择要删除的会员卡')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个会员卡吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    // await batchDeleteVipCards(selectedIds.value)
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    fetchCardList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 状态变更
 */
async function handleStatusChange(card: VipCard, status: number) {
  try {
    loading.value = true
    // await updateVipCardStatus({
    //   cardId: card.id,
    //   status,
    // })
    ElMessage.success('状态更新成功')
    await fetchCardList()
  }
  catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 选择变更
 */
function handleSelectionChange(ids: string[]) {
  selectedIds.value = ids
}

/**
 * 分页大小变更
 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchCardList()
}

/**
 * 当前页变更
 */
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchCardList()
}

/**
 * 配置对话框提交
 */
// function handleConfigSubmit(data: any) {
//   console.log('配置提交:', data)
// }

/**
 * 配置对话框成功
 */
// function handleConfigSuccess() {
//   showConfigDialog.value = false
//   fetchCardList()
// }

/**
 * 配置对话框取消
 */
// function handleConfigCancel() {
//   showConfigDialog.value = false
// }
</script>

<template>
  <div>
    <FaPageHeader title="会员卡管理" description="管理会员的卡片信息" />

    <FaPageMain>
      <!-- 搜索表单 -->
      <CardSearchForm
        v-model="searchParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @export="handleExport"
      />

      <!-- 操作按钮 -->
      <div class="flex gap-3 my-5 p-4 bg-white rounded-lg border border-gray-200 md:flex-row flex-col md:gap-3 gap-2">
        <!-- <ElButton type="primary" :icon="Plus" @click="handleCreate">
          新增会员卡
        </ElButton> -->
        <ElButton
          type="danger"
          :icon="Delete"
          :disabled="!selectedIds.length"
          @click="handleBatchDelete"
        >
          批量删除
        </ElButton>
      </div>

      <!-- 会员卡列表 -->
      <CardTable
        :data="cardList"
        :loading="loading"
        :selected-ids="selectedIds"
        @selection-change="handleSelectionChange"
        @edit="handleEdit"
        @delete="handleDelete"
        @view="handleView"
        @status-change="handleStatusChange"
      />

      <!-- 分页 -->
      <div class="flex justify-center mt-5 p-4 bg-white rounded-lg border border-gray-200">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          :layout="pagination.layout"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 会员卡配置对话框 -->
      <!-- <CardConfigDialog
        v-model="showConfigDialog"
        :card-id="editCardId"
        :page-mode="dialogMode"
        @submit="handleConfigSubmit"
        @success="handleConfigSuccess"
        @cancel="handleConfigCancel"
      /> -->

      <!-- 会员卡详情对话框 -->
      <CardDetailDialog
        v-model="showDetailDialog"
        :card-id="viewCardId"
      />
    </FaPageMain>
  </div>
</template>

<style scoped lang="scss">
/* 已使用 Tailwind CSS 类名，移除原有样式 */
</style>