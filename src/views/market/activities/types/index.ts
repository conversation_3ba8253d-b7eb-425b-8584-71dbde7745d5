/**
 * 活动管理相关类型定义
 */

// 活动优惠券配置
export interface ActivityCoupon {
  couponId: number
  couponName: string
  couponNum: number
  faceValue?: number
  couponType?: number
  useOn?: number
  validStartTime?: string
  validEndTime?: string
}

// 活动基础信息
export interface Activity {
  id: string
  name: string
  banner: string
  detailImg: string
  detailImgHotArea?: string
  linkType?: string
  linkParam?: string
  description: string
  startTime: number
  endTime: number
  userType: string
  limitTimes: number
  welfareType: number
  showType: number
  status: number
  seq: number
  delFlag: boolean
  version: string
  activityCoupons: ActivityCoupon[]
  createTime: number
  updateTime: number
}

// 活动列表查询参数
export interface ActivityListParams {
  page?: number
  size?: number
  name?: string
  status?: number
  startTimeBegin?: number
  startTimeEnd?: number
}

// 活动列表响应
export interface ActivityListResponse {
  total: number
  content: Activity[]
  result: Activity[]
}

// 创建活动参数
export interface CreateActivityParams {
  name: string
  banner?: string
  detailImg?: string
  description?: string
  startTime: number
  endTime: number
  limitType?: number
  limitCount?: number
  coupons?: ActivityCoupon[]
  stores?: number[]
  remark?: string
}

// 编辑活动参数
export interface UpdateActivityParams extends CreateActivityParams {
  id: string
}

// 活动状态变更参数
export interface ActivityStatusParams {
  id: string
  useStatus: number
}

// 获取活动详情参数
export interface ActivityDetailParams {
  activityId: string
}

// 活动状态枚举
export enum ActivityStatus {
  DISABLED = 0, // 关闭
  ENABLED = 1, // 开启
}

// 展示方式枚举
export enum ShowType {
  PUBLIC = 0, // 公开展示
  HIDDEN = 1, // 隐藏展示
}

// 用户类型枚举
export enum UserType {
  ALL = '0', // 全部用户
  NEW = '1', // 新用户
  SPECIFIC = '2', // 指定用户
}

// 福利类型枚举
export enum WelfareType {
  NONE = 0, // 无福利
  COUPON = 1, // 优惠券
  POINTS = 2, // 积分
}

// 限制类型枚举
export enum LimitType {
  UNLIMITED = 0, // 不限制
  LIMITED = 1, // 限制次数
}

// 活动表单数据
export interface ActivityFormData {
  id?: string
  name: string
  banner: string
  detailImg: string
  description: string
  startTime: string
  endTime: string
  userType: string
  limitTimes: number
  welfareType: number
  showType: number
  status: number
  seq: number
  activityCoupons: ActivityCoupon[]
  stores: number[]
  remark: string
}

// 活动搜索表单数据
export interface ActivitySearchForm {
  name: string
  status?: number
  dateRange?: [string, string]
}
