<script setup lang="ts">
// 类型导入
import type { Activity, ActivityListParams } from './types'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref } from 'vue'
// API 导入
import {
  batchDeleteActivities,
  deleteActivity,
  exportActivityList,
  getActivityList,
  updateActivityStatus,
} from '@/api/modules/market/activities'
import ActivityConfigDialog from './components/ActivityConfigDialog.vue'
import ActivityDetailDialog from './components/ActivityDetailDialog.vue'

// 组件导入
import ActivitySearchForm from './components/ActivitySearchForm.vue'
import ActivityTable from './components/ActivityTable.vue'

import { PAGINATION_CONFIG } from './constants'

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)

// 活动列表
const activityList = ref<Activity[]>([])

// 搜索参数
const searchParams = ref<ActivityListParams>({
  page: 1,
  size: 20,
  name: '',
  status: undefined,
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  ...PAGINATION_CONFIG,
})

// 选中的活动ID
const selectedIds = ref<string[]>([])

// 对话框状态
const showConfigDialog = ref(false)
const showDetailDialog = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const editActivityId = ref('')
const viewActivityId = ref('')

// ==================== 生命周期 ====================

onMounted(() => {
  fetchActivityList()
})

// ==================== 方法定义 ====================

/**
 * 获取活动列表
 */
async function fetchActivityList() {
  try {
    loading.value = true

    // 生产环境使用真实API
    const response = await getActivityList({
      ...searchParams.value,
      page: pagination.page,
      size: pagination.size,
    })

    console.log('获取活动列表响应:', response)
    const { code, data } = response
    if (code === 0) {
      activityList.value = data.content || []
      pagination.total = response.total || 0
    }
  }
  catch (error) {
    console.error('获取活动列表失败:', error)
    ElMessage.error('获取活动列表失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
function handleSearch(params: ActivityListParams) {
  searchParams.value = { ...params }
  pagination.page = 1
  fetchActivityList()
}

/**
 * 重置搜索
 */
function handleReset() {
  searchParams.value = {
    page: 1,
    size: 20,
    name: '',
    status: undefined,
  }
  pagination.page = 1
  fetchActivityList()
}

/**
 * 导出
 */
async function handleExport() {
  try {
    loading.value = true
    await exportActivityList(searchParams.value)
    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 新增活动
 */
function handleCreate() {
  dialogMode.value = 'add'
  editActivityId.value = ''
  showConfigDialog.value = true
}

/**
 * 编辑活动
 */
function handleEdit(activity: Activity) {
  dialogMode.value = 'edit'
  editActivityId.value = activity.id
  showConfigDialog.value = true
}

/**
 * 查看活动详情
 */
function handleView(activity: Activity) {
  viewActivityId.value = activity.id
  showDetailDialog.value = true
}

/**
 * 删除活动
 */
async function handleDelete(activity: Activity) {
  try {
    await ElMessageBox.confirm(
      `确定要删除活动"${activity.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    await deleteActivity(activity.id)
    ElMessage.success('删除成功')
    await fetchActivityList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除活动失败:', error)
      ElMessage.error('删除活动失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 批量删除
 */
async function handleBatchDelete() {
  if (!selectedIds.value.length) {
    ElMessage.warning('请选择要删除的活动')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个活动吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true
    await batchDeleteActivities(selectedIds.value)
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    fetchActivityList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 状态变更
 */
async function handleStatusChange(activity: Activity, status: number) {
  try {
    loading.value = true
    await updateActivityStatus({
      id: activity.id,
      useStatus: status,
    })
    ElMessage.success('状态更新成功')
    await fetchActivityList()
  }
  catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 选择变更
 */
function handleSelectionChange(ids: string[]) {
  selectedIds.value = ids
}

/**
 * 分页大小变更
 */
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchActivityList()
}

/**
 * 当前页变更
 */
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchActivityList()
}

/**
 * 配置对话框提交
 */
function handleConfigSubmit(data: any) {
  console.log('配置提交:', data)
}

/**
 * 配置对话框成功
 */
function handleConfigSuccess() {
  showConfigDialog.value = false
  fetchActivityList()
}

/**
 * 配置对话框取消
 */
function handleConfigCancel() {
  showConfigDialog.value = false
}
</script>

<template>
  <div class="activities-management">
    <!-- 搜索表单 -->
    <ActivitySearchForm
      v-model="searchParams"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
      @export="handleExport"
    />

    <!-- 操作按钮 -->
    <div class="actions-bar">
      <ElButton type="primary" :icon="Plus" @click="handleCreate">
        新增活动
      </ElButton>
      <ElButton
        type="danger"
        :icon="Delete"
        :disabled="!selectedIds.length"
        @click="handleBatchDelete"
      >
        批量删除
      </ElButton>
    </div>

    <!-- 活动列表 -->
    <ActivityTable
      :data="activityList"
      :loading="loading"
      :selected-ids="selectedIds"
      @selection-change="handleSelectionChange"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
      @status-change="handleStatusChange"
    />

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <ElPagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="pagination.pageSizes"
        :layout="pagination.layout"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 活动配置对话框 -->
    <ActivityConfigDialog
      v-model="showConfigDialog"
      :activity-id="editActivityId"
      :page-mode="dialogMode"
      @submit="handleConfigSubmit"
      @success="handleConfigSuccess"
      @cancel="handleConfigCancel"
    />

    <!-- 活动详情对话框 -->
    <ActivityDetailDialog
      v-model="showDetailDialog"
      :activity-id="viewActivityId"
    />
  </div>
</template>

<style scoped lang="scss">
.activities-management {
  padding: 20px;
  background: var(--el-bg-color);
  min-height: 100vh;
}

.actions-bar {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  padding: 16px;
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activities-management {
    padding: 10px;
  }

  .actions-bar {
    flex-direction: column;
    gap: 8px;
  }

  .actions-bar .el-button {
    width: 100%;
  }
}
</style>
