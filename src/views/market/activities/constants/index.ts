/**
 * 活动管理常量定义
 */

import { ActivityStatus, ShowType, UserType, WelfareType, LimitType } from '../types'

// 活动状态选项
export const ACTIVITY_STATUS_OPTIONS = [
  { label: '全部', value: undefined },
  { label: '已启用', value: ActivityStatus.ENABLED },
  { label: '已停用', value: ActivityStatus.DISABLED },
]

// 活动状态标签映射
export const ACTIVITY_STATUS_MAP = {
  [ActivityStatus.DISABLED]: { label: '已停用', type: 'danger' },
  [ActivityStatus.ENABLED]: { label: '已启用', type: 'success' },
}

// 展示方式选项
export const SHOW_TYPE_OPTIONS = [
  { label: '公开展示', value: ShowType.PUBLIC },
  { label: '隐藏展示', value: ShowType.HIDDEN },
]

// 展示方式标签映射
export const SHOW_TYPE_MAP = {
  [ShowType.PUBLIC]: { label: '公开展示', type: 'success' },
  [ShowType.HIDDEN]: { label: '隐藏展示', type: 'warning' },
}

// 用户类型选项
export const USER_TYPE_OPTIONS = [
  { label: '全部用户', value: UserType.ALL },
  { label: '新用户', value: UserType.NEW },
  { label: '指定用户', value: UserType.SPECIFIC },
]

// 用户类型标签映射
export const USER_TYPE_MAP = {
  [UserType.ALL]: { label: '全部用户', type: 'primary' },
  [UserType.NEW]: { label: '新用户', type: 'success' },
  [UserType.SPECIFIC]: { label: '指定用户', type: 'warning' },
}

// 福利类型选项
export const WELFARE_TYPE_OPTIONS = [
  { label: '无福利', value: WelfareType.NONE },
  { label: '优惠券', value: WelfareType.COUPON },
  { label: '积分', value: WelfareType.POINTS },
]

// 福利类型标签映射
export const WELFARE_TYPE_MAP = {
  [WelfareType.NONE]: { label: '无福利', type: 'info' },
  [WelfareType.COUPON]: { label: '优惠券', type: 'success' },
  [WelfareType.POINTS]: { label: '积分', type: 'warning' },
}

// 限制类型选项
export const LIMIT_TYPE_OPTIONS = [
  { label: '不限制', value: LimitType.UNLIMITED },
  { label: '限制次数', value: LimitType.LIMITED },
]

// 表格列配置
export const ACTIVITY_TABLE_COLUMNS = [
  { prop: 'name', label: '活动名称', minWidth: 200, showOverflowTooltip: true },
  { prop: 'status', label: '状态', width: 100, align: 'center' },
  { prop: 'userType', label: '用户类型', width: 120, align: 'center' },
  { prop: 'welfareType', label: '福利类型', width: 120, align: 'center' },
  { prop: 'showType', label: '展示方式', width: 120, align: 'center' },
  { prop: 'startTime', label: '开始时间', width: 180, align: 'center' },
  { prop: 'endTime', label: '结束时间', width: 180, align: 'center' },
  { prop: 'seq', label: '排序', width: 80, align: 'center' },
  { prop: 'createTime', label: '创建时间', width: 180, align: 'center' },
  { prop: 'actions', label: '操作', width: 200, align: 'center', fixed: 'right' },
]

// 分页配置
export const PAGINATION_CONFIG = {
  page: 1,
  size: 20,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
}

// 表单验证规则
export const ACTIVITY_FORM_RULES = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 2, max: 50, message: '活动名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' },
  ],
  remark: [
    { max: 200, message: '备注不能超过 200 个字符', trigger: 'blur' },
  ],
}

// 快速时间选择选项
export const QUICK_DATE_OPTIONS = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '明天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() + 1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setDate(end.getDate() + 1)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本周',
    value: () => {
      const start = new Date()
      const day = start.getDay()
      const diff = start.getDate() - day + (day === 0 ? -6 : 1)
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const start = new Date()
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setMonth(end.getMonth() + 1, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
]

// 默认表单数据
export const DEFAULT_ACTIVITY_FORM: Partial<ActivityFormData> = {
  name: '',
  banner: '',
  detailImg: '',
  description: '',
  startTime: '',
  endTime: '',
  userType: UserType.ALL,
  limitTimes: 0,
  welfareType: WelfareType.NONE,
  showType: ShowType.PUBLIC,
  status: ActivityStatus.ENABLED,
  seq: 0,
  activityCoupons: [],
  stores: [],
  remark: '',
}

// 图片上传配置
export const IMAGE_UPLOAD_CONFIG = {
  accept: '.jpg,.jpeg,.png,.gif',
  maxSize: 5 * 1024 * 1024, // 5MB
  maxCount: 1,
}

// 活动状态颜色映射
export const ACTIVITY_STATUS_COLORS = {
  [ActivityStatus.DISABLED]: '#f56c6c',
  [ActivityStatus.ENABLED]: '#67c23a',
}

// 工具函数
export const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

export const getActivityStatusText = (status: number): string => {
  return ACTIVITY_STATUS_MAP[status]?.label || '未知'
}

export const getActivityStatusType = (status: number): string => {
  return ACTIVITY_STATUS_MAP[status]?.type || 'info'
}

export const getUserTypeText = (userType: string): string => {
  return USER_TYPE_MAP[userType]?.label || '未知'
}

export const getWelfareTypeText = (welfareType: number): string => {
  return WELFARE_TYPE_MAP[welfareType]?.label || '未知'
}

export const getShowTypeText = (showType: number): string => {
  return SHOW_TYPE_MAP[showType]?.label || '未知'
}
