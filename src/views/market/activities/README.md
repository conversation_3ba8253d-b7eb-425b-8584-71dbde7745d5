# 活动管理模块

## 📋 模块概述

活动管理模块是营销系统的核心组成部分，提供完整的活动创建、编辑、查看和管理功能。基于API文档 `src/api/modules/activities/活动.md` 实现，确保与后端接口完全兼容。

## 🏗️ 项目结构

```
src/views/activities/
├── 📁 components/                    # 组件目录
│   ├── ActivitySearchForm.vue        # 搜索表单组件
│   ├── ActivityTable.vue             # 活动列表表格组件
│   ├── ActivityConfigDialog.vue      # 活动配置对话框组件
│   └── ActivityDetailDialog.vue      # 活动详情对话框组件
├── 📁 types/                         # 类型定义
│   └── index.ts                      # 活动相关类型定义
├── 📁 constants/                     # 常量定义
│   └── index.ts                      # 活动相关常量和工具函数
├── index.vue                         # 活动管理主页面
└── README.md                         # 本文档

src/api/modules/activities/
├── index.ts                          # 活动API接口
└── 活动.md                           # API文档

src/router/modules/
└── activities.ts                     # 活动路由配置
```

## 🎯 核心功能

### 1. **活动列表管理**
- ✅ 活动列表展示（表格形式）
- ✅ 多条件搜索和筛选
- ✅ 分页显示
- ✅ 批量操作（删除）
- ✅ 状态管理（启用/停用）

### 2. **活动配置**
- ✅ 新增活动
- ✅ 编辑活动
- ✅ 活动详情查看
- ✅ 图片上传（横幅、详情图）
- ✅ 优惠券配置
- ✅ 时间设置
- ✅ 用户类型设置

### 3. **数据管理**
- ✅ 完整的类型定义
- ✅ 表单验证
- ✅ 错误处理
- ✅ 加载状态管理

## 🔧 技术特性

### **前端技术栈**
- **Vue 3** - 组合式API
- **TypeScript** - 类型安全
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理（可选）

### **代码特性**
- 📝 **完整的TypeScript支持**
- 🎨 **响应式设计**
- 🔄 **组件化架构**
- 📱 **移动端适配**
- 🛡️ **错误边界处理**
- 🎯 **性能优化**

## 📊 数据结构

### **活动基础信息**
```typescript
interface Activity {
  id: string                    // 活动ID
  name: string                  // 活动名称
  banner: string                // 活动横幅
  detailImg: string             // 详情图片
  description: string           // 活动描述
  startTime: number             // 开始时间
  endTime: number               // 结束时间
  userType: string              // 用户类型
  limitTimes: number            // 限制次数
  welfareType: number           // 福利类型
  showType: number              // 展示方式
  status: number                // 活动状态
  seq: number                   // 排序
  activityCoupons: ActivityCoupon[]  // 关联优惠券
  // ... 其他字段
}
```

### **搜索参数**
```typescript
interface ActivityListParams {
  page?: number                 // 页码
  size?: number                 // 每页数量
  name?: string                 // 活动名称
  status?: number               // 活动状态
  startTimeBegin?: number       // 开始时间范围
  startTimeEnd?: number         // 结束时间范围
}
```

## 🎨 组件说明

### **1. ActivitySearchForm.vue**
**功能**: 活动搜索和筛选表单
**特性**:
- 基础搜索（名称、状态、时间）
- 高级搜索（用户类型、福利类型、展示方式）
- 实时搜索
- 导出功能

### **2. ActivityTable.vue**
**功能**: 活动列表表格展示
**特性**:
- 表格展示活动信息
- 状态标签显示
- 操作按钮（查看、编辑、删除、状态切换）
- 批量选择
- 图片预览

### **3. ActivityConfigDialog.vue**
**功能**: 活动配置对话框
**特性**:
- 支持新增、编辑、查看三种模式
- 表单验证
- 图片上传
- 优惠券配置
- 响应式布局

### **4. ActivityDetailDialog.vue**
**功能**: 活动详情展示对话框
**特性**:
- 完整的活动信息展示
- 图片预览
- 优惠券信息展示
- 时间状态显示

## 🚀 使用指南

### **1. 页面访问**
```
活动列表: /market/activities/list
新增活动: /market/activities/create
编辑活动: /market/activities/edit/:id
活动详情: /market/activities/detail/:id
```

### **2. 组件使用**
```vue
<!-- 在其他页面中使用活动配置对话框 -->
<ActivityConfigDialog
  v-model="showDialog"
  :activity-id="activityId"
  :page-mode="pageMode"
  @submit="handleSubmit"
  @success="handleSuccess"
/>
```

### **3. API调用**

```typescript
import {getActivityList, createActivity} from 'src/api/modules/activities/index'

// 获取活动列表
const response = await getActivityList({
    page: 1,
    size: 20,
    name: '活动名称'
})

// 创建活动
await createActivity({
    name: '新活动',
    startTime: Date.now(),
    endTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
    // ... 其他参数
})
```

## 🔄 开发模式

### **模拟数据**
开发阶段使用模拟数据，生产环境切换到真实API：

```typescript
// 开发环境
const response = await mockGetActivityList(params)

// 生产环境
// const response = await getActivityList(params)
```

### **切换方式**
1. 注释掉模拟API调用
2. 取消注释真实API调用
3. 确保后端接口可用

## 📋 待完善功能

### **短期计划**
- [ ] 活动统计图表
- [ ] 活动模板功能
- [ ] 批量导入活动
- [ ] 活动复制功能

### **长期计划**
- [ ] 活动效果分析
- [ ] A/B测试支持
- [ ] 智能推荐算法
- [ ] 多语言支持

## 🧪 测试建议

### **功能测试**
1. **基础功能**：创建、编辑、删除、查看活动
2. **搜索功能**：各种搜索条件组合测试
3. **表单验证**：必填字段、格式验证
4. **图片上传**：各种格式和大小测试
5. **状态管理**：启用/停用状态切换

### **兼容性测试**
1. **浏览器兼容**：Chrome、Firefox、Safari、Edge
2. **设备兼容**：桌面端、平板、手机
3. **分辨率适配**：各种屏幕尺寸

### **性能测试**
1. **大数据量**：测试大量活动数据的加载性能
2. **图片加载**：测试图片上传和预览性能
3. **内存使用**：长时间使用的内存泄漏检测

## 🔗 相关文档

- [API文档](../../../api/modules/market/activities/活动.md)
- [优惠券模块](../coupons/README.md)
- [路由配置](../../../router/modules/market/activities.ts)
- [类型定义](./types/index.ts)

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看控制台错误**：检查浏览器控制台的错误信息
2. **检查网络请求**：确认API请求是否正常
3. **验证数据格式**：确保传递的数据格式正确
4. **查看文档**：参考API文档和类型定义

---

**开发状态**: ✅ 已完成基础功能  
**维护状态**: 🔄 持续维护  
**版本**: v1.0.0
