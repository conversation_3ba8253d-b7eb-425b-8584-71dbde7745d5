<template>
  <div class="activity-table">
    <ElTable
      ref="tableRef"
      :data="data"
      :loading="loading"
      row-key="id"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <ElTableColumn type="selection" width="55" align="center" />

      <!-- 活动名称 -->
      <ElTableColumn prop="name" label="活动名称" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="activity-name">
            <ElImage
              v-if="row.banner"
              :src="row.banner"
              :preview-src-list="[row.banner]"
              class="activity-banner"
              fit="cover"
            />
            <span class="name-text">{{ row.name }}</span>
          </div>
        </template>
      </ElTableColumn>

      <!-- 活动状态 -->
      <ElTableColumn prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <ElTag :type="getActivityStatusType(row.status)">
            {{ getActivityStatusText(row.status) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 用户类型 -->
      <ElTableColumn prop="userType" label="用户类型" width="120" align="center">
        <template #default="{ row }">
          <ElTag type="primary" size="small">
            {{ getUserTypeText(row.userType) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 福利类型 -->
      <ElTableColumn prop="welfareType" label="福利类型" width="120" align="center">
        <template #default="{ row }">
          <ElTag :type="getWelfareTypeType(row.welfareType)" size="small">
            {{ getWelfareTypeText(row.welfareType) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 展示方式 -->
      <ElTableColumn prop="showType" label="展示方式" width="120" align="center">
        <template #default="{ row }">
          <ElTag :type="getShowTypeType(row.showType)" size="small">
            {{ getShowTypeText(row.showType) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 限制次数 -->
      <ElTableColumn prop="limitTimes" label="限制次数" width="100" align="center">
        <template #default="{ row }">
          {{ row.limitTimes === 0 ? '不限制' : `${row.limitTimes}次` }}
        </template>
      </ElTableColumn>

      <!-- 开始时间 -->
      <ElTableColumn prop="startTime" label="开始时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.startTime) }}
        </template>
      </ElTableColumn>

      <!-- 结束时间 -->
      <ElTableColumn prop="endTime" label="结束时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.endTime) }}
        </template>
      </ElTableColumn>

      <!-- 排序 -->
      <ElTableColumn prop="seq" label="排序" width="80" align="center" />

      <!-- 创建时间 -->
      <ElTableColumn prop="createTime" label="创建时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.createTime) }}
        </template>
      </ElTableColumn>

      <!-- 操作列 -->
      <ElTableColumn label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <ElButton type="primary" size="small" text @click="handleView(row)">
              查看
            </ElButton>
            <ElButton type="warning" size="small" text @click="handleEdit(row)">
              编辑
            </ElButton>
            <ElButton
              :type="row.status === 1 ? 'danger' : 'success'"
              size="small"
              text
              @click="handleStatusChange(row)"
            >
              {{ row.status === 1 ? '停用' : '启用' }}
            </ElButton>
            <ElButton type="danger" size="small" text @click="handleDelete(row)">
              删除
            </ElButton>
          </div>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ElTable } from 'element-plus'

// 类型导入
import type { Activity } from '../types'

// 常量导入
import {
  formatTimestamp,
  getActivityStatusText,
  getActivityStatusType,
  getUserTypeText,
  getWelfareTypeText,
  getShowTypeText,
  WELFARE_TYPE_MAP,
  SHOW_TYPE_MAP,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  data: Activity[]
  loading?: boolean
  selectedIds?: string[]
}

interface Emits {
  (e: 'selection-change', ids: string[]): void
  (e: 'edit', activity: Activity): void
  (e: 'delete', activity: Activity): void
  (e: 'view', activity: Activity): void
  (e: 'status-change', activity: Activity, status: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedIds: () => [],
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>()

// ==================== 方法定义 ====================

/**
 * 选择变更
 */
function handleSelectionChange(selection: Activity[]) {
  const ids = selection.map(item => item.id)
  emit('selection-change', ids)
}

/**
 * 查看
 */
function handleView(activity: Activity) {
  emit('view', activity)
}

/**
 * 编辑
 */
function handleEdit(activity: Activity) {
  emit('edit', activity)
}

/**
 * 删除
 */
function handleDelete(activity: Activity) {
  emit('delete', activity)
}

/**
 * 状态变更
 */
function handleStatusChange(activity: Activity) {
  const newStatus = activity.status === 1 ? 0 : 1
  emit('status-change', activity, newStatus)
}

/**
 * 获取福利类型标签类型
 */
function getWelfareTypeType(welfareType: number): string {
  return WELFARE_TYPE_MAP[welfareType]?.type || 'info'
}

/**
 * 获取展示方式标签类型
 */
function getShowTypeType(showType: number): string {
  return SHOW_TYPE_MAP[showType]?.type || 'info'
}

// ==================== 暴露方法 ====================

defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: Activity, selected?: boolean) => 
    tableRef.value?.toggleRowSelection(row, selected),
})
</script>

<style scoped lang="scss">
.activity-table {
  background: var(--el-fill-color-blank);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.activity-name {
  display: flex;
  align-items: center;
  gap: 8px;

  .activity-banner {
    width: 40px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;
  }

  .name-text {
    flex: 1;
    font-weight: 500;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 表格样式优化 */
:deep(.el-table) {
  .el-table__header {
    background: var(--el-fill-color-light);
  }

  .el-table__row {
    &:hover {
      background: var(--el-fill-color-lighter);
    }
  }

  .el-table__cell {
    padding: 12px 8px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>
