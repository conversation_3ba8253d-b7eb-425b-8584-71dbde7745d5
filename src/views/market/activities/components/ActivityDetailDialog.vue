<script setup lang="ts">
// 类型导入
import type { Activity } from '../types'
import { ElMessage } from 'element-plus'

import { computed, ref, watch } from 'vue'
import { getActivityDetail } from '@/api/modules/market/activities'
// API 导入

// 常量导入
import {
  formatTimestamp,
  getActivityStatusText,
  getActivityStatusType,
  getShowTypeText,
  getUserTypeText,
  getWelfareTypeText,
  SHOW_TYPE_MAP,
  WELFARE_TYPE_MAP,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  activityId?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 加载状态
const loading = ref(false)

// 活动数据
const activityData = ref<Activity | null>(null)

// ==================== 监听器 ====================

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.activityId) {
    fetchActivityDetail()
  }
})

// ==================== 方法定义 ====================

/**
 * 获取活动详情
 */
async function fetchActivityDetail() {
  if (!props.activityId) { return }

  try {
    loading.value = true

    // 使用模拟数据（开发阶段）
    // const response = await mockGetActivityDetail(props.activityId)

    // 生产环境使用真实API
    const response = await getActivityDetail({ activityId: props.activityId })

    activityData.value = response
  }
  catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败')
    activityData.value = null
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取福利类型标签类型
 */
function getWelfareTypeType(welfareType: number): string {
  return WELFARE_TYPE_MAP[welfareType]?.type || 'info'
}

/**
 * 获取展示方式标签类型
 */
function getShowTypeType(showType: number): string {
  return SHOW_TYPE_MAP[showType]?.type || 'info'
}

/**
 * 获取时间状态文本
 */
function getTimeStatusText(activity: Activity): string {
  const now = Date.now()
  if (now < activity.startTime) {
    return '未开始'
  }
  else if (now > activity.endTime) {
    return '已结束'
  }
  else {
    return '进行中'
  }
}

/**
 * 获取时间状态类型
 */
function getTimeStatusType(activity: Activity): string {
  const now = Date.now()
  if (now < activity.startTime) {
    return 'info'
  }
  else if (now > activity.endTime) {
    return 'danger'
  }
  else {
    return 'success'
  }
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="活动详情"
    :width="800"
    destroy-on-close
  >
    <div v-loading="loading" class="activity-detail">
      <div v-if="activityData" class="detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            基础信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>活动名称：</label>
              <span>{{ activityData.name }}</span>
            </div>
            <div class="info-item">
              <label>活动状态：</label>
              <ElTag :type="getActivityStatusType(activityData.status)">
                {{ getActivityStatusText(activityData.status) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>用户类型：</label>
              <ElTag type="primary" size="small">
                {{ getUserTypeText(activityData.userType) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>福利类型：</label>
              <ElTag :type="getWelfareTypeType(activityData.welfareType)" size="small">
                {{ getWelfareTypeText(activityData.welfareType) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>展示方式：</label>
              <ElTag :type="getShowTypeType(activityData.showType)" size="small">
                {{ getShowTypeText(activityData.showType) }}
              </ElTag>
            </div>
            <div class="info-item">
              <label>限制次数：</label>
              <span>{{ activityData.limitTimes === 0 ? '不限制' : `${activityData.limitTimes}次` }}</span>
            </div>
            <div class="info-item">
              <label>排序：</label>
              <span>{{ activityData.seq }}</span>
            </div>
          </div>
        </div>

        <!-- 活动描述 -->
        <div v-if="activityData.description" class="detail-section">
          <h3 class="section-title">
            活动描述
          </h3>
          <div class="description-content">
            {{ activityData.description }}
          </div>
        </div>

        <!-- 活动时间 -->
        <div class="detail-section">
          <h3 class="section-title">
            活动时间
          </h3>
          <div class="time-info">
            <div class="time-item">
              <label>开始时间：</label>
              <span>{{ formatTimestamp(activityData.startTime) }}</span>
            </div>
            <div class="time-item">
              <label>结束时间：</label>
              <span>{{ formatTimestamp(activityData.endTime) }}</span>
            </div>
            <div class="time-item">
              <label>活动状态：</label>
              <ElTag :type="getTimeStatusType(activityData)">
                {{ getTimeStatusText(activityData) }}
              </ElTag>
            </div>
          </div>
        </div>

        <!-- 活动图片 -->
        <div class="detail-section">
          <h3 class="section-title">
            活动图片
          </h3>
          <div class="image-gallery">
            <div v-if="activityData.banner" class="image-item">
              <label>活动横幅：</label>
              <ElImage
                :src="activityData.banner"
                :preview-src-list="[activityData.banner]"
                class="activity-image"
                fit="cover"
              />
            </div>
            <div v-if="activityData.detailImg" class="image-item">
              <label>详情图片：</label>
              <ElImage
                :src="activityData.detailImg"
                :preview-src-list="[activityData.detailImg]"
                class="activity-image"
                fit="cover"
              />
            </div>
          </div>
        </div>

        <!-- 优惠券信息 -->
        <div v-if="activityData.activityCoupons && activityData.activityCoupons.length" class="detail-section">
          <h3 class="section-title">
            优惠券信息
          </h3>
          <ElTable :data="activityData.activityCoupons" border>
            <ElTableColumn prop="couponName" label="优惠券名称" />
            <ElTableColumn prop="couponNum" label="数量" width="100" align="center" />
          </ElTable>
        </div>

        <!-- 创建信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            创建信息
          </h3>
          <div class="create-info">
            <div class="create-item">
              <label>创建时间：</label>
              <span>{{ formatTimestamp(activityData.createTime) }}</span>
            </div>
            <div class="create-item">
              <label>更新时间：</label>
              <span>{{ formatTimestamp(activityData.updateTime) }}</span>
            </div>
            <div class="create-item">
              <label>版本号：</label>
              <span>{{ activityData.version }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <ElEmpty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">
          关闭
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
.activity-detail {
  .detail-content {
    .detail-section {
      margin-bottom: 24px;
      padding: 20px;
      background: var(--el-fill-color-blank);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        label {
          min-width: 80px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        span {
          color: var(--el-text-color-primary);
        }
      }
    }

    .description-content {
      padding: 16px;
      background: var(--el-fill-color-lighter);
      border-radius: 6px;
      line-height: 1.6;
      color: var(--el-text-color-primary);
    }

    .time-info {
      .time-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          min-width: 80px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        span {
          color: var(--el-text-color-primary);
        }
      }
    }

    .image-gallery {
      .image-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        .activity-image {
          max-width: 300px;
          max-height: 200px;
          border-radius: 6px;
          cursor: pointer;
        }
      }
    }

    .create-info {
      .create-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          min-width: 80px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        span {
          color: var(--el-text-color-primary);
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.dialog-footer {
  text-align: center;
}
</style>
