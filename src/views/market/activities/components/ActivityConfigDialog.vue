<script setup lang="ts">
import type { FormInstance, UploadUserFile } from 'element-plus'
// 类型导入
import type { ActivityFormData, CreateActivityParams, UpdateActivityParams } from '../types'
import type { Coupon } from '@/views/market/coupons/baseConfig/types'
import { Plus } from '@element-plus/icons-vue'

import { ElMessage } from 'element-plus'

import { computed, nextTick, reactive, ref, watch } from 'vue'
// API 导入
import { createActivity, mockGetActivityDetail, updateActivity } from '@/api/modules/market/activities'

// 组件导入
import CouponSelector from '@/components/CouponSelector/index.vue'

// 常量导入
import {
  ACTIVITY_FORM_RULES,
  DEFAULT_ACTIVITY_FORM,
  IMAGE_UPLOAD_CONFIG,
  SHOW_TYPE_OPTIONS,
  USER_TYPE_OPTIONS,
  WELFARE_TYPE_OPTIONS,
} from '../constants'

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  activityId?: string
  pageMode?: 'add' | 'edit' | 'view'
  title?: string
  width?: string | number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: CreateActivityParams | UpdateActivityParams): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
  title: '',
  width: 800,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 对话框标题
const dialogTitle = computed(() => {
  if (props.title) { return props.title }

  const titleMap = {
    add: '新增活动',
    edit: '编辑活动',
    view: '查看活动',
  }
  return titleMap[props.pageMode]
})

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive<ActivityFormData>({ ...DEFAULT_ACTIVITY_FORM })

// 文件列表
const bannerFileList = ref<UploadUserFile[]>([])
const detailFileList = ref<UploadUserFile[]>([])

// 上传地址
const uploadAction = ref('/api/upload/image')

// 优惠券选择器相关
const showCouponSelector = ref(false)
const selectedCoupons = ref<Coupon[]>([])

// 计算已选择的优惠券ID，用于排除
const selectedCouponIds = computed(() =>
  formData.activityCoupons.map(item => item.couponId?.toString() || ''),
)

// ==================== 监听器 ====================

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      if (props.pageMode === 'edit' || props.pageMode === 'view') {
        fetchActivityDetail()
      }
      else {
        resetForm()
      }
    })
  }
})

// ==================== 方法定义 ====================

/**
 * 获取活动详情
 */
async function fetchActivityDetail() {
  if (!props.activityId) { return }

  try {
    loading.value = true

    // 使用模拟数据（开发阶段）
    const response = await mockGetActivityDetail(props.activityId)

    // 生产环境使用真实API
    // const response = await getActivityDetail({ activityId: props.activityId })

    if (response) {
      // 填充表单数据
      Object.assign(formData, {
        ...response,
        startTime: response.startTime ? new Date(response.startTime).toISOString().slice(0, 19).replace('T', ' ') : '',
        endTime: response.endTime ? new Date(response.endTime).toISOString().slice(0, 19).replace('T', ' ') : '',
      })

      // 设置文件列表
      if (response.banner) {
        bannerFileList.value = [{
          name: 'banner.jpg',
          url: response.banner,
        }]
      }

      if (response.detailImg) {
        detailFileList.value = [{
          name: 'detail.jpg',
          url: response.detailImg,
        }]
      }
    }
  }
  catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
function resetForm() {
  Object.assign(formData, { ...DEFAULT_ACTIVITY_FORM })
  bannerFileList.value = []
  detailFileList.value = []
  selectedCoupons.value = []
  formRef.value?.clearValidate()
}

/**
 * 提交表单
 */
async function handleSubmit() {
  if (!formRef.value) { return }

  try {
    await formRef.value.validate()

    loading.value = true

    // 构建提交数据
    const submitData = {
      ...formData,
      startTime: formData.startTime ? new Date(formData.startTime).getTime() : 0,
      endTime: formData.endTime ? new Date(formData.endTime).getTime() : 0,
      banner: bannerFileList.value[0]?.url || '',
      detailImg: detailFileList.value[0]?.url || '',
    }

    let response
    if (props.pageMode === 'add') {
      response = await createActivity(submitData as CreateActivityParams)
    }
    else {
      response = await updateActivity(submitData as UpdateActivityParams)
    }

    ElMessage.success(props.pageMode === 'add' ? '创建成功' : '保存成功')
    emit('submit', submitData)
    emit('success', response)
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 取消
 */
function handleCancel() {
  emit('cancel')
  dialogVisible.value = false
}

/**
 * 关闭对话框
 */
function handleClose() {
  resetForm()
}

/**
 * 横幅上传前检查
 */
function beforeBannerUpload(file: File) {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].includes(file.type)
  const isValidSize = file.size <= IMAGE_UPLOAD_CONFIG.maxSize

  if (!isValidType) {
    ElMessage.error('横幅图片只能是 JPG、PNG、GIF 格式!')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('横幅图片大小不能超过 5MB!')
    return false
  }
  return true
}

/**
 * 详情图上传前检查
 */
function beforeDetailUpload(file: File) {
  return beforeBannerUpload(file)
}

/**
 * 横幅上传成功
 */
function handleBannerSuccess(response: any, file: UploadUserFile) {
  formData.banner = response.data?.url || file.url || ''
}

/**
 * 详情图上传成功
 */
function handleDetailSuccess(response: any, file: UploadUserFile) {
  formData.detailImg = response.data?.url || file.url || ''
}

/**
 * 上传失败
 */
function handleUploadError(error: Error) {
  console.error('上传失败:', error)
  ElMessage.error('上传失败')
}

/**
 * 打开优惠券选择器
 */
function addCoupon() {
  showCouponSelector.value = true
}

/**
 * 确认选择优惠券
 */
function handleCouponConfirm(coupons: Coupon[]) {
  // 将选择的优惠券添加到活动优惠券列表中
  const newCoupons = coupons.map(coupon => ({
    couponId: Number.parseInt(coupon.id),
    couponName: coupon.couponName,
    couponNum: 1, // 默认数量为1
    faceValue: coupon.faceValue || 0,
    couponType: coupon.couponType,
    useOn: coupon.useOn,
    validStartTime: coupon.validStartTime,
    validEndTime: coupon.validEndTime,
  }))

  // 过滤掉已存在的优惠券，避免重复添加
  const existingIds = formData.activityCoupons.map(item => item.couponId)
  const filteredNewCoupons = newCoupons.filter(coupon =>
    !existingIds.includes(coupon.couponId),
  )

  // 添加到活动优惠券列表
  formData.activityCoupons.push(...filteredNewCoupons)

  ElMessage.success(`成功添加 ${filteredNewCoupons.length} 个优惠券`)
}

/**
 * 取消选择优惠券
 */
function handleCouponCancel() {
  console.log('取消选择优惠券')
}

/**
 * 删除优惠券
 */
function removeCoupon(index: number) {
  const removedCoupon = formData.activityCoupons[index]
  formData.activityCoupons.splice(index, 1)
  ElMessage.success(`已删除优惠券: ${removedCoupon.couponName}`)
}

/**
 * 更新优惠券数量
 */
function updateCouponNum(index: number, num: number) {
  if (num < 1) {
    ElMessage.warning('优惠券数量不能小于1')
    formData.activityCoupons[index].couponNum = 1
    return
  }
  if (num > 999) {
    ElMessage.warning('优惠券数量不能大于999')
    formData.activityCoupons[index].couponNum = 999
    return
  }
  formData.activityCoupons[index].couponNum = num
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="800"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    @close="handleClose"
  >
    <div class="activity-config-dialog">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="ACTIVITY_FORM_RULES"
        label-width="120px"
        class="config-form"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-title">
            <h3>基础信息</h3>
            <p>设置活动的基本信息</p>
          </div>

          <ElFormItem label="活动名称" prop="name">
            <ElInput
              v-model="formData.name"
              placeholder="请输入活动名称"
              :disabled="pageMode === 'view'"
              maxlength="50"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="活动描述" prop="description">
            <ElInput
              v-model="formData.description"
              type="textarea"
              placeholder="请输入活动描述"
              :disabled="pageMode === 'view'"
              :rows="3"
              maxlength="500"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="活动横幅" prop="banner">
            <div class="upload-section">
              <ElUpload
                v-model:file-list="bannerFileList"
                :action="uploadAction"
                :disabled="pageMode === 'view'"
                :limit="1"
                :accept="IMAGE_UPLOAD_CONFIG.accept"
                :before-upload="beforeBannerUpload"
                :on-success="handleBannerSuccess"
                :on-error="handleUploadError"
                list-type="picture-card"
              >
                <ElIcon><Plus /></ElIcon>
              </ElUpload>
              <div class="upload-tip">
                支持 JPG、PNG 格式，建议尺寸 750x300，大小不超过 5MB
              </div>
            </div>
          </ElFormItem>

          <ElFormItem label="详情图片" prop="detailImg">
            <div class="upload-section">
              <ElUpload
                v-model:file-list="detailFileList"
                :action="uploadAction"
                :disabled="pageMode === 'view'"
                :limit="1"
                :accept="IMAGE_UPLOAD_CONFIG.accept"
                :before-upload="beforeDetailUpload"
                :on-success="handleDetailSuccess"
                :on-error="handleUploadError"
                list-type="picture-card"
              >
                <ElIcon><Plus /></ElIcon>
              </ElUpload>
              <div class="upload-tip">
                支持 JPG、PNG 格式，建议尺寸 750x1334，大小不超过 5MB
              </div>
            </div>
          </ElFormItem>
        </div>

        <!-- 活动设置 -->
        <div class="form-section">
          <div class="section-title">
            <h3>活动设置</h3>
            <p>设置活动的时间、用户类型等</p>
          </div>

          <div class="form-grid">
            <ElFormItem label="开始时间" prop="startTime">
              <ElDatePicker
                v-model="formData.startTime"
                type="datetime"
                placeholder="请选择开始时间"
                :disabled="pageMode === 'view'"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </ElFormItem>

            <ElFormItem label="结束时间" prop="endTime">
              <ElDatePicker
                v-model="formData.endTime"
                type="datetime"
                placeholder="请选择结束时间"
                :disabled="pageMode === 'view'"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </ElFormItem>
          </div>

          <div class="form-grid">
            <ElFormItem label="用户类型" prop="userType">
              <ElSelect
                v-model="formData.userType"
                placeholder="请选择用户类型"
                :disabled="pageMode === 'view'"
                style="width: 100%"
              >
                <ElOption
                  v-for="option in USER_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="限制次数" prop="limitTimes">
              <ElInputNumber
                v-model="formData.limitTimes"
                :min="0"
                :max="999"
                :disabled="pageMode === 'view'"
                placeholder="0表示不限制"
                style="width: 100%"
              />
            </ElFormItem>
          </div>

          <div class="form-grid">
            <ElFormItem label="福利类型" prop="welfareType">
              <ElSelect
                v-model="formData.welfareType"
                placeholder="请选择福利类型"
                :disabled="pageMode === 'view'"
                style="width: 100%"
              >
                <ElOption
                  v-for="option in WELFARE_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="展示方式" prop="showType">
              <ElSelect
                v-model="formData.showType"
                placeholder="请选择展示方式"
                :disabled="pageMode === 'view'"
                style="width: 100%"
              >
                <ElOption
                  v-for="option in SHOW_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
          </div>

          <div class="form-grid">
            <ElFormItem label="活动状态" prop="status">
              <ElRadioGroup
                v-model="formData.status"
                :disabled="pageMode === 'view'"
              >
                <ElRadio :label="1">
                  启用
                </ElRadio>
                <ElRadio :label="0">
                  停用
                </ElRadio>
              </ElRadioGroup>
            </ElFormItem>

            <ElFormItem label="排序" prop="seq">
              <ElInputNumber
                v-model="formData.seq"
                :min="0"
                :max="9999"
                :disabled="pageMode === 'view'"
                placeholder="数字越小排序越靠前"
                style="width: 100%"
              />
            </ElFormItem>
          </div>
        </div>

        <!-- 优惠券配置 -->
        <div v-if="formData.welfareType === 1" class="form-section">
          <div class="section-title">
            <h3>优惠券配置</h3>
            <p>设置活动赠送的优惠券</p>
          </div>

          <div class="coupon-config">
            <div class="coupon-actions">
              <ElButton
                v-if="pageMode !== 'view'"
                type="primary"
                size="small"
                :icon="Plus"
                @click="addCoupon"
              >
                添加优惠券
              </ElButton>

              <div v-if="formData.activityCoupons.length > 0" class="coupon-summary">
                <ElTag type="info" size="small">
                  已选择 {{ formData.activityCoupons.length }} 个优惠券
                </ElTag>
                <ElTag type="success" size="small">
                  总数量: {{ formData.activityCoupons.reduce((sum, item) => sum + item.couponNum, 0) }}
                </ElTag>
              </div>
            </div>

            <ElTable
              :data="formData.activityCoupons"
              style="width: 100%; margin-top: 16px"
              border
              empty-text="暂未添加优惠券"
            >
              <ElTableColumn prop="couponName" label="优惠券名称" min-width="150">
                <template #default="{ row }">
                  <div class="coupon-info">
                    <div class="coupon-name">
                      {{ row.couponName }}
                    </div>
                    <div class="coupon-meta">
                      <ElTag size="small" type="info">
                        {{ row.couponType === 0 ? '满减券'
                          : row.couponType === 1 ? '减至券'
                            : row.couponType === 2 ? '通兑券'
                              : row.couponType === 3 ? '折扣券' : '多对一券' }}
                      </ElTag>
                      <span class="face-value">面值: ¥{{ row.faceValue }}</span>
                    </div>
                  </div>
                </template>
              </ElTableColumn>

              <ElTableColumn prop="couponNum" label="发放数量" width="120">
                <template #default="{ row, $index }">
                  <ElInputNumber
                    v-if="pageMode !== 'view'"
                    :model-value="row.couponNum"
                    :min="1"
                    :max="999"
                    size="small"
                    controls-position="right"
                    @change="(val) => updateCouponNum($index, val)"
                  />
                  <span v-else>{{ row.couponNum }}</span>
                </template>
              </ElTableColumn>

              <ElTableColumn label="有效期" width="200">
                <template #default="{ row }">
                  <div class="validity-period">
                    <div>{{ row.validStartTime }}</div>
                    <div>{{ row.validEndTime }}</div>
                  </div>
                </template>
              </ElTableColumn>

              <ElTableColumn
                v-if="pageMode !== 'view'"
                label="操作"
                width="80"
                fixed="right"
              >
                <template #default="{ $index }">
                  <ElButton
                    type="danger"
                    size="small"
                    text
                    @click="removeCoupon($index)"
                  >
                    删除
                  </ElButton>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <div class="section-title">
            <h3>备注信息</h3>
            <p>可选的备注信息</p>
          </div>

          <ElFormItem label="备注" prop="remark">
            <ElInput
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入备注信息"
              :disabled="pageMode === 'view'"
              :rows="3"
              maxlength="200"
              show-word-limit
            />
          </ElFormItem>
        </div>
      </ElForm>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          {{ pageMode === 'view' ? '关闭' : '取消' }}
        </ElButton>
        <ElButton
          v-if="pageMode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ pageMode === 'add' ? '创建活动' : '保存修改' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>

  <!-- 优惠券选择器 -->
  <CouponSelector
    v-model="showCouponSelector"
    :multiple="true"
    :max-select="20"
    :exclude-ids="selectedCouponIds"
    :status="1"
    @confirm="handleCouponConfirm"
    @cancel="handleCouponCancel"
  />
</template>

<style scoped lang="scss">
.activity-config-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.config-form {
  .form-section {
    margin-bottom: 32px;
    padding: 24px;
    background: var(--el-fill-color-blank);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    p {
      margin: 0;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
}

.upload-section {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* 上传组件样式优化 */
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

.coupon-config {
  .coupon-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .coupon-summary {
      display: flex;
      gap: 8px;
    }
  }

  .coupon-info {
    .coupon-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .coupon-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: var(--el-text-color-regular);

      .face-value {
        color: var(--el-color-danger);
        font-weight: 500;
      }
    }
  }

  .validity-period {
    font-size: 12px;
    line-height: 1.4;
    color: var(--el-text-color-regular);
  }
}

/* 优化表格样式 */
:deep(.el-table) {
  .el-table__empty-text {
    color: var(--el-text-color-placeholder);
  }
}

/* 输入数字组件样式优化 */
:deep(.el-input-number) {
  width: 100%;

  .el-input__inner {
    text-align: center;
  }
}
</style>
