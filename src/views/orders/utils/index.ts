/**
 * 订单管理工具函数
 * 提供订单相关的格式化、验证和转换功能
 */

import type { AmountFormatOptions, OrderStatus, TimeFormatOptions } from '../types'
import { OrderSourceMap, OrderStatusMap, PaymentMethodMap } from '../types'

// ==================== 金额格式化 ====================

/**
 * 格式化金额
 * @param amount 金额（分）
 * @param options 格式化选项
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number, options: AmountFormatOptions = {}): string {
  const {
    unit = 'yuan',
    precision = 2,
    showSymbol = true,
  } = options

  let value = amount
  if (unit === 'yuan') {
    value = amount / 100
  }

  const formatted = value.toFixed(precision)
  return showSymbol ? `¥${formatted}` : formatted
}

/**
 * 解析金额字符串为分
 * @param amountStr 金额字符串
 * @returns 金额（分）
 */
export function parseAmount(amountStr: string): number {
  const cleaned = amountStr.replace(/[¥,\s]/g, '')
  const amount = Number.parseFloat(cleaned)
  return Math.round(amount * 100)
}

// ==================== 时间格式化 ====================

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export function formatTime(timestamp: number, options: TimeFormatOptions = {}): string {
  if (!timestamp) { return '-' }

  const {
    showTime = true,
  } = options

  const date = new Date(timestamp)

  if (!showTime) {
    return date.toLocaleDateString('zh-CN')
  }

  return date.toLocaleString('zh-CN')
}

/**
 * 格式化相对时间
 * @param timestamp 时间戳
 * @returns 相对时间字符串
 */
export function formatRelativeTime(timestamp: number): string {
  if (!timestamp) { return '-' }

  const now = Date.now()
  const diff = now - timestamp
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天前`
  }
  else if (hours > 0) {
    return `${hours}小时前`
  }
  else if (minutes > 0) {
    return `${minutes}分钟前`
  }
  else {
    return '刚刚'
  }
}

// ==================== 状态处理 ====================

/**
 * 获取订单状态标签类型
 * @param status 订单状态
 * @returns Element Plus 标签类型
 */
export function getOrderStatusTagType(status: OrderStatus): string {
  const typeMap: Record<OrderStatus, string> = {
    0: 'warning', // 待付款
    1: 'success', // 已付款
    2: 'primary', // 已发货
    3: 'info', // 已收货
    4: 'success', // 已完成
    5: 'danger', // 已取消
    6: 'warning', // 退款中
    7: 'info', // 已退款
  }
  return typeMap[status] || 'info'
}

/**
 * 获取订单状态描述
 * @param status 订单状态
 * @returns 状态描述
 */
export function getOrderStatusDesc(status: OrderStatus): string {
  return OrderStatusMap[status] || '未知状态'
}

/**
 * 判断订单是否可以发货
 * @param status 订单状态
 * @returns 是否可以发货
 */
export function canShipOrder(status: OrderStatus): boolean {
  return status === 1 // 已付款状态可以发货
}

/**
 * 判断订单是否可以取消
 * @param status 订单状态
 * @returns 是否可以取消
 */
export function canCancelOrder(status: OrderStatus): boolean {
  return status === 0 || status === 1 // 待付款或已付款状态可以取消
}

// ==================== 数据验证 ====================

/**
 * 验证订单编号格式
 * @param orderNo 订单编号
 * @returns 是否有效
 */
export function validateOrderNo(orderNo: string): boolean {
  if (!orderNo) { return false }
  // 订单编号通常是数字和字母的组合，长度在10-30位
  return /^[A-Z0-9]{10,30}$/i.test(orderNo)
}

/**
 * 验证手机号格式
 * @param phone 手机号
 * @returns 是否有效
 */
export function validatePhone(phone: string): boolean {
  if (!phone) { return false }
  return /^1[3-9]\d{9}$/.test(phone)
}

/**
 * 验证运单号格式
 * @param trackingNumber 运单号
 * @returns 是否有效
 */
export function validateTrackingNumber(trackingNumber: string): boolean {
  if (!trackingNumber) { return false }
  // 运单号通常是字母和数字的组合，长度在5-30位
  return /^[A-Z0-9]{5,30}$/i.test(trackingNumber)
}

// ==================== 数据转换 ====================

/**
 * 转换搜索参数
 * @param params 原始参数
 * @returns 转换后的参数
 */
export function transformSearchParams(params: any): any {
  const transformed = { ...params }

  // 处理时间范围
  if (params.dateRange && Array.isArray(params.dateRange) && params.dateRange.length === 2) {
    transformed.startTime = new Date(params.dateRange[0]).getTime()
    transformed.endTime = new Date(params.dateRange[1]).getTime()
    delete transformed.dateRange
  }

  // 清理空值
  Object.keys(transformed).forEach((key) => {
    if (transformed[key] === '' || transformed[key] === null || transformed[key] === undefined) {
      delete transformed[key]
    }
  })

  return transformed
}

/**
 * 计算订单统计信息
 * @param orders 订单列表
 * @returns 统计信息
 */
export function calculateOrderStats(orders: any[]) {
  const stats = {
    totalCount: orders.length,
    totalAmount: 0,
    statusCounts: {} as Record<string, number>,
    avgAmount: 0,
  }

  orders.forEach((order) => {
    stats.totalAmount += order.payAmount || 0
    const statusKey = order.status.toString()
    stats.statusCounts[statusKey] = (stats.statusCounts[statusKey] || 0) + 1
  })

  stats.avgAmount = stats.totalCount > 0 ? stats.totalAmount / stats.totalCount : 0

  return stats
}

// ==================== 导出工具 ====================

/**
 * 导出订单数据为CSV格式
 * @param orders 订单列表
 * @returns CSV字符串
 */
export function exportOrdersToCSV(orders: any[]): string {
  const headers = [
    '订单编号',
    '用户ID',
    '订单状态',
    '实付金额',
    '支付方式',
    '订单来源',
    '下单时间',
    '支付时间',
  ]

  const rows = orders.map(order => [
    order.id,
    order.userId,
    getOrderStatusDesc(order.status),
    formatAmount(order.payAmount, { showSymbol: false }),
    PaymentMethodMap[order.paymentMethod as keyof typeof PaymentMethodMap] || '-',
    OrderSourceMap[order.source as keyof typeof OrderSourceMap] || '-',
    formatTime(order.createAt),
    formatTime(order.paymentTime),
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')

  return csvContent
}

/**
 * 下载CSV文件
 * @param csvContent CSV内容
 * @param filename 文件名
 */
export function downloadCSV(csvContent: string, filename: string = 'orders.csv'): void {
  const blob = new Blob([`\uFEFF${csvContent}`], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
