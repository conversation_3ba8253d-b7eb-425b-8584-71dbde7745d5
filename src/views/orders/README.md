# 订单管理模块

## 概述

订单管理模块是一个完整的订单处理系统，提供订单列表查看、详情展示、发货处理等功能。基于Vue 3 + TypeScript + Element Plus构建，具有良好的类型安全性和用户体验。

## 功能特性

### 核心功能
- ✅ 订单列表展示（分页、搜索、筛选）
- ✅ 订单详情查看
- ✅ 订单发货处理
- ✅ 订单状态管理
- ✅ 多条件搜索（订单号、时间范围、状态、手机号）
- ✅ 响应式设计，支持移动端

### 技术特性
- ✅ TypeScript 类型安全
- ✅ 组件化架构
- ✅ API 统一管理
- ✅ 错误处理机制
- ✅ 加载状态管理
- ✅ 表单验证

## 目录结构

```
src/views/orders/
├── components/              # 组件目录
│   ├── OrderSearchForm.vue  # 搜索表单组件
│   ├── OrderTable.vue       # 订单表格组件
│   ├── OrderDetailDialog.vue # 订单详情对话框
│   ├── OrderDetailCard.vue  # 订单详情卡片
│   └── ShipOrderDialog.vue  # 发货对话框组件
├── detail/                  # 详情页面
│   └── [id].vue            # 订单详情页面
├── types/                   # 类型定义
│   └── index.ts            # 订单相关类型
├── utils/                   # 工具函数
│   └── index.ts            # 订单工具函数
├── index.vue               # 订单列表主页面
├── orderapi.md            # API接口文档
└── README.md              # 模块文档
```

### API服务
```
src/api/modules/orders/
└── index.ts               # 订单API接口
```

### 路由配置
```
src/router/modules/orders.ts  # 订单管理路由配置
```

## 核心功能实现

### 1. 类型系统
- 完整的TypeScript类型定义
- 基于接口文档的准确类型映射
- 支持枚举类型和联合类型
- 提供类型守卫和工具类型

### 2. API服务
- 基于Axios的HTTP客户端
- 统一的错误处理机制
- 支持请求/响应拦截器
- 类型安全的API调用

### 3. 状态管理
- 使用Vue 3 Composition API
- 响应式数据管理
- 计算属性和监听器
- 生命周期钩子集成

### 4. 组件设计
- 高度可复用的组件架构
- Props/Emits类型定义
- 插槽和作用域插槽支持
- 组件间通信机制

### 5. 表单处理
- Element Plus表单组件
- 表单验证规则
- 动态表单字段
- 搜索和筛选功能

### 6. 数据展示
- 表格组件封装
- 分页功能实现
- 搜索和筛选
- 排序功能

## 接口对接

### 订单列表接口
- **接口地址**: `POST /adm_order/list`
- **权限要求**: `/store/order/query`
- **支持参数**: 分页、订单号、时间范围、状态、手机号

### 订单详情接口
- **接口地址**: `POST /adm_order/detail/{id}`
- **权限要求**: `/store/order/detail`
- **返回数据**: 完整的订单信息

### 订单发货接口
- **接口地址**: `POST /adm_order/ship`
- **权限要求**: `/store/order/ship`
- **必需参数**: 订单ID、物流公司、运单号

## 使用指南

### 1. 基本使用

```vue
<template>
  <div>
    <!-- 订单列表页面 -->
    <router-link to="/orders/list">订单管理</router-link>
  </div>
</template>
```

### 2. 组件使用

```vue
<script setup lang="ts">
import OrderTable from '@/views/orders/components/OrderTable.vue'
import type { Order } from '@/views/orders/types'

const orders = ref<Order[]>([])

const handleViewDetail = (order: Order) => {
  // 处理查看详情
}

const handleShip = (order: Order) => {
  // 处理发货
}
</script>

<template>
  <OrderTable
    :data="orders"
    @view-detail="handleViewDetail"
    @ship="handleShip"
  />
</template>
```

### 3. API调用

```typescript
import { getOrderList, getOrderDetail, shipOrder } from '@/api/modules/orders'

// 获取订单列表
const fetchOrders = async () => {
  const response = await getOrderList({
    page: 0,
    size: 20,
    status: 1
  })
  return response.data
}

// 获取订单详情
const fetchOrderDetail = async (id: string) => {
  const response = await getOrderDetail(id)
  return response.data
}

// 订单发货
const handleShip = async (params: ShipOrderParams) => {
  const response = await shipOrder(params)
  return response.data
}
```

### 4. 工具函数使用

```typescript
import { formatAmount, formatTime, canShipOrder } from '@/views/orders/utils'

// 格式化金额
const amount = formatAmount(12345) // ¥123.45

// 格式化时间
const time = formatTime(Date.now()) // 2024-01-01 12:00:00

// 判断是否可发货
const canShip = canShipOrder(1) // true
```

## 权限配置

订单管理模块需要以下权限：

- `/store/order/query` - 查看订单列表
- `/store/order/detail` - 查看订单详情
- `/store/order/ship` - 订单发货

## 样式定制

模块使用Element Plus主题系统，支持：

- 深色模式适配
- 主题色彩定制
- 响应式布局
- 移动端适配

## 扩展开发

### 添加新的订单状态

1. 更新类型定义：
```typescript
// src/views/orders/types/index.ts
export enum OrderStatus {
  // 添加新状态
  NEW_STATUS = 8,
}

export const OrderStatusMap = {
  // 添加状态映射
  [OrderStatus.NEW_STATUS]: '新状态',
}
```

2. 更新组件逻辑：
```typescript
// 更新状态标签类型映射
const statusTagType = {
  8: 'primary', // 新状态
}
```

### 添加新的搜索条件

1. 更新类型定义：
```typescript
export interface OrderListParams {
  // 添加新的搜索参数
  newField?: string
}
```

2. 更新搜索表单组件：
```vue
<ElFormItem label="新字段" prop="newField">
  <ElInput v-model="formData.newField" />
</ElFormItem>
```

## 注意事项

1. **权限验证**: 确保用户具有相应的权限才能访问功能
2. **数据格式**: 金额以分为单位存储，显示时需要转换
3. **时间处理**: 时间戳统一使用毫秒级
4. **错误处理**: 所有API调用都应包含错误处理
5. **性能优化**: 大量数据时考虑虚拟滚动或分页加载

## 更新日志

- v1.0.0: 初始版本，包含基础订单管理功能
- v1.0.1: 添加订单发货功能
- v1.0.2: 优化搜索和筛选功能
- v1.0.3: 改进响应式设计和移动端适配
