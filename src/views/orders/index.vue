<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOrderList, getOrderDetail, shipOrder } from '@/api/modules/orders'
import type { OrderListParams, Order, ShipOrderParams } from './types'
import OrderSearchForm from './components/OrderSearchForm.vue'
import OrderTable from './components/OrderTable.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import ShipOrderDialog from './components/ShipOrderDialog.vue'

defineOptions({
  name: 'OrdersIndex',
})

// 响应式数据
const loading = ref(false)
const tableData = ref<Order[]>([])
const total = ref(0)

// 分页参数
const pagination = reactive({
  page: 1,
  size: 20,
})

// 搜索参数
const searchParams = reactive<OrderListParams>({
  page: 1,
  size: 20,
  orderNo: '',
  startTime: undefined,
  endTime: undefined,
  status: undefined,
  userPhone: '',
})

// 对话框状态
const detailDialogVisible = ref(false)
const shipDialogVisible = ref(false)
const currentOrder = ref<Order | null>(null)

// 获取订单列表
const fetchOrderList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchParams,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size,
    }

    const response = await getOrderList(params)
    if (response.data.code === 0) {
      const data = response.data.data
      tableData.value = data.content || data.result || []
      total.value = data.total || 0
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params: Partial<OrderListParams>) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  fetchOrderList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams, {
    page: 1,
    size: 20,
    orderNo: '',
    startTime: undefined,
    endTime: undefined,
    status: undefined,
    userPhone: '',
  })
  pagination.page = 1
  fetchOrderList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  searchParams.size = size
  fetchOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  searchParams.page = page
  fetchOrderList()
}

// 查看订单详情
const handleViewDetail = async (order: Order) => {
  try {
    const response = await getOrderDetail(order.id)
    if (response.data.code === 0) {
      currentOrder.value = response.data.data
      detailDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

// 订单发货
const handleShip = (order: Order) => {
  currentOrder.value = order
  shipDialogVisible.value = true
}

// 确认发货
const handleConfirmShip = async (params: ShipOrderParams) => {
  try {
    const response = await shipOrder(params)
    if (response.data.code === 0) {
      ElMessage.success('发货成功')
      shipDialogVisible.value = false
      fetchOrderList() // 刷新列表
    }
  } catch (error) {
    console.error('发货失败:', error)
    ElMessage.error('发货失败')
  }
}

// 初始化
onMounted(() => {
  fetchOrderList()
})
</script>

<template>
  <div>
    <FaPageHeader title="订单管理">
      <template #description>
        <p>管理系统中的所有订单，包括查看订单详情、处理发货等操作</p>
      </template>
    </FaPageHeader>

    <FaPageMain>
      <!-- 搜索表单 -->
      <OrderSearchForm
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />

      <!-- 订单表格 -->
      <OrderTable
        :data="tableData"
        :loading="loading"
        @view-detail="handleViewDetail"
        @ship="handleShip"
      />

      <!-- 分页 -->
      <ElPagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="mt-4 flex justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </FaPageMain>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order="currentOrder"
    />

    <!-- 发货对话框 -->
    <ShipOrderDialog
      v-model="shipDialogVisible"
      :order="currentOrder"
      @confirm="handleConfirmShip"
    />
  </div>
</template>

<style scoped>
/* 订单管理页面样式 */
</style>
