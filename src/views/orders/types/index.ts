/**
 * 订单管理模块类型定义
 * 基于API文档定义完整的类型系统
 */

// ==================== 基础类型 ====================

/** 基础响应类型 */
export interface BaseResponse<T = any> {
  code: number
  data: T
  msg: string
  success?: boolean
}

/** 分页响应类型 */
export interface PageResponse<T = any> {
  total: number
  content: T[]
  result: T[]
}

/** 分页请求参数 */
export interface PageParams {
  page: number
  size: number
}

// ==================== 枚举类型 ====================

/** 订单状态枚举 */
export enum OrderStatus {
  PENDING_PAYMENT = 0, // 待付款
  PAID = 1, // 已付款
  SHIPPED = 2, // 已发货
  RECEIVED = 3, // 已收货
  COMPLETED = 4, // 已完成
  CANCELLED = 5, // 已取消
  REFUNDING = 6, // 退款中
  REFUNDED = 7, // 已退款
}

/** 订单状态描述映射 */
export const OrderStatusMap = {
  [OrderStatus.PENDING_PAYMENT]: '待付款',
  [OrderStatus.PAID]: '已付款',
  [OrderStatus.SHIPPED]: '已发货',
  [OrderStatus.RECEIVED]: '已收货',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.REFUNDING]: '退款中',
  [OrderStatus.REFUNDED]: '已退款',
}

/** 支付方式枚举 */
export enum PaymentMethod {
  WECHAT = 1, // 微信支付
  ALIPAY = 2, // 支付宝
  BANK_CARD = 3, // 银行卡
  BALANCE = 4, // 余额支付
}

/** 支付方式描述映射 */
export const PaymentMethodMap = {
  [PaymentMethod.WECHAT]: '微信支付',
  [PaymentMethod.ALIPAY]: '支付宝',
  [PaymentMethod.BANK_CARD]: '银行卡',
  [PaymentMethod.BALANCE]: '余额支付',
}

/** 订单来源枚举 */
export enum OrderSource {
  WEB = 0, // 网页
  MOBILE = 1, // 手机端
  MINI_PROGRAM = 2, // 小程序
  APP = 3, // APP
}

/** 订单来源描述映射 */
export const OrderSourceMap = {
  [OrderSource.WEB]: '网页',
  [OrderSource.MOBILE]: '手机端',
  [OrderSource.MINI_PROGRAM]: '小程序',
  [OrderSource.APP]: 'APP',
}

// ==================== 订单相关类型 ====================

/** 订单商品项 */
export interface OrderItem {
  id: string
  userId: string
  orderId: string
  productId: string
  productName: string
  productImage: string
  productSkuId: string
  productSkuName: string
  price: number // 商品单价（单位：分）
  quantity: number // 数量
  totalAmount: number // 小计金额（单位：分）
}

/** 订单信息 */
export interface Order {
  id: string // 订单唯一标识
  userId: string // 账户ID
  totalAmount: number // 订单总金额（单位：分）
  itemList: OrderItem[] // 订单商品列表
  payAmount: number // 实付金额（单位：分）
  discountAmount: number // 优惠金额（单位：分）
  shoppingCartId: string // 购物车id
  shippingFee: number // 运费（单位：分）
  status: OrderStatus // 订单状态
  statusDesc: string // 订单状态展示字段
  paymentMethod: PaymentMethod // 支付方式
  paymentMethodDesc: string // 支付方式描述
  paymentTime: number // 支付时间
  shippingTime: number // 发货时间
  completedTime: number // 完成时间
  shippingInfoId: string // 收货信息ID
  logisticsIdList: number[] // 物流信息ID列表
  couponIdList: number[] // 优惠券ID列表
  remark: string // 备注
  source: OrderSource // 订单来源
  sourceDesc: string // 订单来源展示
  externalOrderNo: string // 外部订单号
  createAt: number // 创建时间
  updateAt: number // 更新时间
}

// ==================== API请求参数类型 ====================

/** 订单列表查询参数 */
export interface OrderListParams extends PageParams {
  orderNo?: string // 订单编号
  startTime?: number // 下单开始时间
  endTime?: number // 下单结束时间
  status?: OrderStatus // 订单状态
  userPhone?: string // 用户手机号
}

/** 订单发货参数 */
export interface ShipOrderParams {
  id: string // 订单id
  companyName: string // 物流公司名称
  trackingNumber: string // 运单号
}

// ==================== API响应类型 ====================

/** 订单列表响应 */
export type OrderListResponse = PageResponse<Order>

/** 订单详情响应 */
export type OrderDetailResponse = Order

// ==================== 表单相关类型 ====================

/** 搜索表单数据 */
export interface SearchFormData {
  orderNo: string
  dateRange: any
  status: OrderStatus | undefined
  userPhone: string
}

/** 发货表单数据 */
export interface ShipFormData {
  companyName: string
  trackingNumber: string
}

// ==================== 工具类型 ====================

/** 金额格式化选项 */
export interface AmountFormatOptions {
  unit?: 'yuan' | 'fen' // 单位：元或分
  precision?: number // 小数位数
  showSymbol?: boolean // 是否显示货币符号
}

/** 时间格式化选项 */
export interface TimeFormatOptions {
  format?: string // 时间格式
  showTime?: boolean // 是否显示时间
}
