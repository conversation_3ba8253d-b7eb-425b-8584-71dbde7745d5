<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElButton, ElCard, ElSkeleton } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { getOrderDetail, shipOrder } from '@/api/modules/orders'
import type { Order, ShipOrderParams } from '../types'
import OrderDetailCard from '../components/OrderDetailCard.vue'
import ShipOrderDialog from '../components/ShipOrderDialog.vue'

defineOptions({
  name: 'OrdersDetail',
})

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const order = ref<Order | null>(null)
const shipDialogVisible = ref(false)

// 获取订单ID
const orderId = route.params.id as string

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!orderId) {
    ElMessage.error('订单ID不能为空')
    router.back()
    return
  }

  try {
    loading.value = true
    const response = await getOrderDetail(orderId)
    if (response.data.code === 0) {
      order.value = response.data.data
    } else {
      ElMessage.error(response.data.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 返回列表
const handleBack = () => {
  router.push('/orders/list')
}

// 订单发货
const handleShip = () => {
  if (!order.value) return
  shipDialogVisible.value = true
}

// 确认发货
const handleConfirmShip = async (params: ShipOrderParams) => {
  try {
    const response = await shipOrder(params)
    if (response.data.code === 0) {
      ElMessage.success('发货成功')
      shipDialogVisible.value = false
      // 重新获取订单详情
      await fetchOrderDetail()
    }
  } catch (error) {
    console.error('发货失败:', error)
    ElMessage.error('发货失败')
  }
}

// 判断是否可以发货
const canShip = (status: number) => {
  return status === 1 // 已付款状态可以发货
}

// 初始化
onMounted(() => {
  fetchOrderDetail()
})
</script>

<template>
  <div>
    <FaPageHeader title="订单详情">
      <template #description>
        <p>查看订单的详细信息，包括商品、费用、物流等信息</p>
      </template>
      <template #default>
        <div class="flex items-center space-x-3">
          <ElButton
            :icon="ArrowLeft"
            @click="handleBack"
          >
            返回列表
          </ElButton>
          <ElButton
            v-if="order && canShip(order.status)"
            type="success"
            @click="handleShip"
          >
            订单发货
          </ElButton>
        </div>
      </template>
    </FaPageHeader>

    <FaPageMain>
      <!-- 加载状态 -->
      <ElCard v-if="loading" class="mb-4">
        <ElSkeleton :rows="8" animated />
      </ElCard>

      <!-- 订单详情 -->
      <OrderDetailCard
        v-else-if="order"
        :order="order"
        show-actions
        @ship="handleShip"
      />

      <!-- 空状态 -->
      <ElCard v-else class="text-center py-12">
        <div class="text-gray-500">
          <div class="text-4xl mb-4">📦</div>
          <div class="text-lg">订单不存在或已被删除</div>
        </div>
      </ElCard>
    </FaPageMain>

    <!-- 发货对话框 -->
    <ShipOrderDialog
      v-model="shipDialogVisible"
      :order="order"
      @confirm="handleConfirmShip"
    />
  </div>
</template>

<style scoped>
/* 订单详情页面样式 */
</style>
