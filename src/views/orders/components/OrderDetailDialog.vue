<script setup lang="ts">
import { computed } from 'vue'
import {
  ElDialog,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElTable,
  ElTableColumn,
  ElImage,
  ElText,
  ElDivider,
} from 'element-plus'
import type { Order } from '../types'
import { OrderStatusMap, PaymentMethodMap, OrderSourceMap } from '../types'

defineOptions({
  name: 'OrderDetailDialog',
})

interface Props {
  modelValue: boolean
  order: Order | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 订单状态标签类型
const statusTagType = computed(() => {
  if (!props.order) return 'info'
  const typeMap: Record<number, string> = {
    0: 'warning', // 待付款
    1: 'success', // 已付款
    2: 'primary', // 已发货
    3: 'info', // 已收货
    4: 'success', // 已完成
    5: 'danger', // 已取消
    6: 'warning', // 退款中
    7: 'info', // 已退款
  }
  return typeMap[props.order.status] || 'info'
})

// 格式化金额（分转元）
const formatAmount = (amount: number) => {
  return (amount / 100).toFixed(2)
}

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 计算商品总数量
// const totalQuantity = computed(() => {
//   if (!props.order?.itemList) return 0
//   return props.order.itemList.reduce((sum, item) => sum + item.quantity, 0)
// })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="订单详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="order" class="order-detail">
      <!-- 基本信息 -->
      <ElDescriptions title="订单信息" :column="2" border>
        <ElDescriptionsItem label="订单编号">
          <ElText class="font-mono">{{ order.id }}</ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="订单状态">
          <ElTag :type="statusTagType as any" size="small">
            {{ order.statusDesc || OrderStatusMap[order.status as keyof typeof OrderStatusMap] }}
          </ElTag>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户ID">
          {{ order.userId }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="外部订单号">
          {{ order.externalOrderNo || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="订单来源">
          {{ order.sourceDesc || OrderSourceMap[order.source] }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="支付方式">
          {{ order.paymentMethodDesc || PaymentMethodMap[order.paymentMethod] || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="下单时间">
          {{ formatTime(order.createAt) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="支付时间">
          {{ formatTime(order.paymentTime) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="发货时间">
          {{ formatTime(order.shippingTime) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="完成时间">
          {{ formatTime(order.completedTime) }}
        </ElDescriptionsItem>
      </ElDescriptions>

      <ElDivider />

      <!-- 商品信息 -->
      <div class="mb-4">
        <h3 class="text-lg font-medium mb-3">商品信息</h3>
        <ElTable :data="order.itemList" border>
          <ElTableColumn label="商品图片" width="80" align="center">
            <template #default="{ row }">
              <ElImage
                v-if="row.productImage"
                :src="row.productImage"
                :preview-src-list="[row.productImage]"
                class="w-12 h-12 rounded"
                fit="cover"
              />
              <div v-else class="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                <ElText size="small" class="text-gray-400">无图</ElText>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn label="商品名称" prop="productName" min-width="200" />
          <ElTableColumn label="规格" prop="productSkuName" width="150" />
          <ElTableColumn label="单价" width="100" align="right">
            <template #default="{ row }">
              ¥{{ formatAmount(row.price) }}
            </template>
          </ElTableColumn>
          <ElTableColumn label="数量" prop="quantity" width="80" align="center" />
          <ElTableColumn label="小计" width="100" align="right">
            <template #default="{ row }">
              ¥{{ formatAmount(row.totalAmount) }}
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <ElDivider />

      <!-- 费用信息 -->
      <ElDescriptions title="费用信息" :column="2" border>
        <ElDescriptionsItem label="商品总额">
          <ElText class="font-medium">¥{{ formatAmount(order.totalAmount) }}</ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="运费">
          <ElText>¥{{ formatAmount(order.shippingFee) }}</ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="优惠金额">
          <ElText class="text-green-600">-¥{{ formatAmount(order.discountAmount) }}</ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="实付金额">
          <ElText class="font-medium text-red-600 text-lg">¥{{ formatAmount(order.payAmount) }}</ElText>
        </ElDescriptionsItem>
      </ElDescriptions>

      <!-- 备注信息 -->
      <div v-if="order.remark" class="mt-4">
        <ElDivider />
        <ElDescriptions title="其他信息" :column="1" border>
          <ElDescriptionsItem label="备注">
            {{ order.remark }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
    </div>

    <template #footer>
      <ElButton @click="dialogVisible = false">关闭</ElButton>
    </template>
  </ElDialog>
</template>

<style scoped>
.order-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-descriptions__content) {
  word-break: break-all;
}
</style>
