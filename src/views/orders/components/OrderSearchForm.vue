<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { OrderListParams, SearchFormData } from '../types'
import { OrderStatusMap } from '../types'

defineOptions({
  name: 'OrderSearchForm',
})

interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'search', params: Partial<OrderListParams>): void
  (e: 'reset'): void
}

withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive<SearchFormData>({
  orderNo: '',
  dateRange: null as any,
  status: undefined,
  userPhone: '',
})

// 表单引用
const formRef = ref<InstanceType<typeof ElForm>>()

// 订单状态选项
const statusOptions = Object.entries(OrderStatusMap).map(([value, label]) => ({
  value: Number(value),
  label,
}))

// 搜索处理
const handleSearch = () => {
  const params: Partial<OrderListParams> = {
    orderNo: formData.orderNo.trim(),
    userPhone: formData.userPhone.trim(),
    status: formData.status,
  }

  // 处理日期范围
  if (formData.dateRange && formData.dateRange.length === 2) {
    params.startTime = new Date(formData.dateRange[0]).getTime()
    params.endTime = new Date(formData.dateRange[1]).getTime()
  }

  emit('search', params)
}

// 重置处理
const handleReset = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    orderNo: '',
    dateRange: null,
    status: undefined,
    userPhone: '',
  })
  emit('reset')
}

// 快捷日期选项
const shortcuts = [
  {
    text: '今天',
    value: () => {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const end = new Date(today)
      end.setHours(23, 59, 59, 999)
      return [today, end]
    },
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      yesterday.setHours(0, 0, 0, 0)
      const end = new Date(yesterday)
      end.setHours(23, 59, 59, 999)
      return [yesterday, end]
    },
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
]
</script>

<template>
  <div class="order-search-form">
    <ElForm
      ref="formRef"
      :model="formData"
      inline
      label-width="auto"
      class="mb-4"
    >
      <ElFormItem label="订单编号" prop="orderNo">
        <ElInput
          v-model="formData.orderNo"
          placeholder="请输入订单编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleSearch"
        />
      </ElFormItem>

      <ElFormItem label="下单时间" prop="dateRange">
        <ElDatePicker
          v-model="formData.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :shortcuts="shortcuts"
          style="width: 350px"
        />
      </ElFormItem>

      <ElFormItem label="订单状态" prop="status">
        <ElSelect
          v-model="formData.status"
          placeholder="请选择订单状态"
          clearable
          style="width: 150px"
        >
          <ElOption
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="用户手机号" prop="userPhone">
        <ElInput
          v-model="formData.userPhone"
          placeholder="请输入用户手机号"
          clearable
          style="width: 180px"
          @keyup.enter="handleSearch"
        />
      </ElFormItem>

      <ElFormItem>
        <ElButton
          type="primary"
          :icon="Search"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </ElButton>
        <ElButton
          :icon="Refresh"
          @click="handleReset"
        >
          重置
        </ElButton>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<style scoped>
.order-search-form {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.order-search-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.order-search-form :deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>
