<script setup lang="ts">
import { computed } from 'vue'
import {
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElTable,
  ElTableColumn,
  ElImage,
  ElText,
  ElButton,

  ElRow,
  ElCol,
} from 'element-plus'
// import { Truck } from '@element-plus/icons-vue'
import type { Order } from '../types'
import { OrderStatusMap, PaymentMethodMap, OrderSourceMap } from '../types'

defineOptions({
  name: 'OrderDetailCard',
})

interface Props {
  order: Order
  showActions?: boolean
}

interface Emits {
  (e: 'ship'): void
}

const props = withDefaults(defineProps<Props>(), {
  showActions: false,
})

const emit = defineEmits<Emits>()

// 订单状态标签类型
const statusTagType = computed(() => {
  const typeMap: Record<number, string> = {
    0: 'warning', // 待付款
    1: 'success', // 已付款
    2: 'primary', // 已发货
    3: 'info', // 已收货
    4: 'success', // 已完成
    5: 'danger', // 已取消
    6: 'warning', // 退款中
    7: 'info', // 已退款
  }
  return typeMap[props.order.status] || 'info'
})

// 格式化金额（分转元）
const formatAmount = (amount: number) => {
  return (amount / 100).toFixed(2)
}

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 计算商品总数量
const totalQuantity = computed(() => {
  if (!props.order.itemList) return 0
  return props.order.itemList.reduce((sum, item) => sum + item.quantity, 0)
})

// 判断是否可以发货
const canShip = computed(() => {
  return props.order.status === 1 // 已付款状态可以发货
})

// 发货操作
const handleShip = () => {
  emit('ship')
}
</script>

<template>
  <div class="order-detail-card">
    <!-- 订单基本信息 -->
    <ElCard class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">订单信息</span>
          <div class="flex items-center space-x-3">
            <ElTag :type="statusTagType as any" size="large">
              {{ order.statusDesc || OrderStatusMap[order.status as keyof typeof OrderStatusMap] }}
            </ElTag>
            <ElButton
              v-if="showActions && canShip"
              type="success"
              size="small"
              @click="handleShip"
            >
              订单发货
            </ElButton>
          </div>
        </div>
      </template>

      <ElDescriptions :column="3" border>
        <ElDescriptionsItem label="订单编号" :span="2">
          <ElText class="font-mono">{{ order.id }}</ElText>
        </ElDescriptionsItem>
        <ElDescriptionsItem label="外部订单号">
          {{ order.externalOrderNo || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户ID">
          {{ order.userId }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="订单来源">
          {{ order.sourceDesc || OrderSourceMap[order.source] }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="支付方式">
          {{ order.paymentMethodDesc || PaymentMethodMap[order.paymentMethod] || '-' }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="下单时间">
          {{ formatTime(order.createAt) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="支付时间">
          {{ formatTime(order.paymentTime) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="发货时间">
          {{ formatTime(order.shippingTime) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="完成时间">
          {{ formatTime(order.completedTime) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="更新时间">
          {{ formatTime(order.updateAt) }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElCard>

    <!-- 商品信息 -->
    <ElCard class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">商品信息</span>
          <ElText class="text-gray-500">共 {{ totalQuantity }} 件商品</ElText>
        </div>
      </template>

      <ElTable :data="order.itemList" border>
        <ElTableColumn label="商品图片" width="100" align="center">
          <template #default="{ row }">
            <ElImage
              v-if="row.productImage"
              :src="row.productImage"
              :preview-src-list="[row.productImage]"
              class="w-16 h-16 rounded"
              fit="cover"
            />
            <div v-else class="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
              <ElText size="small" class="text-gray-400">无图</ElText>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn label="商品名称" prop="productName" min-width="200" show-overflow-tooltip />
        <ElTableColumn label="商品ID" prop="productId" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <ElText class="font-mono text-xs">{{ row.productId }}</ElText>
          </template>
        </ElTableColumn>
        <ElTableColumn label="规格" prop="productSkuName" width="150" show-overflow-tooltip />
        <ElTableColumn label="SKU ID" prop="productSkuId" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <ElText class="font-mono text-xs">{{ row.productSkuId }}</ElText>
          </template>
        </ElTableColumn>
        <ElTableColumn label="单价" width="100" align="right">
          <template #default="{ row }">
            ¥{{ formatAmount(row.price) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="数量" prop="quantity" width="80" align="center" />
        <ElTableColumn label="小计" width="120" align="right">
          <template #default="{ row }">
            <ElText class="font-medium">¥{{ formatAmount(row.totalAmount) }}</ElText>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>

    <!-- 费用信息 -->
    <ElCard class="mb-4">
      <template #header>
        <span class="text-lg font-medium">费用信息</span>
      </template>

      <ElRow :gutter="24">
        <ElCol :span="12">
          <ElDescriptions :column="1" border>
            <ElDescriptionsItem label="商品总额">
              <ElText class="font-medium">¥{{ formatAmount(order.totalAmount) }}</ElText>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="运费">
              <ElText>¥{{ formatAmount(order.shippingFee) }}</ElText>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="优惠金额">
              <ElText class="text-green-600">-¥{{ formatAmount(order.discountAmount) }}</ElText>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCol>
        <ElCol :span="12">
          <div class="h-full flex items-center justify-center bg-gray-50 rounded">
            <div class="text-center">
              <div class="text-sm text-gray-500 mb-2">实付金额</div>
              <div class="text-3xl font-bold text-red-600">
                ¥{{ formatAmount(order.payAmount) }}
              </div>
            </div>
          </div>
        </ElCol>
      </ElRow>
    </ElCard>

    <!-- 其他信息 -->
    <ElCard v-if="order.remark || order.shippingInfoId || order.logisticsIdList?.length || order.couponIdList?.length">
      <template #header>
        <span class="text-lg font-medium">其他信息</span>
      </template>

      <ElDescriptions :column="2" border>
        <ElDescriptionsItem v-if="order.shippingInfoId" label="收货信息ID">
          {{ order.shippingInfoId }}
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="order.shoppingCartId" label="购物车ID">
          {{ order.shoppingCartId }}
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="order.logisticsIdList?.length" label="物流信息ID" :span="2">
          {{ order.logisticsIdList.join(', ') }}
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="order.couponIdList?.length" label="优惠券ID" :span="2">
          {{ order.couponIdList.join(', ') }}
        </ElDescriptionsItem>
        <ElDescriptionsItem v-if="order.remark" label="备注" :span="2">
          {{ order.remark }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElCard>
  </div>
</template>

<style scoped>
.order-detail-card {
  max-width: 100%;
}

.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-descriptions__content) {
  word-break: break-all;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
