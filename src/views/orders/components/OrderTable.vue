<script setup lang="ts">
import { computed } from 'vue'
import { ElTable, ElTableColumn, ElTag, ElButton, ElImage, ElText, ElTooltip } from 'element-plus'
import { View } from '@element-plus/icons-vue'
import type { Order, OrderStatus } from '../types'
import { OrderStatusMap, PaymentMethodMap, OrderSourceMap } from '../types'

defineOptions({
  name: 'OrderTable',
})

interface Props {
  data: Order[]
  loading?: boolean
}

interface Emits {
  (e: 'view-detail', order: Order): void
  (e: 'ship', order: Order): void
}

withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// 订单状态标签类型映射
const statusTagType = computed(() => {
  return {
    0: 'warning', // 待付款
    1: 'success', // 已付款
    2: 'primary', // 已发货
    3: 'info', // 已收货
    4: 'success', // 已完成
    5: 'danger', // 已取消
    6: 'warning', // 退款中
    7: 'info', // 已退款
  }
})

// 格式化金额（分转元）
const formatAmount = (amount: number) => {
  return (amount / 100).toFixed(2)
}

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 查看详情
const handleViewDetail = (order: Order) => {
  emit('view-detail', order)
}

// 发货操作
const handleShip = (order: Order) => {
  emit('ship', order)
}

// 判断是否可以发货
const canShip = (status: OrderStatus) => {
  return status === 1 // 已付款状态可以发货
}
</script>

<template>
  <div class="order-table">
    <ElTable
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      @row-dblclick="handleViewDetail"
    >
      <!-- 订单编号 -->
      <ElTableColumn label="订单编号" prop="id" width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <ElText class="font-mono">{{ row.id }}</ElText>
        </template>
      </ElTableColumn>

      <!-- 商品信息 -->
      <ElTableColumn label="商品信息" min-width="250">
        <template #default="{ row }">
          <div class="flex items-start space-x-3">
            <ElImage
              v-if="row.itemList?.[0]?.productImage"
              :src="row.itemList[0].productImage"
              :preview-src-list="[row.itemList[0].productImage]"
              class="w-12 h-12 rounded"
              fit="cover"
            />
            <div class="flex-1 min-w-0">
              <div class="font-medium text-sm truncate">
                {{ row.itemList?.[0]?.productName || '-' }}
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ row.itemList?.[0]?.productSkuName || '' }}
              </div>
              <div v-if="row.itemList && row.itemList.length > 1" class="text-xs text-blue-500 mt-1">
                等{{ row.itemList.length }}件商品
              </div>
            </div>
          </div>
        </template>
      </ElTableColumn>

      <!-- 订单金额 -->
      <ElTableColumn label="订单金额" width="120" align="right">
        <template #default="{ row }">
          <div class="space-y-1">
            <div class="font-medium text-red-600">
              ¥{{ formatAmount(row.payAmount) }}
            </div>
            <div v-if="row.discountAmount > 0" class="text-xs text-gray-500">
              优惠: ¥{{ formatAmount(row.discountAmount) }}
            </div>
          </div>
        </template>
      </ElTableColumn>

      <!-- 订单状态 -->
      <ElTableColumn label="订单状态" width="100" align="center">
        <template #default="{ row }">
          <ElTag
            :type="statusTagType[row.status as keyof typeof statusTagType] as any"
            size="small"
          >
            {{ row.statusDesc || OrderStatusMap[row.status as keyof typeof OrderStatusMap] }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 支付方式 -->
      <ElTableColumn label="支付方式" width="100" align="center">
        <template #default="{ row }">
          <ElText size="small">
            {{ row.paymentMethodDesc || PaymentMethodMap[row.paymentMethod as keyof typeof PaymentMethodMap] || '-' }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 订单来源 -->
      <ElTableColumn label="来源" width="80" align="center">
        <template #default="{ row }">
          <ElText size="small">
            {{ row.sourceDesc || OrderSourceMap[row.source as keyof typeof OrderSourceMap] || '-' }}
          </ElText>
        </template>
      </ElTableColumn>

      <!-- 下单时间 -->
      <ElTableColumn label="下单时间" width="160">
        <template #default="{ row }">
          <ElText size="small">{{ formatTime(row.createAt) }}</ElText>
        </template>
      </ElTableColumn>

      <!-- 支付时间 -->
      <ElTableColumn label="支付时间" width="160">
        <template #default="{ row }">
          <ElText size="small">{{ formatTime(row.paymentTime) }}</ElText>
        </template>
      </ElTableColumn>

      <!-- 操作 -->
      <ElTableColumn label="操作" width="150" align="center" fixed="right">
        <template #default="{ row }">
          <div class="flex justify-center space-x-2">
            <ElTooltip content="查看详情" placement="top">
              <ElButton
                type="primary"
                :icon="View"
                size="small"
                circle
                @click="handleViewDetail(row)"
              />
            </ElTooltip>

            <ElTooltip
              v-if="canShip(row.status)"
              content="订单发货"
              placement="top"
            >
              <ElButton
                type="success"
                size="small"
                circle
                @click="handleShip(row)"
              >
                发货
              </ElButton>
            </ElTooltip>
          </div>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<style scoped>
.order-table {
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
}

.order-table :deep(.el-table__row) {
  cursor: pointer;
}

.order-table :deep(.el-table__row:hover) {
  background-color: var(--el-table-row-hover-bg-color);
}

.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
