# 订单管理模块测试指南

## 功能测试清单

### 1. 页面访问测试
- [ ] 访问 `/orders/list` 页面是否正常加载
- [ ] 页面标题和描述是否正确显示
- [ ] 搜索表单是否正常渲染
- [ ] 表格组件是否正常渲染
- [ ] 分页组件是否正常渲染

### 2. 搜索功能测试
- [ ] 订单编号搜索
- [ ] 时间范围搜索
- [ ] 订单状态筛选
- [ ] 用户手机号搜索
- [ ] 重置搜索功能
- [ ] 快捷日期选择功能

### 3. 表格功能测试
- [ ] 订单列表数据展示
- [ ] 订单状态标签显示
- [ ] 金额格式化显示
- [ ] 时间格式化显示
- [ ] 商品信息展示
- [ ] 操作按钮显示

### 4. 详情功能测试
- [ ] 点击查看详情按钮
- [ ] 详情对话框正常打开
- [ ] 订单基本信息展示
- [ ] 商品列表展示
- [ ] 费用信息展示
- [ ] 关闭对话框功能

### 5. 发货功能测试
- [ ] 发货按钮显示条件（已付款状态）
- [ ] 点击发货按钮
- [ ] 发货对话框正常打开
- [ ] 物流公司输入
- [ ] 运单号输入
- [ ] 表单验证功能
- [ ] 快速选择物流公司
- [ ] 提交发货功能

### 6. 分页功能测试
- [ ] 页码切换
- [ ] 页面大小切换
- [ ] 总数显示
- [ ] 跳转页面功能

### 7. 响应式测试
- [ ] 桌面端显示
- [ ] 平板端显示
- [ ] 移动端显示
- [ ] 表格横向滚动

### 8. 错误处理测试
- [ ] API请求失败处理
- [ ] 网络错误处理
- [ ] 表单验证错误提示
- [ ] 空数据状态显示

## 测试数据

### 模拟订单数据
```json
{
  "id": "ORDER_20240101_001",
  "userId": "USER_001",
  "totalAmount": 12345,
  "payAmount": 11000,
  "discountAmount": 1345,
  "shippingFee": 0,
  "status": 1,
  "statusDesc": "已付款",
  "paymentMethod": 1,
  "paymentMethodDesc": "微信支付",
  "paymentTime": 1704067200000,
  "source": 2,
  "sourceDesc": "小程序",
  "createAt": 1704067200000,
  "updateAt": 1704067200000,
  "itemList": [
    {
      "id": "ITEM_001",
      "productName": "测试商品",
      "productImage": "https://example.com/image.jpg",
      "productSkuName": "红色 L码",
      "price": 12345,
      "quantity": 1,
      "totalAmount": 12345
    }
  ]
}
```

### 搜索参数测试
```json
{
  "page": 0,
  "size": 20,
  "orderNo": "ORDER_20240101_001",
  "startTime": 1704067200000,
  "endTime": 1704153600000,
  "status": 1,
  "userPhone": "13800138000"
}
```

### 发货参数测试
```json
{
  "id": "ORDER_20240101_001",
  "companyName": "顺丰速运",
  "trackingNumber": "SF1234567890"
}
```

## 测试步骤

### 1. 基础功能测试
1. 启动开发服务器：`npm run dev`
2. 访问 `http://localhost:9001/orders/list`
3. 检查页面是否正常加载
4. 检查各个组件是否正常渲染

### 2. 搜索功能测试
1. 在订单编号输入框输入测试数据
2. 选择时间范围
3. 选择订单状态
4. 输入手机号
5. 点击搜索按钮
6. 检查搜索结果
7. 点击重置按钮
8. 检查表单是否重置

### 3. 表格交互测试
1. 双击表格行查看详情
2. 点击查看详情按钮
3. 点击发货按钮（如果可用）
4. 检查各种状态的订单显示

### 4. 分页测试
1. 切换不同页码
2. 修改每页显示数量
3. 使用跳转功能
4. 检查数据是否正确更新

### 5. 响应式测试
1. 调整浏览器窗口大小
2. 使用开发者工具模拟不同设备
3. 检查布局是否适配

## 已知问题和限制

1. **API接口**: 当前使用的是模拟数据，实际使用时需要连接真实的后端API
2. **权限控制**: 需要配置相应的权限才能访问功能
3. **图片显示**: 商品图片需要有效的URL才能正常显示
4. **时区处理**: 时间显示可能需要根据用户时区进行调整

## 性能优化建议

1. **虚拟滚动**: 对于大量数据，考虑使用虚拟滚动
2. **懒加载**: 图片和详情数据可以考虑懒加载
3. **缓存策略**: 实现适当的数据缓存策略
4. **防抖处理**: 搜索输入添加防抖处理

## 后续改进计划

1. **批量操作**: 添加批量发货、批量导出等功能
2. **高级筛选**: 添加更多筛选条件
3. **数据导出**: 支持Excel、CSV等格式导出
4. **打印功能**: 支持订单打印
5. **状态流转**: 添加订单状态流转图
6. **消息通知**: 添加订单状态变更通知
