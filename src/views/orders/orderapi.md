# 文档

## 【后台】订单管理


### 订单列表
接口权限：/store/order/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_order/list


描述：订单列表
接口权限：/store/order/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| orderNo | string | 否 | - | 订单编号 |  |
| startTime | int64 | 否 | - | 下单开始时间 | 0 |
| endTime | int64 | 否 | - | 下单结束时间 | 0 |
| status | int32 | 否 | - | 订单状态 | 0 |
| userPhone | string | 否 | - | 用户手机号 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "orderNo": "",
    "startTime": 0,
    "endTime": 0,
    "status": 0,
    "userPhone": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 订单唯一标识 |  |
|   └ userId | string | 否 | - | 账户ID |  |
|   └ totalAmount | int64 | 否 | - | 订单总金额 | 0 |
|   └ itemList | array | 否 |  | 订单商品列表 |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ userId | string | 否 | - | 用户ID |  |
|     └ orderId | string | 否 | - | 订单ID |  |
|     └ productId | string | 否 | - | 商品ID |  |
|     └ productName | string | 否 | - | 商品名称 |  |
|     └ productImage | string | 否 | - | 商品图片 |  |
|     └ productSkuId | string | 否 | - | 商品SKU ID |  |
|     └ productSkuName | string | 否 | - | 商品SKU 名称 |  |
|     └ price | int64 | 否 | - | 商品单价（单位：分） | 0 |
|     └ quantity | int32 | 否 | - | 数量 | 0 |
|     └ totalAmount | int64 | 否 | - | 小计金额（单位：分） | 0 |
|   └ payAmount | int64 | 否 | - | 实付金额（单位：分） | 0 |
|   └ discountAmount | int64 | 否 | - | 优惠金额（单位：分） | 0 |
|   └ shoppingCartId | string | 否 | - | 购物车id |  |
|   └ shippingFee | int64 | 否 | - | 运费（单位：分） | 0 |
|   └ status | int32 | 否 | - | 订单状态（待付款/已付款/已发货/已收货/已完成/已取消/退款中/已退款） | 0 |
|   └ statusDesc | string | 否 | - | 订单状态展示字段 |  |
|   └ paymentMethod | int32 | 否 | - | 支付方式 | 0 |
|   └ paymentMethodDesc | string | 否 | - | 支付方式 |  |
|   └ paymentTime | int64 | 否 | - | 支付时间 | 0 |
|   └ shippingTime | int64 | 否 | - | 发货时间 | 0 |
|   └ completedTime | int64 | 否 | - | 完成时间 | 0 |
|   └ shippingInfoId | string | 否 | - | 收货信息ID |  |
|   └ logisticsIdList | array | 否 | - | 物流信息ID列表 | , |
|   └ couponIdList | array | 否 | - | 优惠券ID列表 | , |
|   └ remark | string | 否 | - | 备注 |  |
|   └ source | int32 | 否 | - | 订单来源 | 0 |
|   └ sourceDesc | string | 否 | - | 订单来源展示 |  |
|   └ externalOrderNo | string | 否 | - | 外部订单号 |  |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | 订单唯一标识 |  |
|   └ userId | string | 否 | - | 账户ID |  |
|   └ totalAmount | int64 | 否 | - | 订单总金额 | 0 |
|   └ itemList | array | 否 |  | 订单商品列表 |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ userId | string | 否 | - | 用户ID |  |
|     └ orderId | string | 否 | - | 订单ID |  |
|     └ productId | string | 否 | - | 商品ID |  |
|     └ productName | string | 否 | - | 商品名称 |  |
|     └ productImage | string | 否 | - | 商品图片 |  |
|     └ productSkuId | string | 否 | - | 商品SKU ID |  |
|     └ productSkuName | string | 否 | - | 商品SKU 名称 |  |
|     └ price | int64 | 否 | - | 商品单价（单位：分） | 0 |
|     └ quantity | int32 | 否 | - | 数量 | 0 |
|     └ totalAmount | int64 | 否 | - | 小计金额（单位：分） | 0 |
|   └ payAmount | int64 | 否 | - | 实付金额（单位：分） | 0 |
|   └ discountAmount | int64 | 否 | - | 优惠金额（单位：分） | 0 |
|   └ shoppingCartId | string | 否 | - | 购物车id |  |
|   └ shippingFee | int64 | 否 | - | 运费（单位：分） | 0 |
|   └ status | int32 | 否 | - | 订单状态（待付款/已付款/已发货/已收货/已完成/已取消/退款中/已退款） | 0 |
|   └ statusDesc | string | 否 | - | 订单状态展示字段 |  |
|   └ paymentMethod | int32 | 否 | - | 支付方式 | 0 |
|   └ paymentMethodDesc | string | 否 | - | 支付方式 |  |
|   └ paymentTime | int64 | 否 | - | 支付时间 | 0 |
|   └ shippingTime | int64 | 否 | - | 发货时间 | 0 |
|   └ completedTime | int64 | 否 | - | 完成时间 | 0 |
|   └ shippingInfoId | string | 否 | - | 收货信息ID |  |
|   └ logisticsIdList | array | 否 | - | 物流信息ID列表 | , |
|   └ couponIdList | array | 否 | - | 优惠券ID列表 | , |
|   └ remark | string | 否 | - | 备注 |  |
|   └ source | int32 | 否 | - | 订单来源 | 0 |
|   └ sourceDesc | string | 否 | - | 订单来源展示 |  |
|   └ externalOrderNo | string | 否 | - | 外部订单号 |  |
|   └ createAt | int64 | 否 | - | 创建时间 | 0 |
|   └ updateAt | int64 | 否 | - | 更新时间 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "userId": "",
            "totalAmount": 0,
            "itemList": [
                {
                    "id": "",
                    "userId": "",
                    "orderId": "",
                    "productId": "",
                    "productName": "",
                    "productImage": "",
                    "productSkuId": "",
                    "productSkuName": "",
                    "price": 0,
                    "quantity": 0,
                    "totalAmount": 0
                }
            ],
            "payAmount": 0,
            "discountAmount": 0,
            "shoppingCartId": "",
            "shippingFee": 0,
            "status": 0,
            "statusDesc": "",
            "paymentMethod": 0,
            "paymentMethodDesc": "",
            "paymentTime": 0,
            "shippingTime": 0,
            "completedTime": 0,
            "shippingInfoId": "",
            "logisticsIdList": [
                0,
                0
            ],
            "couponIdList": [
                0,
                0
            ],
            "remark": "",
            "source": 0,
            "sourceDesc": "",
            "externalOrderNo": "",
            "createAt": 0,
            "updateAt": 0
        }
    ],
    "result": [
        {
            "id": "",
            "userId": "",
            "totalAmount": 0,
            "itemList": [
                {
                    "id": "",
                    "userId": "",
                    "orderId": "",
                    "productId": "",
                    "productName": "",
                    "productImage": "",
                    "productSkuId": "",
                    "productSkuName": "",
                    "price": 0,
                    "quantity": 0,
                    "totalAmount": 0
                }
            ],
            "payAmount": 0,
            "discountAmount": 0,
            "shoppingCartId": "",
            "shippingFee": 0,
            "status": 0,
            "statusDesc": "",
            "paymentMethod": 0,
            "paymentMethodDesc": "",
            "paymentTime": 0,
            "shippingTime": 0,
            "completedTime": 0,
            "shippingInfoId": "",
            "logisticsIdList": [
                0,
                0
            ],
            "couponIdList": [
                0,
                0
            ],
            "remark": "",
            "source": 0,
            "sourceDesc": "",
            "externalOrderNo": "",
            "createAt": 0,
            "updateAt": 0
        }
    ]
}
```

#### 错误码

无

### 订单明细
接口权限：/store/order/detail

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_order/detail/{id}


描述：订单明细
接口权限：/store/order/detail

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### Path参数

| 名称 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- |
| id | 是 | No comments found. |  |

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | No comments found. |  |
| userId | string | 否 | - | 用户ID |  |
| orderId | string | 否 | - | 订单ID |  |
| productId | string | 否 | - | 商品ID |  |
| productName | string | 否 | - | 商品名称 |  |
| productImage | string | 否 | - | 商品图片 |  |
| productSkuId | string | 否 | - | 商品SKU ID |  |
| productSkuName | string | 否 | - | 商品SKU 名称 |  |
| price | int64 | 否 | - | 商品单价（单位：分） | 0 |
| quantity | int32 | 否 | - | 数量 | 0 |
| totalAmount | int64 | 否 | - | 小计金额（单位：分） | 0 |

#### 响应示例

```
{
    "id": "",
    "userId": "",
    "orderId": "",
    "productId": "",
    "productName": "",
    "productImage": "",
    "productSkuId": "",
    "productSkuName": "",
    "price": 0,
    "quantity": 0,
    "totalAmount": 0
}
```

#### 错误码

无

### 订单发货
接口权限：/store/order/ship

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/adm_order/ship


描述：订单发货
接口权限：/store/order/ship

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | 订单id |  |
| companyName | string | 否 | - | 物流公司名称 |  |
| trackingNumber | string | 否 | - | 运单号 |  |

#### 请求示例

```
{
    "id": "",
    "companyName": "",
    "trackingNumber": ""
}
```

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
