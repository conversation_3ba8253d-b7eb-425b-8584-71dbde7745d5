<!--
  运费模板管理页面
  提供运费模板的CRUD功能
-->

<route lang="yaml">
meta:
  title: 运费模板管理
  icon: 'ep:truck'
</route>

<script setup lang="ts">
import { Plus, Refresh } from '@element-plus/icons-vue'
import { ElButton, ElCard, ElSpace } from 'element-plus'
import { onMounted, ref } from 'vue'
import ShippingTemplateForm from '../components/ShippingTemplateForm.vue'
import ShippingTemplateTable from '../components/ShippingTemplateTable.vue'
import { useShippingTemplateList } from '../composables/useShippingTemplateList'

defineOptions({
  name: 'ShippingTemplateList',
})

// 使用运费模板列表管理
const {
  templateList,
  loading,
  total,
  searchParams,
  fetchTemplateList,
  refreshList,
  handleSearch,
  handleReset,
  handleSizeChange,
  handleCurrentChange,
  handleDelete,
} = useShippingTemplateList()

// 表单相关
const showTemplateForm = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentTemplate = ref()

// ==================== 生命周期 ====================

onMounted(() => {
  fetchTemplateList()
})

// ==================== 事件处理 ====================

/**
 * 新增模板
 */
function handleCreate() {
  currentTemplate.value = undefined
  formMode.value = 'create'
  showTemplateForm.value = true
}

/**
 * 编辑模板
 */
function handleEdit(template: any) {
  currentTemplate.value = template
  formMode.value = 'edit'
  showTemplateForm.value = true
}

/**
 * 表单提交成功
 */
function handleFormSuccess() {
  showTemplateForm.value = false
  refreshList()
}
</script>

<template>
  <div class="shipping-template-list">
    <!-- 操作栏 -->
    <ElCard class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <ElSpace>
            <ElButton
              type="primary"
              :icon="Plus"
              @click="handleCreate"
            >
              新增模板
            </ElButton>

            <ElButton
              :icon="Refresh"
              :loading="loading"
              @click="refreshList"
            >
              刷新
            </ElButton>
          </ElSpace>
        </div>
      </div>
    </ElCard>

    <!-- 运费模板表格 -->
    <ElCard class="table-card" shadow="never">
      <ShippingTemplateTable
        :data="templateList"
        :loading="loading"
        :total="total"
        :current-page="searchParams.page"
        :page-size="searchParams.size"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </ElCard>

    <!-- 运费模板表单弹窗 -->
    <ShippingTemplateForm
      v-if="showTemplateForm"
      v-model:visible="showTemplateForm"
      :mode="formMode"
      :template="currentTemplate"
      @success="handleFormSuccess"
    />
  </div>
</template>

<style scoped>
.shipping-template-list {
  padding: 16px;
}

.toolbar-card,
.table-card {
  margin-bottom: 16px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
