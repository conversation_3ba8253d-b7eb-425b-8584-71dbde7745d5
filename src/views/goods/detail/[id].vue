<!--
  商品详情页面
  展示完整的商品信息
-->

<route lang="yaml">
meta:
  title: 商品详情
</route>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ElCard, 
  ElButton, 
  ElDescriptions, 
  ElDescriptionsItem,
  ElTag,
  ElImage,
  ElTable,
  ElTableColumn,
  ElSpace,
  ElDivider,
  ElSkeleton,
  ElAlert
} from 'element-plus'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'
import { getProductDetail } from '@/api/modules/goods'
import type { Product } from '@/views/goods/types'
import { 
  getOptionLabel, 
  getStatusColor, 
  formatPrice, 
  formatTimestamp,
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_STATUS_OPTIONS,
  PRODUCT_SOURCE_OPTIONS,
  SPEC_TYPE_OPTIONS,
  DELIVERY_METHOD_OPTIONS
} from '@/views/goods/constants'

defineOptions({
  name: 'ProductDetail',
})

const route = useRoute()
const router = useRouter()

// ==================== 响应式数据 ====================

const loading = ref(false)
const product = ref<Product>()
const error = ref('')

// ==================== 计算属性 ====================

const productId = computed(() => route.params.id as string)

const hasSkuList = computed(() => product.value?.skuList && product.value.skuList.length > 0)

const totalStock = computed(() => {
  if (!hasSkuList.value) return 0
  return product.value!.skuList.reduce((total, sku) => total + (sku.inventory?.stock || 0), 0)
})

const totalSales = computed(() => {
  if (!hasSkuList.value) return 0
  return product.value!.skuList.reduce((total, sku) => total + (sku.inventory?.salesCount || 0), 0)
})

// ==================== 生命周期 ====================

onMounted(() => {
  fetchProductDetail()
})

// ==================== 方法 ====================

/**
 * 获取商品详情
 */
const fetchProductDetail = async () => {
  if (!productId.value) {
    error.value = '商品ID不能为空'
    return
  }

  try {
    loading.value = true
    error.value = ''
    
    const response = await getProductDetail(productId.value)
    product.value = response.data
    
  } catch (err) {
    console.error('获取商品详情失败:', err)
    error.value = '获取商品详情失败，请重试'
  } finally {
    loading.value = false
  }
}

/**
 * 返回列表
 */
const handleBack = () => {
  router.back()
}

/**
 * 编辑商品
 */
const handleEdit = () => {
  router.push(`/goods/edit/${productId.value}`)
}

/**
 * 获取主图片
 */
const getMainImage = (): string => {
  if (!product.value) return ''
  return product.value.mainImage || (product.value.images && product.value.images[0]) || ''
}

/**
 * 获取默认SKU价格
 */
const getDefaultPrice = (): string => {
  if (!hasSkuList.value) return '-'
  const defaultSku = product.value!.skuList.find(sku => sku.default) || product.value!.skuList[0]
  return defaultSku ? formatPrice(defaultSku.price) : '-'
}
</script>

<template>
  <div class="product-detail">
    <!-- 头部操作栏 -->
    <ElCard class="header-card" shadow="never">
      <div class="header-toolbar">
        <div class="header-left">
          <ElButton :icon="ArrowLeft" @click="handleBack">
            返回
          </ElButton>
          <ElDivider direction="vertical" />
          <span class="page-title">商品详情</span>
        </div>
        
        <div class="header-right">
          <ElButton
            v-if="product"
            type="primary"
            :icon="Edit"
            @click="handleEdit"
          >
            编辑商品
          </ElButton>
        </div>
      </div>
    </ElCard>

    <!-- 错误提示 -->
    <ElAlert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <!-- 加载骨架屏 -->
    <template v-if="loading">
      <ElCard class="detail-card" shadow="never">
        <ElSkeleton :rows="10" animated />
      </ElCard>
    </template>

    <!-- 商品详情内容 -->
    <template v-else-if="product">
      <!-- 基本信息 -->
      <ElCard class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">基本信息</span>
        </template>
        
        <div class="product-basic-info">
          <div class="product-image-section">
            <ElImage
              :src="getMainImage()"
              :preview-src-list="product.images"
              class="main-image"
              fit="cover"
            >
              <template #error>
                <div class="image-slot">
                  <span>暂无图片</span>
                </div>
              </template>
            </ElImage>
          </div>
          
          <div class="product-info-section">
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="商品ID">
                {{ product.id }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="商品名称">
                {{ product.name }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="商品类型">
                <ElTag size="small">
                  {{ getOptionLabel(PRODUCT_TYPE_OPTIONS, product.productType) }}
                </ElTag>
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="商品状态">
                <ElTag :type="getStatusColor(product.status)" size="small">
                  {{ getOptionLabel(PRODUCT_STATUS_OPTIONS, product.status) }}
                </ElTag>
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="商品来源">
                {{ getOptionLabel(PRODUCT_SOURCE_OPTIONS, product.source) }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="规格类型">
                {{ getOptionLabel(SPEC_TYPE_OPTIONS, product.specType) }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="配送方式">
                {{ getOptionLabel(DELIVERY_METHOD_OPTIONS, product.deliveryMethod) }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="分类ID">
                {{ product.categoryId || '-' }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="默认价格">
                <span class="price">¥{{ getDefaultPrice() }}</span>
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="总库存">
                <span :class="{ 'low-stock': totalStock < 10 }">
                  {{ totalStock }}
                </span>
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="总销量">
                {{ product.salesCount || 0 }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="排序">
                {{ product.sortOrder || 0 }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="创建时间">
                {{ formatTimestamp(product.createAt) }}
              </ElDescriptionsItem>
              
              <ElDescriptionsItem label="更新时间">
                {{ formatTimestamp(product.updateAt) }}
              </ElDescriptionsItem>
            </ElDescriptions>
          </div>
        </div>
        
        <!-- 商品简介 -->
        <div v-if="product.description" class="product-description">
          <h4>商品简介</h4>
          <p>{{ product.description }}</p>
        </div>
      </ElCard>

      <!-- SKU列表 -->
      <ElCard v-if="hasSkuList" class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">SKU列表</span>
        </template>
        
        <ElTable :data="product.skuList" border stripe>
          <ElTableColumn label="SKU名称" prop="name" min-width="150" />
          
          <ElTableColumn label="价格(元)" width="120" align="center">
            <template #default="{ row }">
              <span class="price">¥{{ formatPrice(row.price) }}</span>
            </template>
          </ElTableColumn>
          
          <ElTableColumn label="会员价(元)" width="120" align="center">
            <template #default="{ row }">
              <span class="member-price">¥{{ formatPrice(row.memberPrice) }}</span>
            </template>
          </ElTableColumn>
          
          <ElTableColumn label="原价(元)" width="120" align="center">
            <template #default="{ row }">
              ¥{{ formatPrice(row.originalPrice) }}
            </template>
          </ElTableColumn>
          
          <ElTableColumn label="库存" width="100" align="center">
            <template #default="{ row }">
              <span :class="{ 'low-stock': (row.inventory?.stock || 0) < 10 }">
                {{ row.inventory?.stock || 0 }}
              </span>
            </template>
          </ElTableColumn>
          
          <ElTableColumn label="销量" width="100" align="center">
            <template #default="{ row }">
              {{ row.inventory?.salesCount || 0 }}
            </template>
          </ElTableColumn>
          
          <ElTableColumn label="状态" width="100" align="center">
            <template #default="{ row }">
              <ElTag :type="row.visible ? 'success' : 'info'" size="small">
                {{ row.visible ? '显示' : '隐藏' }}
              </ElTag>
            </template>
          </ElTableColumn>
          
          <ElTableColumn label="默认" width="80" align="center">
            <template #default="{ row }">
              <ElTag v-if="row.default" type="warning" size="small">
                默认
              </ElTag>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElCard>

      <!-- 商品详情 -->
      <ElCard v-if="product.detail" class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">商品详情</span>
        </template>
        
        <div class="product-detail-content" v-html="product.detail" />
      </ElCard>
    </template>
  </div>
</template>

<style scoped>
.product-detail {
  padding: 16px;
}

.header-card,
.detail-card {
  margin-bottom: 16px;
}

.error-alert {
  margin-bottom: 16px;
}

.header-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  margin-left: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.product-basic-info {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.product-image-section {
  flex-shrink: 0;
}

.main-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.product-info-section {
  flex: 1;
}

.product-description {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
}

.product-description h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.product-description p {
  margin: 0;
  line-height: 1.6;
  color: var(--el-text-color-regular);
}

.price {
  font-weight: 500;
  color: var(--el-color-danger);
}

.member-price {
  font-weight: 500;
  color: var(--el-color-warning);
}

.low-stock {
  color: var(--el-color-warning);
  font-weight: 500;
}

.product-detail-content {
  line-height: 1.6;
  color: var(--el-text-color-regular);
}

.product-detail-content :deep(img) {
  max-width: 100%;
  height: auto;
}
</style>
