/**
 * 商品管理模块常量定义
 * 包含枚举选项、配置常量等
 */

import { ProductType, ProductStatus, ProductSource, SpecType, DeliveryMethod } from '../types'

// ==================== 选项配置 ====================

/** 商品类型选项 */
export const PRODUCT_TYPE_OPTIONS = [
  { label: '实物商品', value: ProductType.PHYSICAL },
  { label: '虚拟商品', value: ProductType.VIRTUAL },
  { label: '服务商品', value: ProductType.SERVICE },
]

/** 商品状态选项 */
export const PRODUCT_STATUS_OPTIONS = [
  { label: '草稿', value: ProductStatus.DRAFT, color: 'default' },
  { label: '上架', value: ProductStatus.ACTIVE, color: 'success' },
  { label: '下架', value: ProductStatus.INACTIVE, color: 'warning' },
  { label: '已删除', value: ProductStatus.DELETED, color: 'danger' },
]

/** 商品来源选项 */
export const PRODUCT_SOURCE_OPTIONS = [
  { label: '自营', value: ProductSource.SELF },
  { label: '第三方', value: ProductSource.THIRD_PARTY },
]

/** 规格类型选项 */
export const SPEC_TYPE_OPTIONS = [
  { label: '单规格', value: SpecType.SINGLE },
  { label: '多规格', value: SpecType.MULTIPLE },
]

/** 配送方式选项 */
export const DELIVERY_METHOD_OPTIONS = [
  { label: '商家配送', value: DeliveryMethod.MERCHANT_DELIVERY },
  { label: '快递配送', value: DeliveryMethod.EXPRESS_DELIVERY },
  { label: '自提', value: DeliveryMethod.SELF_PICKUP },
]

/** 排序字段选项 */
export const SORT_FIELD_OPTIONS = [
  { label: '创建时间', value: 'createAt' },
  { label: '销量', value: 'salesCount' },
  { label: '价格', value: 'price' },
  { label: '库存', value: 'stock' },
]

/** 排序方向选项 */
export const SORT_DIRECTION_OPTIONS = [
  { label: '升序', value: 'asc' },
  { label: '降序', value: 'desc' },
]

// ==================== 配置常量 ====================

/** 默认分页大小 */
export const DEFAULT_PAGE_SIZE = 20

/** 分页大小选项 */
export const PAGE_SIZE_OPTIONS = [10, 20, 50, 100]

/** 图片上传限制 */
export const IMAGE_UPLOAD_CONFIG = {
  maxSize: 5 * 1024 * 1024, // 5MB
  acceptTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  maxCount: 10, // 最多上传10张图片
}

/** 商品名称长度限制 */
export const PRODUCT_NAME_MAX_LENGTH = 100

/** 商品描述长度限制 */
export const PRODUCT_DESCRIPTION_MAX_LENGTH = 500

/** 商品详情长度限制 */
export const PRODUCT_DETAIL_MAX_LENGTH = 10000

/** 价格范围限制（分） */
export const PRICE_RANGE = {
  min: 1,        // 最低1分
  max: 99999999, // 最高999999.99元
}

/** 库存范围限制 */
export const STOCK_RANGE = {
  min: 0,
  max: 999999,
}

/** 重量范围限制（kg） */
export const WEIGHT_RANGE = {
  min: 0,
  max: 99999,
}

/** 体积范围限制（立方米） */
export const VOLUME_RANGE = {
  min: 0,
  max: 999,
}

// ==================== 工具函数 ====================

/**
 * 根据值获取选项标签
 * @param options 选项数组
 * @param value 值
 * @returns 标签
 */
export function getOptionLabel(options: Array<{ label: string; value: any }>, value: any): string {
  const option = options.find(item => item.value === value)
  return option?.label || String(value)
}

/**
 * 根据状态获取颜色
 * @param status 状态值
 * @returns 颜色
 */
export function getStatusColor(status: ProductStatus): string {
  const option = PRODUCT_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

/**
 * 格式化价格（分转元）
 * @param price 价格（分）
 * @returns 格式化后的价格字符串
 */
export function formatPrice(price: number): string {
  return (price / 100).toFixed(2)
}

/**
 * 解析价格（元转分）
 * @param priceStr 价格字符串
 * @returns 价格（分）
 */
export function parsePrice(priceStr: string): number {
  return Math.round(parseFloat(priceStr) * 100)
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
export function formatTimestamp(timestamp: number): string {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 验证商品名称
 * @param name 商品名称
 * @returns 验证结果
 */
export function validateProductName(name: string): { valid: boolean; message?: string } {
  if (!name || name.trim().length === 0) {
    return { valid: false, message: '商品名称不能为空' }
  }
  if (name.length > PRODUCT_NAME_MAX_LENGTH) {
    return { valid: false, message: `商品名称不能超过${PRODUCT_NAME_MAX_LENGTH}个字符` }
  }
  return { valid: true }
}

/**
 * 验证价格
 * @param price 价格（分）
 * @returns 验证结果
 */
export function validatePrice(price: number): { valid: boolean; message?: string } {
  if (price < PRICE_RANGE.min) {
    return { valid: false, message: `价格不能低于${formatPrice(PRICE_RANGE.min)}元` }
  }
  if (price > PRICE_RANGE.max) {
    return { valid: false, message: `价格不能超过${formatPrice(PRICE_RANGE.max)}元` }
  }
  return { valid: true }
}

/**
 * 验证库存
 * @param stock 库存数量
 * @returns 验证结果
 */
export function validateStock(stock: number): { valid: boolean; message?: string } {
  if (stock < STOCK_RANGE.min) {
    return { valid: false, message: `库存不能低于${STOCK_RANGE.min}` }
  }
  if (stock > STOCK_RANGE.max) {
    return { valid: false, message: `库存不能超过${STOCK_RANGE.max}` }
  }
  return { valid: true }
}
