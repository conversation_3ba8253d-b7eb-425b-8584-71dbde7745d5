/**
 * 商品列表管理 Composable
 * 提供商品列表数据管理、搜索、分页、批量操作等功能
 */

import type {
  BatchUpdateProductStatusParams,
  Product,
  ProductSearchParams,
  UpdateProductStatusParams,
} from '@/views/goods/types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, reactive, ref } from 'vue'
import {
  batchUpdateProductStatus,
  deleteProduct,
  getProductList,
  searchProducts,
  updateProductStatus,
} from '@/api/modules/goods'
import { DEFAULT_PAGE_SIZE } from '@/views/goods/constants'

export function useProductList() {
  // ==================== 响应式状态 ====================

  const productList = ref<Product[]>([])
  const loading = ref(false)
  const total = ref(0)
  const selectedIds = ref<string[]>([])

  // 搜索参数
  const searchParams = reactive<ProductSearchParams>({
    page: 1,
    size: DEFAULT_PAGE_SIZE,
    name: '',
    productType: undefined,
    categoryId: '',
    status: undefined,
    source: undefined,
    sortField: 'createAt',
    sortDirection: 'desc',
  })

  // ==================== 计算属性 ====================

  const hasSelected = computed(() => selectedIds.value.length > 0)
  const isSingleSelected = computed(() => selectedIds.value.length === 1)
  const isMultipleSelected = computed(() => selectedIds.value.length > 1)
  const hasData = computed(() => productList.value.length > 0)

  // ==================== 数据获取 ====================

  /**
   * 获取商品列表
   */
  const fetchProductList = async (useSearch = false): Promise<void> => {
    try {
      loading.value = true

      // 清理空值参数
      const params = Object.fromEntries(
        Object.entries(searchParams).filter(([_, value]) =>
          value !== undefined && value !== '' && value !== null,
        ),
      )

      const response = useSearch
        ? await searchProducts(params)
        : await getProductList(params)

      const data = response.data
      productList.value = data.content || []
      total.value = data.total || 0
    }
    catch (error) {
      console.error('获取商品列表失败:', error)
      ElMessage.error('获取商品列表失败')
      productList.value = []
      total.value = 0
    }
    finally {
      loading.value = false
    }
  }

  /**
   * 刷新列表
   */
  const refreshList = () => {
    selectedIds.value = []
    return fetchProductList()
  }

  /**
   * 搜索商品
   */
  const handleSearch = () => {
    searchParams.page = 1
    selectedIds.value = []
    return fetchProductList(true)
  }

  /**
   * 重置搜索
   */
  const handleReset = () => {
    Object.assign(searchParams, {
      page: 1,
      size: DEFAULT_PAGE_SIZE,
      name: '',
      productType: undefined,
      categoryId: '',
      status: undefined,
      source: undefined,
      sortField: 'createAt',
      sortDirection: 'desc',
    })
    selectedIds.value = []
    return fetchProductList()
  }

  // ==================== 分页处理 ====================

  /**
   * 页面大小变化
   */
  const handleSizeChange = (size: number) => {
    searchParams.size = size
    searchParams.page = 1
    return fetchProductList()
  }

  /**
   * 当前页变化
   */
  const handleCurrentChange = (page: number) => {
    searchParams.page = page
    return fetchProductList()
  }

  // ==================== 选择处理 ====================

  /**
   * 选择变化
   */
  const handleSelectionChange = (selection: Product[]) => {
    selectedIds.value = selection.map(item => item.id)
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedIds.value = []
  }

  // ==================== 状态管理 ====================

  /**
   * 更新商品状态
   */
  const handleStatusChange = async (product: Product, status: number) => {
    try {
      const params: UpdateProductStatusParams = {
        id: product.id,
        status,
      }

      await updateProductStatus(params)

      // 更新本地数据
      const index = productList.value.findIndex(item => item.id === product.id)
      if (index !== -1) {
        productList.value[index].status = status
      }

      ElMessage.success('状态更新成功')
    }
    catch (error) {
      console.error('状态更新失败:', error)
      ElMessage.error('状态更新失败')
      throw error
    }
  }

  /**
   * 批量更新状态
   */
  const handleBatchStatusChange = async (status: number) => {
    if (!hasSelected.value) {
      ElMessage.warning('请先选择商品')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要批量更新选中的 ${selectedIds.value.length} 个商品的状态吗？`,
        '批量操作确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )

      const params: BatchUpdateProductStatusParams = {
        ids: selectedIds.value,
        status,
      }

      await batchUpdateProductStatus(params)

      // 更新本地数据
      productList.value.forEach((item) => {
        if (selectedIds.value.includes(item.id)) {
          item.status = status
        }
      })

      clearSelection()
      ElMessage.success('批量状态更新成功')
    }
    catch (error) {
      if (error !== 'cancel') {
        console.error('批量状态更新失败:', error)
        ElMessage.error('批量状态更新失败')
      }
    }
  }

  // ==================== 删除操作 ====================

  /**
   * 删除商品
   */
  const handleDelete = async (product: Product) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除商品"${product.name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )

      await deleteProduct(product.id)

      // 从列表中移除
      const index = productList.value.findIndex(item => item.id === product.id)
      if (index !== -1) {
        productList.value.splice(index, 1)
        total.value -= 1
      }

      // 如果当前页没有数据且不是第一页，则跳转到上一页
      if (productList.value.length === 0 && searchParams.page > 1) {
        searchParams.page -= 1
        await fetchProductList()
      }

      ElMessage.success('删除成功')
    }
    catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // ==================== 返回接口 ====================

  return {
    // 状态
    productList,
    loading,
    total,
    selectedIds,
    searchParams,

    // 计算属性
    hasSelected,
    isSingleSelected,
    isMultipleSelected,
    hasData,

    // 方法
    fetchProductList,
    refreshList,
    handleSearch,
    handleReset,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    clearSelection,
    handleStatusChange,
    handleBatchStatusChange,
    handleDelete,
  }
}
