/**
 * 运费模板列表管理 Composable
 * 提供运费模板数据管理、搜索、分页等功能
 */

import { computed, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  searchShippingTemplates, 
  deleteShippingTemplate 
} from '@/api/modules/goods'
import type { 
  ShippingTemplate, 
  ShippingTemplateSearchParams 
} from '@/views/goods/types'
import { DEFAULT_PAGE_SIZE } from '@/views/goods/constants'

export function useShippingTemplateList() {
  // ==================== 响应式状态 ====================
  
  const templateList = ref<ShippingTemplate[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 搜索参数
  const searchParams = reactive<ShippingTemplateSearchParams>({
    page: 1,
    size: DEFAULT_PAGE_SIZE,
    name: '',
  })

  // ==================== 计算属性 ====================
  
  const hasData = computed(() => templateList.value.length > 0)

  // ==================== 数据获取 ====================
  
  /**
   * 获取运费模板列表
   */
  const fetchTemplateList = async (): Promise<void> => {
    try {
      loading.value = true
      
      // 清理空值参数
      const params = Object.fromEntries(
        Object.entries(searchParams).filter(([_, value]) => 
          value !== undefined && value !== '' && value !== null
        )
      )

      const response = await searchShippingTemplates(params)
      const data = response.data
      
      templateList.value = data.content || []
      total.value = data.total || 0
      
    } catch (error) {
      console.error('获取运费模板列表失败:', error)
      ElMessage.error('获取运费模板列表失败')
      templateList.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  /**
   * 刷新列表
   */
  const refreshList = () => {
    return fetchTemplateList()
  }

  /**
   * 搜索模板
   */
  const handleSearch = () => {
    searchParams.page = 1
    return fetchTemplateList()
  }

  /**
   * 重置搜索
   */
  const handleReset = () => {
    Object.assign(searchParams, {
      page: 1,
      size: DEFAULT_PAGE_SIZE,
      name: '',
    })
    return fetchTemplateList()
  }

  // ==================== 分页处理 ====================
  
  /**
   * 页面大小变化
   */
  const handleSizeChange = (size: number) => {
    searchParams.size = size
    searchParams.page = 1
    return fetchTemplateList()
  }

  /**
   * 当前页变化
   */
  const handleCurrentChange = (page: number) => {
    searchParams.page = page
    return fetchTemplateList()
  }

  // ==================== 删除操作 ====================
  
  /**
   * 删除运费模板
   */
  const handleDelete = async (template: ShippingTemplate) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除运费模板"${template.name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      await deleteShippingTemplate(template.id)
      
      // 从列表中移除
      const index = templateList.value.findIndex(item => item.id === template.id)
      if (index !== -1) {
        templateList.value.splice(index, 1)
        total.value -= 1
      }
      
      // 如果当前页没有数据且不是第一页，则跳转到上一页
      if (templateList.value.length === 0 && searchParams.page > 1) {
        searchParams.page -= 1
        await fetchTemplateList()
      }
      
      ElMessage.success('删除成功')
      
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // ==================== 返回接口 ====================
  
  return {
    // 状态
    templateList,
    loading,
    total,
    searchParams,
    
    // 计算属性
    hasData,
    
    // 方法
    fetchTemplateList,
    refreshList,
    handleSearch,
    handleReset,
    handleSizeChange,
    handleCurrentChange,
    handleDelete,
  }
}
