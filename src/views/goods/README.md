# 商品管理模块

基于接口文档完整实现的商品管理系统，包含商品CRUD、运费模板管理等功能。

## 功能特性

### 🛍️ 商品管理
- **商品列表** - 支持分页、搜索、筛选、排序
- **商品详情** - 完整展示商品信息、SKU列表、库存等
- **商品表单** - 支持新增/编辑商品，包含基本信息、图片、SKU、详情等
- **状态管理** - 支持单个/批量上下架操作
- **删除功能** - 支持商品删除操作

### 🚚 运费模板管理
- **模板列表** - 运费模板的分页展示
- **模板表单** - 新增/编辑运费模板
- **模板删除** - 删除不需要的运费模板

### 📊 数据统计
- **库存统计** - 实时显示商品库存状态
- **销量统计** - 展示商品销售数据
- **分类统计** - 按分类统计商品数量

## 技术架构

### 目录结构
```
src/views/goods/
├── components/           # 组件目录
│   ├── ProductSearchForm.vue      # 商品搜索表单
│   ├── ProductTable.vue           # 商品表格
│   ├── ProductForm.vue            # 商品表单
│   ├── ShippingTemplateTable.vue  # 运费模板表格
│   └── ShippingTemplateForm.vue   # 运费模板表单
├── composables/          # 组合式函数
│   ├── useProductList.ts           # 商品列表管理
│   └── useShippingTemplateList.ts # 运费模板列表管理
├── constants/            # 常量定义
│   └── index.ts                   # 枚举选项、配置常量、工具函数
├── types/               # 类型定义
│   └── index.ts                   # 完整的TypeScript类型系统
├── detail/              # 详情页面
│   └── [id].vue                   # 商品详情页
├── shipping/            # 运费模板页面
│   └── index.vue                  # 运费模板列表页
├── index.vue            # 商品列表页面
└── README.md            # 模块文档
```

### API服务
```
src/api/modules/goods/
├── product.ts           # 商品相关API
├── shipping.ts          # 运费模板相关API
└── index.ts            # API模块入口
```

### 路由配置
```
src/router/modules/goods.ts  # 商品管理路由配置
```

## 核心功能实现

### 1. 类型系统
- 完整的TypeScript类型定义
- 基于接口文档的准确类型映射
- 支持枚举类型和联合类型
- 提供类型守卫和工具类型

### 2. API服务
- 基于Axios的HTTP客户端
- 统一的错误处理机制
- 支持请求/响应拦截器
- 类型安全的API调用

### 3. 状态管理
- 使用Vue 3 Composition API
- 响应式数据管理
- 计算属性和监听器
- 生命周期钩子集成

### 4. 组件设计
- 高度可复用的组件架构
- Props/Emits类型定义
- 插槽和作用域插槽支持
- 组件间通信机制

### 5. 表单处理
- Element Plus表单组件
- 表单验证规则
- 动态表单字段
- 文件上传支持（待实现）

### 6. 数据展示
- 表格组件封装
- 分页功能实现
- 搜索和筛选
- 排序功能

## 接口对接

### 商品管理接口
- `POST /adm_product/list` - 商品列表
- `POST /adm_product/search` - 商品搜索
- `POST /adm_product/detail/{id}` - 商品详情
- `POST /adm_product/add` - 新增商品
- `POST /adm_product/update` - 更新商品
- `POST /adm_product/updateStatus` - 更新商品状态
- `POST /adm_product/batchUpdateStatus` - 批量更新状态
- `POST /adm_product/delete/{id}` - 删除商品
- `POST /adm_product/countByCategory/{id}` - 分类统计

### 运费模板接口
- `POST /adm_product/createShippingFeeTpl` - 创建/编辑模板
- `POST /adm_product/searchShippingFeeTpl` - 搜索模板
- `POST /adm_product/getAllShippingFeeTpl` - 获取所有模板
- `POST /adm_product/getShippingFeeTplDetail/{id}` - 模板详情
- `POST /adm_product/deleteShippingFeeTpl/{id}` - 删除模板

## 数据模型

### 商品模型
```typescript
interface Product {
  id: string
  name: string
  productType: ProductType
  categoryId: string
  skuList: ProductSku[]
  status: ProductStatus
  source: ProductSource
  mainImage: string
  images: string[]
  detail: string
  salesCount: number
  // ... 更多字段
}
```

### SKU模型
```typescript
interface ProductSku {
  id: string
  productId: string
  name: string
  paramList: SkuParam[]
  price: number
  memberPrice: number
  inventory: Inventory
  // ... 更多字段
}
```

### 运费模板模型
```typescript
interface ShippingTemplate {
  id: string
  name: string
  area: string
  shippingFee: number
  remark: string
  sortOrder: number
  // ... 更多字段
}
```

## 使用说明

### 1. 商品列表页面
- 访问 `/goods/list` 查看商品列表
- 支持按名称、类型、状态等条件搜索
- 支持批量操作（上架、下架）
- 点击操作按钮进行查看、编辑、删除

### 2. 商品详情页面
- 访问 `/goods/detail/:id` 查看商品详情
- 展示完整的商品信息和SKU列表
- 支持直接跳转到编辑页面

### 3. 运费模板管理
- 访问 `/goods/shipping` 管理运费模板
- 支持新增、编辑、删除运费模板
- 按地区和费用进行管理

## 待完善功能

### 1. 图片上传
- 商品主图上传
- 商品轮播图上传
- 图片预览和管理

### 2. SKU管理
- 动态SKU规格配置
- 批量SKU操作
- SKU库存管理

### 3. 富文本编辑
- 商品详情富文本编辑器
- 图片插入和管理
- 内容格式化

### 4. 数据导入导出
- Excel批量导入商品
- 商品数据导出
- 模板下载

### 5. 权限控制
- 基于角色的权限管理
- 操作权限控制
- 数据权限隔离

## 开发规范

### 1. 代码规范
- 使用TypeScript严格模式
- 遵循Vue 3 Composition API最佳实践
- 统一的命名规范和代码格式

### 2. 组件规范
- 单一职责原则
- Props类型定义
- 事件命名规范
- 样式作用域

### 3. API规范
- 统一的错误处理
- 请求参数类型检查
- 响应数据类型定义
- 接口文档同步

### 4. 测试规范
- 单元测试覆盖
- 组件测试
- API测试
- E2E测试

## 性能优化

### 1. 代码分割
- 路由级别的代码分割
- 组件懒加载
- 第三方库按需引入

### 2. 缓存策略
- 列表数据缓存
- 图片资源缓存
- API响应缓存

### 3. 虚拟滚动
- 大数据量表格优化
- 无限滚动加载
- 分页性能优化

## 部署说明

### 1. 环境配置
- 开发环境配置
- 测试环境配置
- 生产环境配置

### 2. 构建优化
- 代码压缩
- 资源优化
- 缓存策略

### 3. 监控告警
- 错误监控
- 性能监控
- 用户行为分析
