<!--
  商品表单组件
  支持新增、编辑、查看商品信息
-->

<script setup lang="ts">
import type { CreateProductParams, Product, UpdateProductParams } from '@/views/goods/types'
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
  ElTabPane,
  ElTabs,
} from 'element-plus'
import { computed, nextTick, ref, watch } from 'vue'
import { createProduct, updateProduct } from '@/api/modules/goods'
import {
  DELIVERY_METHOD_OPTIONS,
  PRODUCT_SOURCE_OPTIONS,
  PRODUCT_STATUS_OPTIONS,
  PRODUCT_TYPE_OPTIONS,
  SPEC_TYPE_OPTIONS,
} from '@/views/goods/constants'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  mode: 'create' | 'edit' | 'view'
  product?: Product
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  product: undefined,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const formRef = ref()
const loading = ref(false)
const activeTab = ref('basic')

// 表单数据
const formData = ref<CreateProductParams | UpdateProductParams>({
  name: '',
  productType: 0,
  categoryId: '',
  status: 1,
  source: 0,
  specType: 0,
  deliveryMethod: 0,
  mainImage: '',
  images: [],
  detail: '',
  description: '',
  sortOrder: 0,
  skuList: [],
  checkOrderFormIdList: [],
  externalId: '',
  initialSalesCount: 0,
})

// ==================== 计算属性 ====================

const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '新增商品'
    case 'edit':
      return '编辑商品'
    case 'view':
      return '查看商品'
    default:
      return '商品信息'
  }
})

const isReadonly = computed(() => props.mode === 'view')

// 表单验证规则
const formRules = computed(() => ({
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 1, max: 100, message: '商品名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  productType: [
    { required: true, message: '请选择商品类型', trigger: 'change' },
  ],
  categoryId: [
    { required: true, message: '请输入商品分类ID', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择商品状态', trigger: 'change' },
  ],
  source: [
    { required: true, message: '请选择商品来源', trigger: 'change' },
  ],
  specType: [
    { required: true, message: '请选择规格类型', trigger: 'change' },
  ],
  deliveryMethod: [
    { required: true, message: '请选择配送方式', trigger: 'change' },
  ],
}))

// ==================== 监听器 ====================

watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      initFormData()
    }
  },
  { immediate: true },
)

// ==================== 方法 ====================

/**
 * 初始化表单数据
 */
function initFormData() {
  if (props.mode === 'edit' && props.product) {
    // 编辑模式，填充现有数据
    formData.value = {
      ...props.product,
      id: props.product.id,
    }
  }
  else if (props.mode === 'view' && props.product) {
    // 查看模式，填充现有数据
    formData.value = { ...props.product }
  }
  else {
    // 新增模式，重置表单
    formData.value = {
      name: '',
      productType: 0,
      categoryId: '',
      status: 1,
      source: 0,
      specType: 0,
      deliveryMethod: 0,
      mainImage: '',
      images: [],
      detail: '',
      description: '',
      sortOrder: 0,
      skuList: [],
      checkOrderFormIdList: [],
      externalId: '',
      initialSalesCount: 0,
    }
  }

  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/**
 * 提交表单
 */
async function handleSubmit() {
  if (isReadonly.value) {
    dialogVisible.value = false
    return
  }

  try {
    await formRef.value?.validate()
    loading.value = true

    if (props.mode === 'create') {
      await createProduct(formData.value as CreateProductParams)
      ElMessage.success('商品创建成功')
    }
    else if (props.mode === 'edit') {
      await updateProduct(formData.value as UpdateProductParams)
      ElMessage.success('商品更新成功')
    }

    dialogVisible.value = false
    emit('success')
  }
  catch (error) {
    console.error('表单提交失败:', error)
    if (error !== 'validation failed') {
      ElMessage.error('操作失败，请重试')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
function handleCancel() {
  dialogVisible.value = false
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :disabled="isReadonly"
      label-width="120px"
    >
      <ElTabs v-model="activeTab">
        <!-- 基本信息 -->
        <ElTabPane label="基本信息" name="basic">
          <ElFormItem label="商品名称" prop="name">
            <ElInput
              v-model="formData.name"
              placeholder="请输入商品名称"
              maxlength="100"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="商品类型" prop="productType">
            <ElSelect v-model="formData.productType" placeholder="请选择商品类型">
              <ElOption
                v-for="option in PRODUCT_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="商品分类" prop="categoryId">
            <ElInput
              v-model="formData.categoryId"
              placeholder="请输入商品分类ID"
            />
          </ElFormItem>

          <ElFormItem label="商品状态" prop="status">
            <ElSelect v-model="formData.status" placeholder="请选择商品状态">
              <ElOption
                v-for="option in PRODUCT_STATUS_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="商品来源" prop="source">
            <ElSelect v-model="formData.source" placeholder="请选择商品来源">
              <ElOption
                v-for="option in PRODUCT_SOURCE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="规格类型" prop="specType">
            <ElSelect v-model="formData.specType" placeholder="请选择规格类型">
              <ElOption
                v-for="option in SPEC_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="配送方式" prop="deliveryMethod">
            <ElSelect v-model="formData.deliveryMethod" placeholder="请选择配送方式">
              <ElOption
                v-for="option in DELIVERY_METHOD_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="商品简介" prop="description">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入商品简介"
              maxlength="500"
              show-word-limit
            />
          </ElFormItem>
        </ElTabPane>

        <!-- 商品图片 -->
        <ElTabPane label="商品图片" name="images">
          <!-- TODO: 实现图片上传组件 -->
          <div class="image-upload-placeholder">
            <p>图片上传功能待实现</p>
          </div>
        </ElTabPane>

        <!-- SKU管理 -->
        <ElTabPane label="SKU管理" name="sku">
          <!-- TODO: 实现SKU管理组件 -->
          <div class="sku-management-placeholder">
            <p>SKU管理功能待实现</p>
          </div>
        </ElTabPane>

        <!-- 商品详情 -->
        <ElTabPane label="商品详情" name="detail">
          <ElFormItem label="商品详情" prop="detail">
            <ElInput
              v-model="formData.detail"
              type="textarea"
              :rows="10"
              placeholder="请输入商品详情"
            />
          </ElFormItem>
        </ElTabPane>
      </ElTabs>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          {{ isReadonly ? '关闭' : '取消' }}
        </ElButton>
        <ElButton
          v-if="!isReadonly"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ props.mode === 'create' ? '创建' : '更新' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.image-upload-placeholder,
.sku-management-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color-light);
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
