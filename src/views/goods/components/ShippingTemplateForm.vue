<!--
  运费模板表单组件
  支持新增、编辑运费模板
-->

<script setup lang="ts">
import type { ShippingTemplate, ShippingTemplateParams } from '@/views/goods/types'
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
} from 'element-plus'
import { computed, nextTick, ref, watch } from 'vue'
import { createOrUpdateShippingTemplate } from '@/api/modules/goods'
import { formatPrice, parsePrice } from '@/views/goods/constants'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  mode: 'create' | 'edit'
  template?: ShippingTemplate
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  template: undefined,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref<ShippingTemplateParams>({
  name: '',
  area: '',
  shippingFee: 0,
  remark: '',
  sortOrder: 0,
})

// ==================== 计算属性 ====================

const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增运费模板' : '编辑运费模板'
})

// 表单验证规则
const formRules = computed(() => ({
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 50, message: '模板名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  area: [
    { required: true, message: '请输入适用地区', trigger: 'blur' },
    { min: 1, max: 200, message: '适用地区长度在 1 到 200 个字符', trigger: 'blur' },
  ],
  shippingFee: [
    { required: true, message: '请输入运费', trigger: 'blur' },
    { type: 'number', min: 0, message: '运费不能小于0', trigger: 'blur' },
  ],
  remark: [
    { max: 500, message: '备注长度不能超过 500 个字符', trigger: 'blur' },
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序值在 0 到 9999 之间', trigger: 'blur' },
  ],
}))

// ==================== 监听器 ====================

watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      initFormData()
    }
  },
  { immediate: true },
)

// ==================== 方法 ====================

/**
 * 初始化表单数据
 */
function initFormData() {
  if (props.mode === 'edit' && props.template) {
    // 编辑模式，填充现有数据
    formData.value = {
      id: props.template.id,
      name: props.template.name,
      area: props.template.area,
      shippingFee: props.template.shippingFee,
      remark: props.template.remark || '',
      sortOrder: props.template.sortOrder || 0,
    }
  }
  else {
    // 新增模式，重置表单
    formData.value = {
      name: '',
      area: '',
      shippingFee: 0,
      remark: '',
      sortOrder: 0,
    }
  }

  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    await formRef.value?.validate()
    loading.value = true

    await createOrUpdateShippingTemplate(formData.value)

    ElMessage.success(props.mode === 'create' ? '模板创建成功' : '模板更新成功')
    dialogVisible.value = false
    emit('success')
  }
  catch (error) {
    console.error('表单提交失败:', error)
    if (error !== 'validation failed') {
      ElMessage.error('操作失败，请重试')
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
function handleCancel() {
  dialogVisible.value = false
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <ElFormItem label="模板名称" prop="name">
        <ElInput
          v-model="formData.name"
          placeholder="请输入模板名称"
          maxlength="50"
          show-word-limit
        />
      </ElFormItem>

      <ElFormItem label="适用地区" prop="area">
        <ElInput
          v-model="formData.area"
          placeholder="请输入适用地区，如：全国、北京市、上海市等"
          maxlength="200"
          show-word-limit
        />
      </ElFormItem>

      <ElFormItem label="运费(分)" prop="shippingFee">
        <ElInputNumber
          v-model="formData.shippingFee"
          :min="0"
          :max="999999"
          :step="1"
          placeholder="请输入运费（单位：分）"
          style="width: 100%;"
        />
        <div class="form-tip">
          当前运费：¥{{ formatPrice(formData.shippingFee || 0) }}
        </div>
      </ElFormItem>

      <ElFormItem label="排序" prop="sortOrder">
        <ElInputNumber
          v-model="formData.sortOrder"
          :min="0"
          :max="9999"
          :step="1"
          placeholder="请输入排序值，数字越小越靠前"
          style="width: 100%;"
        />
      </ElFormItem>

      <ElFormItem label="备注" prop="remark">
        <ElInput
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="500"
          show-word-limit
        />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          取消
        </ElButton>
        <ElButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ props.mode === 'create' ? '创建' : '更新' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.dialog-footer {
  text-align: right;
}
</style>
