<!--
  商品表格组件
  展示商品列表数据，支持选择、操作等功能
-->

<script setup lang="ts">
import type { Product } from '@/views/goods/types'
import { Delete, Edit, View } from '@element-plus/icons-vue'
import {
  ElButton,
  ElImage,
  ElPagination,
  ElSpace,
  ElSwitch,
  ElTable,
  ElTableColumn,
  ElTag,
  ElTooltip,
} from 'element-plus'
import { computed } from 'vue'
import {
  formatPrice,
  formatTimestamp,
  getOptionLabel,
  getStatusColor,
  PAGE_SIZE_OPTIONS,
  PRODUCT_SOURCE_OPTIONS,
  PRODUCT_STATUS_OPTIONS,
  PRODUCT_TYPE_OPTIONS,
} from '@/views/goods/constants'

// ==================== Props & Emits ====================

interface Props {
  data: Product[]
  loading?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
}

interface Emits {
  (e: 'selection-change', selection: Product[]): void
  (e: 'size-change', size: number): void
  (e: 'current-change', page: number): void
  (e: 'view', product: Product): void
  (e: 'edit', product: Product): void
  (e: 'delete', product: Product): void
  (e: 'status-change', product: Product, status: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0,
  currentPage: 1,
  pageSize: 20,
})

const emit = defineEmits<Emits>()

// ==================== 计算属性 ====================

const tableData = computed(() => props.data)

// ==================== 事件处理 ====================

/**
 * 选择变化
 */
function handleSelectionChange(selection: Product[]) {
  emit('selection-change', selection)
}

/**
 * 页面大小变化
 */
function handleSizeChange(size: number) {
  emit('size-change', size)
}

/**
 * 当前页变化
 */
function handleCurrentChange(page: number) {
  emit('current-change', page)
}

/**
 * 查看详情
 */
function handleView(product: Product) {
  emit('view', product)
}

/**
 * 编辑商品
 */
function handleEdit(product: Product) {
  emit('edit', product)
}

/**
 * 删除商品
 */
function handleDelete(product: Product) {
  emit('delete', product)
}

/**
 * 状态切换
 */
function handleStatusChange(product: Product, status: boolean) {
  const newStatus = status ? 1 : 2 // 1: 上架, 2: 下架
  emit('status-change', product, newStatus)
}

/**
 * 获取主图片
 */
function getMainImage(product: Product): string {
  return product.mainImage || (product.images && product.images[0]) || ''
}

/**
 * 获取默认SKU价格
 */
function getDefaultPrice(product: Product): string {
  const defaultSku = product.skuList?.find(sku => sku.default) || product.skuList?.[0]
  return defaultSku ? formatPrice(defaultSku.price) : '-'
}

/**
 * 获取总库存
 */
function getTotalStock(product: Product): number {
  return product.skuList?.reduce((total, sku) => total + (sku.inventory?.stock || 0), 0) || 0
}
</script>

<template>
  <div class="product-table">
    <!-- 表格 -->
    <ElTable
      :data="tableData"
      :loading="loading"
      stripe
      border
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <ElTableColumn type="selection" width="55" align="center" />

      <!-- 商品信息 -->
      <ElTableColumn label="商品信息" min-width="300">
        <template #default="{ row }">
          <div class="product-info">
            <ElImage
              :src="getMainImage(row)"
              :preview-src-list="row.images"
              class="product-image"
              fit="cover"
              lazy
            >
              <template #error>
                <div class="image-slot">
                  <span>暂无图片</span>
                </div>
              </template>
            </ElImage>
            <div class="product-details">
              <div class="product-name">
                {{ row.name }}
              </div>
              <div class="product-id">
                ID: {{ row.id }}
              </div>
              <div class="product-description">
                {{ row.description || '-' }}
              </div>
            </div>
          </div>
        </template>
      </ElTableColumn>

      <!-- 商品类型 -->
      <ElTableColumn label="类型" width="100" align="center">
        <template #default="{ row }">
          <ElTag size="small">
            {{ getOptionLabel(PRODUCT_TYPE_OPTIONS, row.productType) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 价格 -->
      <ElTableColumn label="价格(元)" width="120" align="center">
        <template #default="{ row }">
          <span class="price">¥{{ getDefaultPrice(row) }}</span>
        </template>
      </ElTableColumn>

      <!-- 库存 -->
      <ElTableColumn label="库存" width="100" align="center">
        <template #default="{ row }">
          <span :class="{ 'low-stock': getTotalStock(row) < 10 }">
            {{ getTotalStock(row) }}
          </span>
        </template>
      </ElTableColumn>

      <!-- 销量 -->
      <ElTableColumn label="销量" width="100" align="center">
        <template #default="{ row }">
          {{ row.salesCount || 0 }}
        </template>
      </ElTableColumn>

      <!-- 状态 -->
      <ElTableColumn label="状态" width="120" align="center">
        <template #default="{ row }">
          <ElTag
            :type="getStatusColor(row.status)"
            size="small"
          >
            {{ getOptionLabel(PRODUCT_STATUS_OPTIONS, row.status) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <!-- 来源 -->
      <ElTableColumn label="来源" width="100" align="center">
        <template #default="{ row }">
          {{ getOptionLabel(PRODUCT_SOURCE_OPTIONS, row.source) }}
        </template>
      </ElTableColumn>

      <!-- 上架状态切换 -->
      <ElTableColumn label="上架" width="80" align="center">
        <template #default="{ row }">
          <ElSwitch
            :model-value="row.status === 1"
            :disabled="row.status === 3"
            @change="(value) => handleStatusChange(row, value)"
          />
        </template>
      </ElTableColumn>

      <!-- 创建时间 -->
      <ElTableColumn label="创建时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.createAt) }}
        </template>
      </ElTableColumn>

      <!-- 操作 -->
      <ElTableColumn label="操作" width="180" align="center" fixed="right">
        <template #default="{ row }">
          <ElSpace>
            <ElTooltip content="查看详情">
              <ElButton
                type="primary"
                :icon="View"
                size="small"
                circle
                @click="handleView(row)"
              />
            </ElTooltip>

            <ElTooltip content="编辑">
              <ElButton
                type="warning"
                :icon="Edit"
                size="small"
                circle
                @click="handleEdit(row)"
              />
            </ElTooltip>

            <ElTooltip content="删除">
              <ElButton
                type="danger"
                :icon="Delete"
                size="small"
                circle
                @click="handleDelete(row)"
              />
            </ElTooltip>
          </ElSpace>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <ElPagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="PAGE_SIZE_OPTIONS"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.product-table {
  width: 100%;
}

.product-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.product-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 4px;
}

.image-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background: var(--el-fill-color-light);
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.product-id {
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.product-description {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.price {
  font-weight: 500;
  color: var(--el-color-danger);
}

.low-stock {
  font-weight: 500;
  color: var(--el-color-warning);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
