<!--
  运费模板表格组件
  展示运费模板列表数据
-->

<script setup lang="ts">
import type { ShippingTemplate } from '@/views/goods/types'
import { Delete, Edit } from '@element-plus/icons-vue'
import {
  ElButton,
  ElPagination,
  ElSpace,
  ElTable,
  ElTableColumn,
  ElTooltip,
} from 'element-plus'
import { computed } from 'vue'
import { formatPrice, formatTimestamp, PAGE_SIZE_OPTIONS } from '@/views/goods/constants'

// ==================== Props & Emits ====================

interface Props {
  data: ShippingTemplate[]
  loading?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
}

interface Emits {
  (e: 'size-change', size: number): void
  (e: 'current-change', page: number): void
  (e: 'edit', template: ShippingTemplate): void
  (e: 'delete', template: ShippingTemplate): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0,
  currentPage: 1,
  pageSize: 20,
})

const emit = defineEmits<Emits>()

// ==================== 计算属性 ====================

const tableData = computed(() => props.data)

// ==================== 事件处理 ====================

/**
 * 页面大小变化
 */
function handleSizeChange(size: number) {
  emit('size-change', size)
}

/**
 * 当前页变化
 */
function handleCurrentChange(page: number) {
  emit('current-change', page)
}

/**
 * 编辑模板
 */
function handleEdit(template: ShippingTemplate) {
  emit('edit', template)
}

/**
 * 删除模板
 */
function handleDelete(template: ShippingTemplate) {
  emit('delete', template)
}
</script>

<template>
  <div class="shipping-template-table">
    <!-- 表格 -->
    <ElTable
      :data="tableData"
      :loading="loading"
      stripe
      border
    >
      <!-- 模板名称 -->
      <ElTableColumn label="模板名称" prop="name" min-width="150">
        <template #default="{ row }">
          <span class="template-name">{{ row.name }}</span>
        </template>
      </ElTableColumn>

      <!-- 适用地区 -->
      <ElTableColumn label="适用地区" prop="area" min-width="200">
        <template #default="{ row }">
          <span>{{ row.area || '-' }}</span>
        </template>
      </ElTableColumn>

      <!-- 运费 -->
      <ElTableColumn label="运费(元)" width="120" align="center">
        <template #default="{ row }">
          <span class="shipping-fee">¥{{ formatPrice(row.shippingFee) }}</span>
        </template>
      </ElTableColumn>

      <!-- 排序 -->
      <ElTableColumn label="排序" width="100" align="center">
        <template #default="{ row }">
          {{ row.sortOrder || 0 }}
        </template>
      </ElTableColumn>

      <!-- 备注 -->
      <ElTableColumn label="备注" prop="remark" min-width="200">
        <template #default="{ row }">
          <ElTooltip
            v-if="row.remark"
            :content="row.remark"
            placement="top"
          >
            <span class="remark-text">{{ row.remark }}</span>
          </ElTooltip>
          <span v-else>-</span>
        </template>
      </ElTableColumn>

      <!-- 创建时间 -->
      <ElTableColumn label="创建时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.createAt) }}
        </template>
      </ElTableColumn>

      <!-- 更新时间 -->
      <ElTableColumn label="更新时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatTimestamp(row.updateAt) }}
        </template>
      </ElTableColumn>

      <!-- 操作 -->
      <ElTableColumn label="操作" width="120" align="center" fixed="right">
        <template #default="{ row }">
          <ElSpace>
            <ElTooltip content="编辑">
              <ElButton
                type="warning"
                :icon="Edit"
                size="small"
                circle
                @click="handleEdit(row)"
              />
            </ElTooltip>

            <ElTooltip content="删除">
              <ElButton
                type="danger"
                :icon="Delete"
                size="small"
                circle
                @click="handleDelete(row)"
              />
            </ElTooltip>
          </ElSpace>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <ElPagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="PAGE_SIZE_OPTIONS"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.shipping-template-table {
  width: 100%;
}

.template-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.shipping-fee {
  font-weight: 500;
  color: var(--el-color-danger);
}

.remark-text {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
