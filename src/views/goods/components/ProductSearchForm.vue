<!--
  商品搜索表单组件
  提供商品列表的搜索和筛选功能
-->

<script setup lang="ts">
import type { ProductSearchParams } from '@/views/goods/types'
import { Refresh, Search } from '@element-plus/icons-vue'
import { ElButton, ElCol, ElForm, ElFormItem, ElInput, ElOption, ElRow, ElSelect } from 'element-plus'
import { ref } from 'vue'
import {
  PRODUCT_SOURCE_OPTIONS,
  PRODUCT_STATUS_OPTIONS,
  PRODUCT_TYPE_OPTIONS,
  SORT_DIRECTION_OPTIONS,
  SORT_FIELD_OPTIONS,
} from '@/views/goods/constants'

// ==================== Props & Emits ====================

interface Props {
  modelValue: ProductSearchParams
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: ProductSearchParams): void
  (e: 'search'): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const formRef = ref()
const showAdvanced = ref(false)

// ==================== 计算属性 ====================

const searchParams = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// ==================== 事件处理 ====================

/**
 * 搜索
 */
function handleSearch() {
  emit('search')
}

/**
 * 重置
 */
function handleReset() {
  formRef.value?.resetFields()
  emit('reset')
}

/**
 * 切换高级搜索
 */
function toggleAdvanced() {
  showAdvanced.value = !showAdvanced.value
}

/**
 * 回车搜索
 */
function handleEnterSearch(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleSearch()
  }
}

/**
 * 清空后自动搜索
 */
function handleClear() {
  // 清空后自动触发搜索
  handleSearch()
}
</script>

<template>
  <div class="product-search-form">
    <ElForm
      ref="formRef"
      :model="searchParams"
      inline
      label-width="80px"
      @keyup.enter="handleEnterSearch"
    >
      <ElRow :gutter="16">
        <!-- 基础搜索 -->
        <ElCol :span="6">
          <ElFormItem label="商品名称" prop="name">
            <ElInput
              v-model="searchParams.name"
              placeholder="请输入商品名称"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleClear"
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="商品类型" prop="productType">
            <ElSelect
              v-model="searchParams.productType"
              placeholder="请选择商品类型"
              clearable
              @clear="handleClear"
            >
              <ElOption
                v-for="option in PRODUCT_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="商品状态" prop="status">
            <ElSelect
              v-model="searchParams.status"
              placeholder="请选择商品状态"
              clearable
              @clear="handleClear"
            >
              <ElOption
                v-for="option in PRODUCT_STATUS_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem>
            <ElButton
              type="primary"
              :icon="Search"
              :loading="loading"
              @click="handleSearch"
            >
              搜索
            </ElButton>
            <ElButton
              :icon="Refresh"
              @click="handleReset"
            >
              重置
            </ElButton>
            <ElButton
              link
              @click="toggleAdvanced"
            >
              {{ showAdvanced ? '收起' : '展开' }}
            </ElButton>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 高级搜索 -->
      <ElRow v-show="showAdvanced" :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="商品来源" prop="source">
            <ElSelect
              v-model="searchParams.source"
              placeholder="请选择商品来源"
              clearable
              @clear="handleClear"
            >
              <ElOption
                v-for="option in PRODUCT_SOURCE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="分类ID" prop="categoryId">
            <ElInput
              v-model="searchParams.categoryId"
              placeholder="请输入分类ID"
              clearable
              @clear="handleClear"
            />
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="排序字段" prop="sortField">
            <ElSelect
              v-model="searchParams.sortField"
              placeholder="请选择排序字段"
              clearable
              @clear="handleClear"
            >
              <ElOption
                v-for="option in SORT_FIELD_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="排序方向" prop="sortDirection">
            <ElSelect
              v-model="searchParams.sortDirection"
              placeholder="请选择排序方向"
              clearable
              @clear="handleClear"
            >
              <ElOption
                v-for="option in SORT_DIRECTION_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style scoped>
.product-search-form {
  padding: 16px;
}

.product-search-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.product-search-form :deep(.el-form-item__content) {
  width: 100%;
}

.product-search-form :deep(.el-input),
.product-search-form :deep(.el-select) {
  width: 100%;
}
</style>
