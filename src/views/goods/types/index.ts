/**
 * 商品管理模块类型定义
 * 基于接口文档定义完整的类型系统
 */

// ==================== 基础类型 ====================

/** 基础响应类型 */
export interface BaseResponse<T = any> {
  code: number
  data: T
  msg: string
  success: boolean
}

/** 分页响应类型 */
export interface PageResponse<T = any> {
  total: number
  content: T[]
  result: T[]
}

/** 分页请求参数 */
export interface PageParams {
  page: number
  size: number
}

// ==================== 枚举类型 ====================

/** 商品类型枚举 */
export enum ProductType {
  PHYSICAL = 'PHYSICAL', // 实物商品
  VIRTUAL = 'VIRTUAL', // 虚拟商品
  SERVICE = 'SERVICE', // 服务商品
}

/** 商品状态枚举 */
export enum ProductStatus {
  DRAFT = 0, // 草稿
  ACTIVE = 1, // 上架
  INACTIVE = 2, // 下架
  DELETED = 3, // 已删除
}

/** 商品来源枚举 */
export enum ProductSource {
  SELF = 0, // 自营
  THIRD_PARTY = 1, // 第三方
}

/** 规格类型枚举 */
export enum SpecType {
  SINGLE = 'SINGLE', // 单规格
  MULTIPLE = 'MULTIPLE', // 多规格
}

/** 配送方式枚举 */
export enum DeliveryMethod {
  MERCHANT_DELIVERY = 'MERCHANT_DELIVERY', // 商家配送
  EXPRESS_DELIVERY = 'EXPRESS_DELIVERY', // 快递配送
  SELF_PICKUP = 'SELF_PICKUP', // 自提
}

// ==================== 商品相关类型 ====================

/** SKU参数 */
export interface SkuParam {
  specName: string // 规格名称
  attrName: string // 属性名称
  attrValue: string // 属性值
}

/** 库存信息 */
export interface Inventory {
  id: string
  stock: number // 库存数量
  salesCount: number // 销量
  alertStock: number // 预警库存
  lockedStock: number // 锁定库存
  createAt: number // 创建时间
  updateAt: number // 更新时间
}

/** SKU信息 */
export interface ProductSku {
  id: string
  productId: string
  name: string
  paramList: SkuParam[] // SKU参数列表
  price: number // 价格（分）
  memberPrice: number // 会员价（分）
  originalPrice: number // 原价（分）
  costPrice: number // 成本价（分）
  image: string // 图片
  inventory: Inventory // 库存信息
  weight: number // 重量（kg）
  volume: number // 体积（立方米）
  default: boolean // 是否默认
  visible: boolean // 是否展示
  barcode: string // 商品条码
  createAt: number // 创建时间
  updateAt: number // 更新时间
}

/** 商品信息 */
export interface Product {
  id: string
  name: string // 商品名称
  productType: ProductType // 商品类型
  productTypeDesc: string // 商品类型描述
  categoryId: string // 商品分类ID
  skuList: ProductSku[] // SKU列表
  status: ProductStatus // 商品状态
  statusDesc: string // 状态描述
  source: ProductSource // 商品来源
  sourceDesc: string // 来源描述
  mainImage: string // 主图片
  images: string[] // 图片列表
  detail: string // 商品详情
  salesCount: number // 销量
  initialSalesCount: number // 初始销量
  createAt: number // 创建时间
  updateAt: number // 更新时间
  externalId: string // 外部商品ID
  delFlag: number // 删除标记
  description: string // 商品简介
  specType: SpecType // 规格类型
  specTypeDesc: string // 规格类型描述
  deliveryMethod: DeliveryMethod // 配送方式
  deliveryMethodDesc: string // 配送方式描述
  checkOrderFormIdList: string[] // 下单信息表单项ID列表
  formListEmpty: boolean // 表单列表是否为空
  sortOrder: number // 排序
}

// ==================== 请求参数类型 ====================

/** 商品列表查询参数 */
export interface ProductListParams extends PageParams {
  name?: string // 商品名称
  productType?: number // 商品类型
  categoryId?: string // 商品分类ID
  status?: number // 商品状态
  source?: number // 商品来源
}

/** 商品搜索参数 */
export interface ProductSearchParams extends PageParams {
  name?: string // 商品名称（模糊搜索）
  productType?: number // 商品类型
  categoryId?: string // 商品分类ID
  status?: number // 商品状态
  source?: number // 商品来源
  sortField?: string // 排序字段
  sortDirection?: string // 排序方向
}

/** 商品创建参数 */
export interface CreateProductParams {
  name: string // 商品名称
  productType: number // 商品类型
  categoryId: string // 商品分类ID
  skuList?: ProductSku[] // SKU列表
  status: number // 商品状态
  source: number // 商品来源
  mainImage?: string // 主图片
  images?: string[] // 图片列表
  detail?: string // 商品详情
  description?: string // 商品简介
  specType: number // 规格类型
  deliveryMethod: number // 配送方式
  sortOrder?: number // 排序
  checkOrderFormIdList?: string[] // 下单信息表单项ID列表
  externalId?: string // 外部商品ID
  initialSalesCount?: number // 初始销量
}

/** 商品更新参数 */
export interface UpdateProductParams extends Partial<CreateProductParams> {
  id: string // 商品ID
}

/** 商品状态更新参数 */
export interface UpdateProductStatusParams {
  id: string
  status: number
}

/** 批量更新商品状态参数 */
export interface BatchUpdateProductStatusParams {
  ids: string[]
  status: number
}

// ==================== 运费模板相关类型 ====================

/** 运费模板 */
export interface ShippingTemplate {
  id: string
  name: string // 模板名称
  area: string // 适用地区
  shippingFee: number // 运费（分）
  remark: string // 备注
  sortOrder: number // 排序
  createAt: number // 创建时间
  updateAt: number // 更新时间
  delFlag: number // 删除标记
}

/** 运费模板搜索参数 */
export interface ShippingTemplateSearchParams extends PageParams {
  name?: string // 模板名称（模糊搜索）
}

/** 运费模板创建/更新参数 */
export interface ShippingTemplateParams {
  id?: string // 模板ID（编辑时指定）
  name: string // 模板名称
  area: string // 适用地区
  shippingFee: number // 运费（分）
  remark?: string // 备注
  sortOrder?: number // 排序
}

// ==================== API响应类型 ====================

/** 商品列表响应 */
export type ProductListResponse = PageResponse<Product>

/** 商品详情响应 */
export type ProductDetailResponse = Product

/** 运费模板列表响应 */
export type ShippingTemplateListResponse = PageResponse<ShippingTemplate>

/** 运费模板详情响应 */
export type ShippingTemplateDetailResponse = ShippingTemplate

// ==================== 表单相关类型 ====================

/** 表单验证规则 */
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any, callback: any) => void
  min?: number
  max?: number
  pattern?: RegExp
}

/** 表单字段配置 */
export interface FormFieldConfig {
  [key: string]: FormRule[]
}

// ==================== 组件Props类型 ====================

/** 商品表格组件Props */
export interface ProductTableProps {
  data: Product[]
  loading?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
}

/** 商品搜索表单Props */
export interface ProductSearchFormProps {
  modelValue: ProductSearchParams
  loading?: boolean
}

/** 商品表单Props */
export interface ProductFormProps {
  modelValue?: Product
  mode: 'create' | 'edit' | 'view'
  loading?: boolean
}

// ==================== 工具类型 ====================

/** 选择性必需 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

/** 选择性可选 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
