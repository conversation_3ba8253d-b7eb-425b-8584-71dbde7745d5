<route lang="yaml">
meta:
  title: 商品管理
  icon: 'ep:goods'
</route>

<script setup lang="ts">
import { Download, Plus, Refresh, Upload } from '@element-plus/icons-vue'
import { ElButton, ElCard, ElDivider, ElSpace } from 'element-plus'
import { onMounted, ref } from 'vue'
import ProductForm from './components/ProductForm.vue'
import ProductSearchForm from './components/ProductSearchForm.vue'
import ProductTable from './components/ProductTable.vue'
import { useProductList } from './composables/useProductList'

defineOptions({
  name: 'ProductList',
})

// 使用商品列表管理
const {
  productList,
  loading,
  total,
  selectedIds,
  searchParams,
  hasSelected,
  isSingleSelected,
  isMultipleSelected,
  hasData,
  fetchProductList,
  refreshList,
  handleSearch,
  handleReset,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  clearSelection,
  handleStatusChange,
  handleBatchStatusChange,
  handleDelete,
} = useProductList()

// 表单相关
const showProductForm = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const currentProduct = ref()

// ==================== 生命周期 ====================

onMounted(() => {
  fetchProductList()
})

// ==================== 事件处理 ====================

/**
 * 新增商品
 */
function handleCreate() {
  currentProduct.value = undefined
  formMode.value = 'create'
  showProductForm.value = true
}

/**
 * 编辑商品
 */
function handleEdit(product: any) {
  currentProduct.value = product
  formMode.value = 'edit'
  showProductForm.value = true
}

/**
 * 查看商品详情
 */
function handleView(product: any) {
  currentProduct.value = product
  formMode.value = 'view'
  showProductForm.value = true
}

/**
 * 表单提交成功
 */
function handleFormSuccess() {
  showProductForm.value = false
  refreshList()
}

/**
 * 批量上架
 */
function handleBatchOnline() {
  handleBatchStatusChange(1) // 上架状态
}

/**
 * 批量下架
 */
function handleBatchOffline() {
  handleBatchStatusChange(2) // 下架状态
}

/**
 * 导出商品
 */
function handleExport() {
  // TODO: 实现导出功能
  console.log('导出商品')
}

/**
 * 导入商品
 */
function handleImport() {
  // TODO: 实现导入功能
  console.log('导入商品')
}
</script>

<template>
  <div class="product-list">
    <!-- 搜索表单 -->
    <ElCard class="search-card" shadow="never">
      <ProductSearchForm
        v-model="searchParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </ElCard>

    <!-- 操作栏 -->
    <ElCard class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <ElSpace>
            <ElButton
              type="primary"
              :icon="Plus"
              @click="handleCreate"
            >
              新增商品
            </ElButton>

            <ElButton
              :icon="Refresh"
              :loading="loading"
              @click="refreshList"
            >
              刷新
            </ElButton>

            <ElDivider direction="vertical" />

            <ElButton
              :disabled="!hasSelected"
              @click="handleBatchOnline"
            >
              批量上架
            </ElButton>

            <ElButton
              :disabled="!hasSelected"
              @click="handleBatchOffline"
            >
              批量下架
            </ElButton>
          </ElSpace>
        </div>

        <div class="toolbar-right">
          <ElSpace>
            <ElButton
              :icon="Download"
              @click="handleExport"
            >
              导出
            </ElButton>

            <ElButton
              :icon="Upload"
              @click="handleImport"
            >
              导入
            </ElButton>
          </ElSpace>
        </div>
      </div>

      <!-- 选择提示 -->
      <div v-if="hasSelected" class="selection-info">
        <span>已选择 {{ selectedIds.length }} 项</span>
        <ElButton link @click="clearSelection">
          清空选择
        </ElButton>
      </div>
    </ElCard>

    <!-- 商品表格 -->
    <ElCard class="table-card" shadow="never">
      <ProductTable
        :data="productList"
        :loading="loading"
        :total="total"
        :current-page="searchParams.page"
        :page-size="searchParams.size"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @status-change="handleStatusChange"
      />
    </ElCard>

    <!-- 商品表单弹窗 -->
    <ProductForm
      v-if="showProductForm"
      v-model:visible="showProductForm"
      :mode="formMode"
      :product="currentProduct"
      @success="handleFormSuccess"
    />
  </div>
</template>

<style scoped>
.product-list {
  padding: 16px;
}

.search-card,
.toolbar-card,
.table-card {
  margin-bottom: 16px;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.selection-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 4px;
}
</style>
