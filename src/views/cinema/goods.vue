<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'

import apiCinema from '@/api/modules/cinema/index'

// 响应式数据
const cinemaList = ref<any[]>([])
const filteredCinemas = ref<any[]>([])
const loading = ref({
  cinemaList: false,
  goods: false
})
const searchQuery = ref('')
const selectedCinema = ref<any | null>(null)
const goodsList = ref<any[]>([])

// 获取影院列表
async function getCinemaList() {
  loading.value.cinemaList = true
  try {
    const res = await apiCinema.getAllCinemaList()
    if (res.code === 0) {
      cinemaList.value = res.data
      filteredCinemas.value = res.data
    } else {
      ElMessage.error(res.msg || '获取影院列表失败')
    }
  }
  catch (e) {
    console.error('获取影院列表失败:', e)
    ElMessage.error('获取影院列表失败')
  }
  finally {
    loading.value.cinemaList = false
  }
}

// 搜索影院
function searchCinema() {
  const query = searchQuery.value.toLowerCase()
  if (!query) {
    filteredCinemas.value = cinemaList.value
    return
  }

  filteredCinemas.value = cinemaList.value.filter(cinema =>
    cinema.name?.toLowerCase().includes(query)
    || cinema.code?.toLowerCase().includes(query)
    || cinema.city?.toLowerCase().includes(query),
  )
}

// 选择影院并获取卖品信息
async function selectCinema(cinema: any) {
  selectedCinema.value = cinema

  // 获取最新的卖品信息
  loading.value.goods = true
  try {
    const res = await apiCinema.getCinemaDetail({ id: cinema.id || '' })
    if (res.code === 0) {
      goodsList.value = res.data.goods || []
    } else {
      ElMessage.error(res.msg || '获取卖品信息失败')
      goodsList.value = []
    }
  }
  catch (e) {
    console.error('获取卖品信息失败:', e)
    ElMessage.error('获取卖品信息失败')
    goodsList.value = []
  }
  finally {
    loading.value.goods = false
  }
}

onMounted(() => {
  getCinemaList()
})
</script>

<template>
  <div class="p-5">
    <fa-page-header title="影院卖品管理" description="查看和管理各影院的卖品信息" />
    <fa-page-main>
      <div class="h-[calc(100vh-180px)] flex gap-5">
        <!-- 左侧影院列表 -->
        <div class="w-3/10 flex flex-col gap-4">
          <div class="pb-2.5">
            <el-input
              v-model="searchQuery"
              placeholder="搜索影院名称、编码或城市"
              prefix-icon="Search"
              clearable
              @input="searchCinema"
              @clear="searchCinema"
            />
          </div>

          <div
            v-loading="loading.cinemaList"
            class="flex-1 overflow-y-auto border border-gray-200 rounded"
          >
            <div
              v-for="cinema in filteredCinemas"
              :key="cinema.id"
              class="cursor-pointer border-b border-gray-200 p-3 transition-colors duration-200 hover:bg-blue-50"
              :class="{ 'bg-blue-50 border-l-4 border-l-orange-8': cinema === selectedCinema }"
              @click="selectCinema(cinema)"
            >
              <div class="mb-1 flex items-center justify-between">
                <span class="text-sm text-gray-800 font-medium">{{ cinema.name }}</span>
                <span
                  class="rounded px-1.5 py-0.5 text-xs"
                  :class="cinema.syncStatus === 1 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-500'"
                >
                  {{ cinema.syncStatus === 1 ? '正常' : '隔离' }}
                </span>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>{{ cinema.code }}</span>
                <span>{{ cinema.province }} {{ cinema.city }}</span>
                <span>{{ cinema.screenCount }}个影厅</span>
              </div>
            </div>

            <!-- 无数据提示 -->
            <div
              v-if="!loading.cinemaList && filteredCinemas.length === 0"
              class="text-center py-10 text-gray-500"
            >
              未找到相关影院
            </div>
          </div>
        </div>

        <!-- 右侧卖品信息 -->
        <div class="w-7/10 flex flex-col gap-4">
          <div v-if="selectedCinema" class="rounded-lg bg-gray-100 p-4">
            <h3 class="mt-0">
              {{ selectedCinema.name }} - 卖品列表
            </h3>
            <p>影院编码: {{ selectedCinema.code }}</p>
            <p>地址: {{ selectedCinema.address }}</p>
          </div>

          <div
            v-loading="loading.goods"
            class="flex-1"
          >
            <div v-if="goodsList.length > 0" class="flex-1 overflow-auto">
              <el-table :data="goodsList" border class="full-width">
                <el-table-column prop="name" label="卖品名称" min-width="200" show-overflow-tooltip />
                <el-table-column prop="code" label="卖品编码" width="150" />
                <el-table-column label="是否套餐" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.packageFlag === 'Y' ? 'success' : 'info'">
                      {{ row.packageFlag === 'Y' ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="standardPrice" label="标准价(元)" width="120" align="right">
                  <template #default="{ row }">
                    {{ row.standardPrice !== undefined ? (row.standardPrice / 100).toFixed(2) : 'N/A' }}
                  </template>
                </el-table-column>
                <el-table-column prop="settlePrice" label="结算价(元)" width="120" align="right">
                  <template #default="{ row }">
                    {{ row.settlePrice !== undefined ? (row.settlePrice / 100).toFixed(2) : 'N/A' }}
                  </template>
                </el-table-column>
                <el-table-column label="图片" width="120">
                  <template #default="{ row }">
                    <el-image
                      v-if="row.goodsPicUrl"
                      :src="row.goodsPicUrl"
                      :preview-src-list="[row.goodsPicUrl]"
                      fit="cover"
                      style="width: 80px; height: 80px;"
                      lazy
                    />
                    <span v-else>无图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="desc" label="描述" min-width="200" show-overflow-tooltip />
              </el-table>
            </div>

            <div
              v-else-if="selectedCinema && !loading.goods"
              class="py-10 text-center text-gray-500"
            >
              <p>该影院暂无卖品信息</p>
            </div>

            <div
              v-else-if="!selectedCinema && !loading.goods"
              class="py-10 text-center text-gray-500"
            >
              <p>请从左侧选择一个影院</p>
            </div>
          </div>
        </div>
      </div>
    </fa-page-main>
  </div>
</template>

<style scoped>
/* 已经使用 UnoCSS 工具类，移除原有的样式 */
</style>
