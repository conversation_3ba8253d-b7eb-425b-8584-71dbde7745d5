# 优惠券选择组件 (CouponSelector)

## 📋 概述

`CouponSelector` 是一个功能完整的优惠券选择组件，支持单选/多选、搜索筛选、分页等功能，可以在各种业务场景中复用。

## ✨ 功能特性

- ✅ **单选/多选模式**: 支持单个或多个优惠券选择
- ✅ **智能筛选**: 支持按名称、类型、状态等条件筛选
- ✅ **分页支持**: 大数据量下的分页展示
- ✅ **排除功能**: 可排除指定的优惠券ID
- ✅ **数量限制**: 支持最大选择数量限制
- ✅ **状态管理**: 自动同步选中状态
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **TypeScript**: 完整的类型定义支持

## 🎯 使用场景

### 1. **营销活动配置**
在创建营销活动时选择关联的优惠券

### 2. **用户发券**
为用户批量发放优惠券时的选择

### 3. **订单优惠**
在订单结算时选择可用的优惠券

### 4. **数据分析**
在报表分析中选择要统计的优惠券

## 🚀 基础用法

### 单选模式
```vue
<template>
  <div>
    <ElButton @click="showSelector = true">选择优惠券</ElButton>
    
    <CouponSelector
      v-model="showSelector"
      :multiple="false"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
    
    <div v-if="selectedCoupon">
      已选择: {{ selectedCoupon.couponName }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CouponSelector from '@/components/CouponSelector/index.vue'

const showSelector = ref(false)
const selectedCoupon = ref(null)

function handleConfirm(coupons) {
  selectedCoupon.value = coupons[0]
  console.log('选择的优惠券:', coupons[0])
}

function handleCancel() {
  console.log('取消选择')
}
</script>
```

### 多选模式
```vue
<template>
  <div>
    <ElButton @click="showSelector = true">
      选择优惠券 ({{ selectedCoupons.length }})
    </ElButton>
    
    <CouponSelector
      v-model="showSelector"
      :multiple="true"
      :max-select="5"
      :selected-coupons="selectedCoupons"
      @confirm="handleConfirm"
    />
    
    <ElTag 
      v-for="coupon in selectedCoupons" 
      :key="coupon.id"
      closable
      @close="removeCoupon(coupon.id)"
    >
      {{ coupon.couponName }}
    </ElTag>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CouponSelector from '@/components/CouponSelector/index.vue'

const showSelector = ref(false)
const selectedCoupons = ref([])

function handleConfirm(coupons) {
  selectedCoupons.value = coupons
  console.log('选择的优惠券:', coupons)
}

function removeCoupon(couponId) {
  selectedCoupons.value = selectedCoupons.value.filter(
    coupon => coupon.id !== couponId
  )
}
</script>
```

## 🔧 API 参考

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `boolean` | `false` | 对话框显示状态 |
| `multiple` | `boolean` | `true` | 是否支持多选 |
| `selectedCoupons` | `Coupon[]` | `[]` | 已选择的优惠券列表 |
| `useOn` | `number` | - | 限制适用商品类型 (0:影票 1:卖品 2:演出 3:展览) |
| `couponType` | `number` | - | 限制优惠券类型 (0:满减 1:减至 2:通兑 3:折扣 4:多对一) |
| `status` | `number` | - | 限制优惠券状态 (0:未开始 1:进行中 2:已结束 3:已停用) |
| `excludeIds` | `string[]` | `[]` | 排除的优惠券ID列表 |
| `maxSelect` | `number` | `10` | 最大选择数量 (仅多选模式) |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: boolean)` | 对话框显示状态变化 |
| `confirm` | `(coupons: Coupon[])` | 确认选择时触发 |
| `cancel` | `()` | 取消选择时触发 |

### Coupon 类型定义

```typescript
interface Coupon {
  id: string                    // 优惠券ID
  couponName: string           // 优惠券名称
  couponType: number           // 优惠券类型
  useOn: number               // 适用商品类型
  faceValue: number           // 面值
  minOrderAmount: number      // 最低使用金额
  validStartTime: string      // 有效开始时间
  validEndTime: string        // 有效结束时间
  status: number              // 状态
  // ... 其他字段
}
```

## 🎨 高级用法

### 1. 限制特定类型优惠券
```vue
<CouponSelector
  v-model="showSelector"
  :use-on="0"
  :coupon-type="0"
  :status="1"
  @confirm="handleConfirm"
/>
```

### 2. 排除已使用的优惠券
```vue
<CouponSelector
  v-model="showSelector"
  :exclude-ids="usedCouponIds"
  @confirm="handleConfirm"
/>
```

### 3. 自定义最大选择数量
```vue
<CouponSelector
  v-model="showSelector"
  :multiple="true"
  :max-select="3"
  @confirm="handleConfirm"
/>
```

### 4. 预设已选择的优惠券
```vue
<CouponSelector
  v-model="showSelector"
  :selected-coupons="preSelectedCoupons"
  @confirm="handleConfirm"
/>
```

## 🎯 实际应用示例

### 营销活动配置
```vue
<template>
  <ElForm :model="activityForm">
    <ElFormItem label="关联优惠券">
      <div class="coupon-selection">
        <ElButton @click="showCouponSelector = true">
          选择优惠券 ({{ activityForm.coupons.length }})
        </ElButton>
        
        <div class="selected-coupons">
          <ElTag
            v-for="coupon in activityForm.coupons"
            :key="coupon.id"
            closable
            @close="removeCoupon(coupon.id)"
          >
            {{ coupon.couponName }}
          </ElTag>
        </div>
      </div>
    </ElFormItem>
  </ElForm>
  
  <CouponSelector
    v-model="showCouponSelector"
    :multiple="true"
    :max-select="10"
    :selected-coupons="activityForm.coupons"
    :status="1"
    @confirm="handleCouponConfirm"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import CouponSelector from '@/components/CouponSelector/index.vue'

const showCouponSelector = ref(false)
const activityForm = reactive({
  name: '',
  coupons: []
})

function handleCouponConfirm(coupons) {
  activityForm.coupons = coupons
}

function removeCoupon(couponId) {
  activityForm.coupons = activityForm.coupons.filter(
    coupon => coupon.id !== couponId
  )
}
</script>
```

### 用户发券功能
```vue
<template>
  <div class="send-coupon">
    <ElCard>
      <template #header>
        <span>批量发券</span>
      </template>
      
      <ElForm :model="sendForm">
        <ElFormItem label="选择用户">
          <UserSelector v-model="sendForm.userIds" />
        </ElFormItem>
        
        <ElFormItem label="选择优惠券">
          <ElButton @click="showCouponSelector = true">
            选择优惠券 ({{ sendForm.coupons.length }})
          </ElButton>
          
          <div class="coupon-list">
            <CouponCard
              v-for="coupon in sendForm.coupons"
              :key="coupon.id"
              :coupon="coupon"
              @remove="removeCoupon"
            />
          </div>
        </ElFormItem>
        
        <ElFormItem>
          <ElButton type="primary" @click="handleSend">
            发放优惠券
          </ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>
    
    <CouponSelector
      v-model="showCouponSelector"
      :multiple="true"
      :max-select="20"
      :selected-coupons="sendForm.coupons"
      :status="1"
      @confirm="handleCouponConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import CouponSelector from '@/components/CouponSelector/index.vue'
import { sendCouponsToUsers } from '@/api/modules/coupons'

const showCouponSelector = ref(false)
const sendForm = reactive({
  userIds: [],
  coupons: []
})

function handleCouponConfirm(coupons) {
  sendForm.coupons = coupons
}

function removeCoupon(couponId) {
  sendForm.coupons = sendForm.coupons.filter(
    coupon => coupon.id !== couponId
  )
}

async function handleSend() {
  try {
    await sendCouponsToUsers({
      userIds: sendForm.userIds,
      couponIds: sendForm.coupons.map(c => c.id)
    })
    ElMessage.success('发放成功')
  } catch (error) {
    ElMessage.error('发放失败')
  }
}
</script>
```

## 🎨 样式自定义

### 自定义主题色
```vue
<style scoped>
:deep(.el-button--primary) {
  background-color: #your-primary-color;
  border-color: #your-primary-color;
}

:deep(.el-tag--success) {
  background-color: #your-success-color;
}
</style>
```

### 自定义表格样式
```vue
<style scoped>
:deep(.el-table .el-table__row:hover) {
  background-color: #your-hover-color;
}

:deep(.el-table .amount) {
  color: #your-amount-color;
  font-weight: bold;
}
</style>
```

## 🔧 扩展功能

### 1. 添加优惠券预览
```vue
<!-- 在表格中添加预览按钮 -->
<ElTableColumn label="操作" width="100">
  <template #default="{ row }">
    <ElButton 
      size="small" 
      text 
      @click.stop="previewCoupon(row)"
    >
      预览
    </ElButton>
  </template>
</ElTableColumn>
```

### 2. 添加批量操作
```vue
<!-- 添加批量选择按钮 -->
<div class="batch-actions">
  <ElButton @click="selectAll">全选当页</ElButton>
  <ElButton @click="clearSelection">清空选择</ElButton>
</div>
```

### 3. 添加收藏功能
```vue
<!-- 添加收藏按钮 -->
<ElTableColumn label="收藏" width="80">
  <template #default="{ row }">
    <ElButton
      :icon="row.isFavorite ? 'Star' : 'StarFilled'"
      size="small"
      text
      @click.stop="toggleFavorite(row)"
    />
  </template>
</ElTableColumn>
```

## 🐛 常见问题

### Q: 为什么选择的优惠券没有同步？
A: 确保正确使用 `v-model` 绑定 `modelValue`，并在 `confirm` 事件中更新数据。

### Q: 如何限制只显示特定类型的优惠券？
A: 使用 `useOn`、`couponType`、`status` 等 props 进行筛选。

### Q: 如何自定义表格列？
A: 目前组件内置了常用列，如需自定义可以 fork 组件代码进行修改。

### Q: 分页数据加载慢怎么办？
A: 可以添加缓存机制或使用虚拟滚动来优化性能。

## 📝 更新日志

### v1.0.0 (2024-12)
- ✅ 初始版本发布
- ✅ 支持单选/多选模式
- ✅ 支持搜索和筛选
- ✅ 支持分页功能
- ✅ 完整的 TypeScript 支持

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个组件！

### 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test
```

---

**作者**: 开发团队  
**最后更新**: 2024年12月  
**版本**: v1.0.0