<script setup lang="ts">
import type { Coupon, CouponListParams } from '@/views/market/coupons/baseConfig/types'
import { Refresh, Search } from '@element-plus/icons-vue'
import { ElButton, ElDialog, ElEmpty, ElInput, ElOption, ElPagination, ElSelect, ElTable, ElTableColumn, ElTag } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { getCouponList } from '@/api/modules/market/coupons'

defineOptions({
  name: 'CouponSelector',
})

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  selectedCoupons: () => [],
  excludeIds: () => [],
  maxSelect: 10,
})

const emits = defineEmits<Emits>()

// import { formatAmount, getCouponStatusDesc, getCouponTypeDesc, getUseOnDesc } from '@/views/coupons/baseConfig/constants'

function formatAmount(amount: number): string {
  return `¥${(amount / 100).toFixed(2)}`
}

function getCouponStatusDesc(status: number): string {
  const statusMap = ['未开始', '进行中', '已结束', '已停用']
  return statusMap[status] || '未知'
}

function getCouponTypeDesc(type: number): string {
  const typeMap = ['满减券', '减至券', '通兑券', '折扣券', '多对一券']
  return typeMap[type] || '未知'
}

function getUseOnDesc(useOn: number): string {
  const useOnMap = ['影票', '卖品', '演出', '展览']
  return useOnMap[useOn] || '未知'
}

interface Props {
  modelValue: boolean
  multiple?: boolean
  selectedCoupons?: Coupon[]
  useOn?: number // 限制优惠券类型：0-影票 1-卖品 2-演出 3-展览
  couponType?: number // 限制优惠券种类：0-满减 1-减至 2-通兑 3-折扣 4-多对一
  status?: number // 限制优惠券状态：0-未开始 1-进行中 2-已结束 3-已停用
  excludeIds?: string[] // 排除的优惠券ID
  maxSelect?: number // 最大选择数量
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', coupons: Coupon[]): void
  (e: 'cancel'): void
}

// 响应式数据
const loading = ref(false)
const couponList = ref<Coupon[]>([])
const selectedRows = ref<Coupon[]>([...props.selectedCoupons])
const tableRef = ref()

// 搜索参数
const searchParams = ref<CouponListParams>({
  page: 1,
  size: 10,
  name: '',
  couponType: props.couponType,
  useOn: props.useOn,
  status: props.status,
})

// 分页信息
const pagination = ref({
  total: 0,
  page: 1,
  size: 10,
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emits('update:modelValue', value),
})

const filteredCouponList = computed(() => {
  return couponList.value.filter(coupon =>
    !props.excludeIds.includes(coupon.id),
  )
})

const isMaxSelected = computed(() => {
  return props.multiple && selectedRows.value.length >= props.maxSelect
})

const confirmButtonText = computed(() => {
  if (props.multiple) {
    return `确认选择 (${selectedRows.value.length}/${props.maxSelect})`
  }
  return '确认选择'
})

// 方法
async function fetchCouponList() {
  loading.value = true
  try {
    const params = {
      ...searchParams.value,
      page: pagination.value.page,
      size: pagination.value.size,
    }

    const response = await getCouponList(params)
    const { data } = response
    couponList.value = data.content || []
    pagination.value.total = data.total || 0
  }
  catch (error) {
    console.error('获取优惠券列表失败:', error)
    couponList.value = []
  }
  finally {
    loading.value = false
  }
}

function handleSearch() {
  pagination.value.page = 1
  fetchCouponList()
}

function handleReset() {
  searchParams.value = {
    page: 1,
    size: 10,
    name: '',
    couponType: props.couponType,
    useOn: props.useOn,
    status: props.status,
  }
  handleSearch()
}

function handlePageChange(page: number) {
  pagination.value.page = page
  fetchCouponList()
}

function handleSizeChange(size: number) {
  pagination.value.size = size
  pagination.value.page = 1
  fetchCouponList()
}

function handleSelectionChange(selection: Coupon[]) {
  selectedRows.value = selection
}

function handleRowClick(row: Coupon) {
  if (!props.multiple) {
    selectedRows.value = [row]
    return
  }

  const index = selectedRows.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    selectedRows.value.splice(index, 1)
    tableRef.value?.toggleRowSelection(row, false)
  }
  else if (!isMaxSelected.value) {
    selectedRows.value.push(row)
    tableRef.value?.toggleRowSelection(row, true)
  }
}

function isRowSelectable(row: Coupon) {
  if (selectedRows.value.find(item => item.id === row.id)) {
    return true
  }
  return !isMaxSelected.value
}

function handleConfirm() {
  emits('confirm', selectedRows.value)
  dialogVisible.value = false
}

function handleCancel() {
  selectedRows.value = [...props.selectedCoupons]
  emits('cancel')
  dialogVisible.value = false
}

// 监听器
watch(() => props.selectedCoupons, (newVal) => {
  selectedRows.value = [...newVal]
}, { deep: true })

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    selectedRows.value = [...props.selectedCoupons]
    fetchCouponList()
  }
})

// 生命周期
onMounted(() => {
  if (props.modelValue) {
    fetchCouponList()
  }
})
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="选择优惠券"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="search-form">
      <ElInput
        v-model="searchParams.name"
        placeholder="请输入优惠券名称"
        style="width: 200px; margin-right: 10px;"
        clearable
        @keyup.enter="handleSearch"
      />

      <ElSelect
        v-if="!props.couponType"
        v-model="searchParams.couponType"
        placeholder="优惠券类型"
        style="width: 120px; margin-right: 10px;"
        clearable
      >
        <ElOption label="满减券" :value="0" />
        <ElOption label="减至券" :value="1" />
        <ElOption label="通兑券" :value="2" />
        <ElOption label="折扣券" :value="3" />
        <ElOption label="多对一券" :value="4" />
      </ElSelect>

      <ElSelect
        v-if="!props.useOn"
        v-model="searchParams.useOn"
        placeholder="适用商品"
        style="width: 120px; margin-right: 10px;"
        clearable
      >
        <ElOption label="影票" :value="0" />
        <ElOption label="卖品" :value="1" />
        <ElOption label="演出" :value="2" />
        <ElOption label="展览" :value="3" />
      </ElSelect>

      <ElSelect
        v-if="!props.status"
        v-model="searchParams.status"
        placeholder="优惠券状态"
        style="width: 120px; margin-right: 10px;"
        clearable
      >
        <ElOption label="未开始" :value="0" />
        <ElOption label="进行中" :value="1" />
        <ElOption label="已结束" :value="2" />
        <ElOption label="已停用" :value="3" />
      </ElSelect>

      <ElButton type="primary" :icon="Search" @click="handleSearch">
        搜索
      </ElButton>
      <ElButton :icon="Refresh" @click="handleReset">
        重置
      </ElButton>
    </div>

    <!-- 选择提示 -->
    <div v-if="props.multiple" class="selection-tip">
      <ElTag v-if="selectedRows.length > 0" type="success">
        已选择 {{ selectedRows.length }} 个优惠券
      </ElTag>
      <ElTag v-if="isMaxSelected" type="warning">
        最多可选择 {{ props.maxSelect }} 个优惠券
      </ElTag>
    </div>

    <!-- 表格区域 -->
    <ElTable
      ref="tableRef"
      v-loading="loading"
      :data="filteredCouponList"
      :row-key="(row: Coupon) => row.id"
      style="margin-top: 15px;"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <ElTableColumn
        v-if="props.multiple"
        type="selection"
        width="55"
        :selectable="isRowSelectable"
      />

      <ElTableColumn prop="name" label="优惠券名称" min-width="150" />

      <ElTableColumn prop="couponType" label="类型" width="100">
        <template #default="{ row }">
          <ElTag size="small">
            {{ getCouponTypeDesc(row.couponType) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="useOn" label="适用商品" width="100">
        <template #default="{ row }">
          <ElTag size="small" type="info">
            {{ getUseOnDesc(row.useOn) }}
          </ElTag>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="faceValue" label="面值" width="100">
        <template #default="{ row }">
          <span class="amount">{{ formatAmount(row.faceValue) }}</span>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="minOrderAmount" label="使用门槛" width="100">
        <template #default="{ row }">
          <span v-if="row.minOrderAmount > 0" class="amount">
            满{{ formatAmount(row.minOrderAmount) }}
          </span>
          <span v-else class="no-threshold">无门槛</span>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="validStartTime" label="有效期" width="200">
        <template #default="{ row }">
          <div class="date-range">
            <div>{{ row.validStartTime }}</div>
            <div>{{ row.validEndTime }}</div>
          </div>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="status" label="状态" width="100">
        <template #default="{ row }">
          <ElTag
            :type="row.status === 1 ? 'success' : row.status === 0 ? 'info' : 'danger'"
            size="small"
          >
            {{ getCouponStatusDesc(row.status) }}
          </ElTag>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 空状态 -->
    <ElEmpty v-if="!loading && filteredCouponList.length === 0" description="暂无优惠券数据" />

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <ElPagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          取消
        </ElButton>
        <ElButton
          type="primary"
          :disabled="selectedRows.length === 0"
          @click="handleConfirm"
        >
          {{ confirmButtonText }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.search-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.selection-tip {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.amount {
  color: #f56c6c;
  font-weight: 500;
}

.no-threshold {
  color: #909399;
  font-size: 12px;
}

.date-range {
  font-size: 12px;
  line-height: 1.4;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
