<template>
  <div class="cinema-selector-example">
    <fa-page-header title="影院选择组件示例" description="CinemaSelector 组件的各种使用方式" />

    <fa-page-main>
      <!-- 基础用法 -->
      <el-card class="example-card">
        <template #header>
          <h3>基础用法</h3>
        </template>

        <div class="example-content">
          <CinemaSelector v-model="basicSelection" @change="handleBasicChange" />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(basicSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 单选模式 -->
      <el-card class="example-card">
        <template #header>
          <h3>单选模式</h3>
        </template>

        <div class="example-content">
          <CinemaSelector
            v-model="singleSelection"
            :multiple="false"
            placeholder="请选择一个影院"
            @change="handleSingleChange"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(singleSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 自定义触发器 -->
      <el-card class="example-card">
        <template #header>
          <h3>自定义触发器</h3>
        </template>

        <div class="example-content">
          <CinemaSelector v-model="customSelection">
            <el-button type="success" icon="Location">
              <span>选择影院</span>
              <el-badge
                v-if="customSelection.length > 0"
                :value="customSelection.length"
                class="ml-2"
              />
            </el-button>
          </CinemaSelector>

          <div class="result-display">
            <h4>选择结果：</h4>
            <div v-if="customSelection.length === 0" class="empty-state">
              暂无选择
            </div>
            <div v-else class="selected-list">
              <el-tag
                v-for="cinema in customSelection"
                :key="cinema.id"
                closable
                @close="removeCinema(cinema)"
                class="cinema-tag"
              >
                {{ cinema.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 禁用状态 -->
      <el-card class="example-card">
        <template #header>
          <h3>禁用状态</h3>
        </template>

        <div class="example-content">
          <div class="control-panel">
            <el-switch
              v-model="isDisabled"
              active-text="启用"
              inactive-text="禁用"
            />
          </div>

          <CinemaSelector
            v-model="disabledSelection"
            :disabled="isDisabled"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(disabledSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 不显示数量 -->
      <el-card class="example-card">
        <template #header>
          <h3>不显示选择数量</h3>
        </template>

        <div class="example-content">
          <CinemaSelector
            v-model="noCountSelection"
            :show-select-num="false"
            placeholder="选择影院（不显示数量）"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(noCountSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </fa-page-main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CinemaSelector from './index.vue'

defineOptions({
  name: 'CinemaSelectorExample',
})

// 响应式数据
const basicSelection = ref([])
const singleSelection = ref([])
const customSelection = ref([])
const disabledSelection = ref([])
const noCountSelection = ref([])
const isDisabled = ref(false)

// 事件处理
function handleBasicChange(cinemas: any[]) {
  console.log('基础选择变化:', cinemas)
}

function handleSingleChange(cinemas: any[]) {
  console.log('单选变化:', cinemas)
}

function removeCinema(cinema: any) {
  const index = customSelection.value.findIndex(item => item.id === cinema.id)
  if (index > -1) {
    customSelection.value.splice(index, 1)
  }
}
</script>

<style scoped lang="scss">
.cinema-selector-example {
  .example-card {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    h3 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }

  .example-content {
    .control-panel {
      padding: 12px;
      margin-bottom: 16px;
      background-color: var(--el-bg-color-page);
      border-radius: 6px;
    }

    .result-display {
      padding: 16px;
      margin-top: 20px;
      background-color: var(--el-fill-color-lighter);
      border-radius: 6px;

      h4 {
        margin: 0 0 12px;
        color: var(--el-text-color-primary);
      }

      pre {
        padding: 12px;
        margin: 0;
        overflow-x: auto;
        font-size: 12px;
        line-height: 1.5;
        background-color: var(--el-bg-color);
        border-radius: 4px;
      }

      .empty-state {
        font-style: italic;
        color: var(--el-text-color-placeholder);
      }

      .selected-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .cinema-tag {
          margin: 0;
        }
      }
    }
  }
}

.ml-2 {
  margin-left: 8px;
}
</style>
