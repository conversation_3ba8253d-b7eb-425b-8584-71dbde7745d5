# CinemaSelector 影院选择组件

一个现代化、易用的影院选择组件，支持搜索、分页、批量操作等功能。

## 特性

- 🎯 **简洁易用**: 清晰的API设计，开箱即用
- 🔍 **智能搜索**: 支持按影院名称、城市搜索
- 📄 **分页支持**: 内置分页功能，处理大量数据
- 🎨 **现代UI**: 基于Element Plus，支持响应式设计
- ⚡ **高性能**: 优化的数据处理和渲染
- 🛡️ **类型安全**: 完整的TypeScript支持

## 基础用法

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <CinemaSelector v-model="selectedCinemas" />
    
    <!-- 自定义触发器 -->
    <CinemaSelector v-model="selectedCinemas">
      <el-button type="success" icon="Location">
        选择影院 ({{ selectedCinemas.length }})
      </el-button>
    </CinemaSelector>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CinemaSelector from '@/components/cinemaSelector'

const selectedCinemas = ref([])

// 监听选择变化
function handleCinemaChange(cinemas) {
  console.log('选择的影院:', cinemas)
}
</script>
```

## API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `SelectedCinema[]` | `[]` | 选中的影院列表，支持 v-model |
| multiple | `boolean` | `true` | 是否支持多选 |
| showSelectNum | `boolean` | `true` | 是否显示选择数量 |
| placeholder | `string` | `'请选择影院'` | 占位符文本 |
| disabled | `boolean` | `false` | 是否禁用 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(cinemas: SelectedCinema[])` | 选择变化时触发 |
| change | `(cinemas: SelectedCinema[])` | 选择变化时触发 |

### Types

```typescript
interface SelectedCinema {
  id: string          // 唯一标识
  name: string        // 影院名称
  aliasName?: string  // 影院别名
  code: string        // 影院编码
  cityName?: string   // 城市名称
  [key: string]: any  // 其他扩展字段
}
```

## 高级用法

### 单选模式

```vue
<template>
  <CinemaSelector 
    v-model="selectedCinema" 
    :multiple="false"
    placeholder="请选择一个影院"
  />
</template>

<script setup>
const selectedCinema = ref([])
</script>
```

### 禁用状态

```vue
<template>
  <CinemaSelector 
    v-model="selectedCinemas" 
    :disabled="isLoading"
  />
</template>
```

### 自定义显示

```vue
<template>
  <CinemaSelector 
    v-model="selectedCinemas"
    :show-select-num="false"
  >
    <div class="custom-trigger">
      <el-icon><Location /></el-icon>
      <span>已选择影院: {{ selectedCinemas.map(c => c.name).join(', ') }}</span>
    </div>
  </CinemaSelector>
</template>
```

## 组件特性

### 搜索功能
- 支持按影院名称模糊搜索
- 支持按城市筛选
- 实时搜索结果更新

### 批量操作
- 批量添加当前页所有影院
- 一键清空所有选择
- 单个影院快速删除

### 用户体验
- 已选择影院实时预览
- 重复选择智能提示
- 响应式设计，移动端友好

### 数据处理
- 自动去重处理
- 数据格式标准化
- 错误状态处理

## 样式定制

组件使用CSS变量，支持主题定制：

```scss
.cinema-selector {
  // 自定义触发器样式
  .selected-count {
    color: var(--el-color-primary);
    font-weight: bold;
  }
}

// 自定义对话框样式
.selected-panel {
  .selected-item {
    // 自定义选中项样式
    border-left: 3px solid var(--el-color-primary);
  }
}
```

## 注意事项

1. **数据格式**: 确保API返回的影院数据包含必要字段（name, code等）
2. **唯一标识**: 组件使用 `code` 字段作为影院的唯一标识
3. **性能优化**: 大量数据时建议启用虚拟滚动（后续版本支持）
4. **错误处理**: 网络错误时会显示友好的错误提示

## 更新日志

### v2.0.0
- 🎉 完全重写，采用 Composition API
- ✨ 新增 TypeScript 支持
- 🎨 全新的UI设计
- ⚡ 性能优化
- 🛡️ 更好的错误处理

### v1.x
- 基础功能实现
- Options API 版本

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个组件！
