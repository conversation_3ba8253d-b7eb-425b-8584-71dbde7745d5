<script lang="ts" setup>
import type { CheckboxValueType } from 'element-plus'
import { ElMessage } from 'element-plus'

import cinemaApi from '@/api/modules/cinema'

interface Hall {
  code: string
  name: string
  seatCount?: number
  type: string
}

interface HallSelection {
  hallId: string
  hallName: string
}

interface Props {
  cinemaId?: string | number
  cinemaCode?: string
  cinemaName?: string
  disabled?: boolean
  modelValue?: HallSelection[]
  enableCache?: boolean // 新增缓存开关属性
}

interface Emits {
  (e: 'update:modelValue', value: HallSelection[]): void
  (e: 'change', value: HallSelection[], halls: Hall[]): void
}

const props = withDefaults(defineProps<Props>(), {
  cinemaId: '',
  cinemaCode: '',
  cinemaName: '',
  modelValue: () => [],
  disabled: false,
  enableCache: true, // 默认启用缓存
})

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const hallList = ref<Hall[]>([])

// 内部选中的影厅代码数组
const selectedHalls = ref<string[]>(props.modelValue?.map(item => item.hallId) || [])

// 缓存相关的常量
const CACHE_KEY_PREFIX = 'hall_list_cache_'
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 1天的毫秒数

// 计算属性
const selectAll = computed({
  get: () => selectedHalls.value.length === hallList.value.length && hallList.value.length > 0,
  set: (value: boolean) => {
    if (value) {
      selectedHalls.value = hallList.value.map(hall => hall.code)
    }
    else {
      selectedHalls.value = []
    }
  },
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedHalls.value.length
  return selectedCount > 0 && selectedCount < hallList.value.length
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  // 将对象数组转换为字符串数组
  selectedHalls.value = newValue?.map(item => item.hallId) || []
}, { deep: true })

// 监听cinemaId变化，获取影厅数据
watch(() => props.cinemaId, (newId) => {
  if (newId) {
    fetchHallList()
  }
}, { immediate: true })

// 监听选择变化，避免重复触发
let isUpdating = false
watch(selectedHalls, (newValue, oldValue) => {
  // 避免重复触发
  if (isUpdating || JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    return
  }

  isUpdating = true

  // 将字符串数组转换为对象数组
  const selectedHallObjects = hallList.value.filter(hall => newValue.includes(hall.code))
  const hallSelections: HallSelection[] = selectedHallObjects.map(hall => ({
    hallId: hall.code,
    hallName: hall.name,
  }))

  emit('update:modelValue', hallSelections)
  emit('change', hallSelections, selectedHallObjects)

  // 下一个 tick 重置标志
  setTimeout(() => {
    isUpdating = false
  }, 0)
}, { deep: true })

// 方法
async function fetchHallList() {
  if (!props.cinemaId) {
    return
  }
  // 检查是否有缓存数据
  const cacheKey = `${CACHE_KEY_PREFIX}${props.cinemaId}`
  // 检查是否启用了缓存功能
  if (props.enableCache) {
    const cachedData = localStorage.getItem(cacheKey)

    if (cachedData) {
      try {
        const parsedData = JSON.parse(cachedData)
        // 检查缓存是否过期 (1天)
        if (Date.now() - parsedData.timestamp < CACHE_DURATION) {
          // 使用缓存数据
          hallList.value = parsedData.data
          return
        }
        else {
          // 缓存过期，删除过期缓存
          localStorage.removeItem(cacheKey)
        }
      }
      // eslint-disable-next-line unused-imports/no-unused-vars
      catch (e) {
        // 解析缓存数据出错，删除无效缓存
        localStorage.removeItem(cacheKey)
      }
    }
  }

  try {
    isLoading.value = true
    // 使用 cinemaId 获取影院详情
    const response = await cinemaApi.getCinemaDetailById({ id: props.cinemaId })

    if (response.code === 0 && response.data) {
      // 从影院详情中获取影厅列表
      const screens = response.data.screens?.map(screen => ({
        ...screen,
      })) || []

      // 如果启用了缓存，则缓存数据到 localStorage
      if (props.enableCache || props.cinemaId || screens.length) {
        const cacheData = {
          timestamp: Date.now(),
          data: screens,
        }
        localStorage.setItem(cacheKey, JSON.stringify(cacheData))
      }

      hallList.value = screens
    }
    else {
      ElMessage.error('获取影厅列表失败')
      hallList.value = []
    }
  }
  catch (error) {
    console.error('获取影厅列表失败:', error)
    ElMessage.error('获取影厅列表失败')
    hallList.value = []
  }
  finally {
    isLoading.value = false
  }
}

function handleSelectAll(value: CheckboxValueType) {
  selectAll.value = value as boolean
  if (value) {
    // 全选：选中所有影厅
    selectedHalls.value = hallList.value.map(hall => hall.code)
  }
  else {
    // 取消全选：清空选择
    selectedHalls.value = []
  }
}

// 暴露方法给父组件
defineExpose({
  fetchHallList,
  getSelectedHalls: () => {
    const selectedHallObjects = hallList.value.filter(hall => selectedHalls.value.includes(hall.code))
    return selectedHallObjects.map(hall => ({
      hallId: hall.code,
      hallName: hall.name,
    }))
  },
  getAllHalls: () => hallList.value,
})

onMounted(() => {
  if (props.cinemaId) {
    fetchHallList()
  }
})
</script>

<template>
  <div class="hall-selector">
    <div class="cinema-info">
      <el-text class="cinema-name">
        {{ cinemaName }}
      </el-text>
      <el-text size="small" type="info">
        {{ cinemaId }}
      </el-text>
    </div>

    <div class="hall-selection">
      <div class="selection-header">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          :disabled="disabled"
          @change="handleSelectAll"
        >
          全选影厅
        </el-checkbox>
        <el-text size="small" type="info">
          已选择 {{ selectedHalls.length }} / {{ hallList.length }} 个影厅
        </el-text>
      </div>

      <div v-loading="isLoading" class="hall-list">
        <!--        {{ selectedHalls }} -->
        <div v-if="hallList.length === 0 && !isLoading" class="empty-state">
          <el-empty :image-size="60" description="暂无影厅数据" />
        </div>
        <el-checkbox-group
          v-else
          v-model="selectedHalls"
          :disabled="disabled"
        >
          <div class="hall-grid">
            <el-checkbox
              v-for="hall in hallList"
              :key="hall.code"
              :value="hall.code"
              class="hall-item"
            >
              <div class="hall-content">
                <div class="hall-name">
                  {{ hall.name }}
                </div>
                <div class="hall-details">
                  <el-tag size="small" type="info">
                    {{ hall.type || '标准厅' }}
                  </el-tag>
                  <span class="seat-count">{{ hall.seatCount }}座</span>
                </div>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.hall-selector {
  overflow: hidden;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;

  .cinema-info {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color);

    .cinema-name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .hall-selection {
    padding: 16px;

    .selection-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 12px;
      margin-bottom: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .hall-list {
      min-height: auto;

      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 120px;
      }

      .hall-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;

        .hall-item {
          width: 100%;
          margin: 0;
          height: auto;

          :deep(.el-checkbox__label) {
            width: 100%;
            padding-left: 8px;
          }

          .hall-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding: 8px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            transition: all 0.2s;

            &:hover {
              background-color: var(--el-color-primary-light-9);
              border-color: var(--el-color-primary);
            }

            .hall-name {
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 500;
              color: var(--el-text-color-primary);
              white-space: nowrap;
            }

            .hall-details {
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 12px;

              .seat-count {
                color: var(--el-text-color-secondary);
              }
            }
          }
        }

        .hall-item.is-checked {
          .hall-content {
            background-color: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .hall-selector {
    .hall-selection {
      .hall-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
