<script setup lang="ts">
import {
  ArrowLeft,
  HomeFilled,
  Refresh,
  WarningFilled,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// ==================== Props ====================

interface Props {
  errorMessage?: string
  errorDetails?: string
  showDetails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  errorMessage: '',
  errorDetails: '',
  showDetails: false,
})

// ==================== 响应式数据 ====================

const router = useRouter()

// ==================== 方法定义 ====================

/**
 * 重新加载页面
 */
function handleReload() {
  try {
    window.location.reload()
  }
  catch (error) {
    ElMessage.error('重新加载失败')
  }
}

/**
 * 返回上一页
 */
function handleGoBack() {
  try {
    if (window.history.length > 1) {
      router.go(-1)
    }
    else {
      router.push('/')
    }
  }
  catch (error) {
    ElMessage.error('返回失败')
    router.push('/')
  }
}

/**
 * 回到首页
 */
function handleGoHome() {
  try {
    router.push('/')
  }
  catch (error) {
    window.location.href = '/'
  }
}
</script>

<template>
  <div class="error-boundary">
    <div class="error-content">
      <div class="error-icon">
        <ElIcon size="64" color="#f56c6c">
          <WarningFilled />
        </ElIcon>
      </div>

      <h2 class="error-title">
        页面加载失败
      </h2>

      <p class="error-message">
        {{ errorMessage || '抱歉，页面加载时出现了问题' }}
      </p>

      <div v-if="showDetails && errorDetails" class="error-details">
        <ElCollapse>
          <ElCollapseItem title="错误详情" name="details">
            <pre class="error-stack">{{ errorDetails }}</pre>
          </ElCollapseItem>
        </ElCollapse>
      </div>

      <div class="error-actions">
        <ElButton type="primary" @click="handleReload">
          <ElIcon><Refresh /></ElIcon>
          重新加载
        </ElButton>

        <ElButton @click="handleGoBack">
          <ElIcon><ArrowLeft /></ElIcon>
          返回上页
        </ElButton>

        <ElButton @click="handleGoHome">
          <ElIcon><HomeFilled /></ElIcon>
          回到首页
        </ElButton>
      </div>

      <div class="error-tips">
        <h4>可能的解决方案：</h4>
        <ul>
          <li>检查网络连接是否正常</li>
          <li>清除浏览器缓存后重试</li>
          <li>尝试刷新页面</li>
          <li>如果问题持续存在，请联系技术支持</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  background: var(--el-bg-color);
}

.error-content {
  max-width: 600px;
  text-align: center;

  .error-icon {
    margin-bottom: 24px;
  }

  .error-title {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .error-message {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: var(--el-text-color-regular);
    line-height: 1.6;
  }

  .error-details {
    margin-bottom: 32px;
    text-align: left;

    .error-stack {
      background: var(--el-fill-color-light);
      padding: 16px;
      border-radius: 6px;
      font-size: 12px;
      line-height: 1.4;
      color: var(--el-text-color-regular);
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
  }

  .error-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-bottom: 32px;
    flex-wrap: wrap;
  }

  .error-tips {
    text-align: left;
    padding: 20px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
    border-left: 4px solid var(--el-color-primary);

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary {
    padding: 20px 16px;
  }

  .error-content {
    .error-title {
      font-size: 20px;
    }

    .error-message {
      font-size: 14px;
    }

    .error-actions {
      flex-direction: column;
      align-items: stretch;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
