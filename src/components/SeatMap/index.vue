<!-- /src/components/SeatMap/index.vue -->
<script setup lang="ts">
import type { ScheduleSeat } from '#/cinema'
import { computed, ref } from 'vue'

interface Props {
  seats: ScheduleSeat[]
}

const props = defineProps<Props>()

// 格式化座位状态
function formatSeatStatus(status: string) {
  const statusMap: Record<string, string> = {
    Available: '可出售',
    Locked: '已锁定',
    Sold: '已售出',
    Booked: '已预订',
    Unavailable: '不可用',
    Isolate: '隔离座',
  }
  return statusMap[status] || status
}

// 获取状态对应的颜色
function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    Available: 'bg-green-500',
    Sold: 'bg-red-500',
    Locked: 'bg-orange-500',
    Booked: 'bg-yellow-500',
    Unavailable: 'bg-gray-500',
    Isolate: 'bg-gray-400',
  }
  return colorMap[status] || 'bg-gray-300'
}

// 计算座位图的边界
const seatBounds = computed(() => {
  if (!props.seats.length) {
    return { minX: 0, maxX: 0, minY: 0, maxY: 0 }
  }

  let minX = Infinity
  let maxX = -Infinity
  let minY = Infinity
  let maxY = -Infinity

  props.seats.forEach((seat) => {
    minX = Math.min(minX, seat.xCoord)
    maxX = Math.max(maxX, seat.xCoord)
    minY = Math.min(minY, seat.yCoord)
    maxY = Math.max(maxY, seat.yCoord)
  })

  return { minX, maxX, minY, maxY }
})

// 计算座位图尺寸
const mapDimensions = computed(() => {
  const padding = 20
  const cellSize = 20
  return {
    width: (seatBounds.value.maxX - seatBounds.value.minX + 1) * cellSize + padding * 2,
    height: (seatBounds.value.maxY - seatBounds.value.minY + 1) * cellSize + padding * 2,
    cellSize,
    padding,
  }
})

// 转换坐标为实际位置（翻转Y轴）
function getSeatPosition(x: number, y: number) {
  const { minX, maxY } = seatBounds.value
  const { cellSize, padding } = mapDimensions.value
  return {
    x: (x - minX) * cellSize + padding,
    y: (maxY - y) * cellSize + padding,
  }
}

// Tooltip相关
const tooltipVisible = ref(false)
const tooltipContent = ref('')
const tooltipPosition = ref({ x: 0, y: 0 })

function showTooltip(event: MouseEvent, seat: ScheduleSeat) {
  tooltipContent.value = `
    座位编码: ${seat.seatCode}
    位置: ${seat.rowNum}排${seat.columnNum}座
    状态: ${formatSeatStatus(seat.status)}
    座位等级: ${seat.levelName || '无'}
    会员限制: ${seat.memberLevelCode || '无'}
    坐标: (${seat.xCoord}, ${seat.yCoord})
  `.trim()

  tooltipPosition.value = { x: event.clientX, y: event.clientY }
  tooltipVisible.value = true
}

function hideTooltip() {
  tooltipVisible.value = false
}
</script>

<template>
  <div class="relative">
    <div
      class="relative overflow-auto border border-gray-300 rounded bg-gray-100"
      style="max-height: 500px; min-height: 300px;"
    >
      <svg
        :width="mapDimensions.width"
        :height="mapDimensions.height"
        class="mx-auto block"
      >
        <!-- 座位 -->
        <g v-for="seat in seats" :key="seat.seatCode">
          <rect
            :x="getSeatPosition(seat.xCoord, seat.yCoord).x - mapDimensions.cellSize / 2"
            :y="getSeatPosition(seat.xCoord, seat.yCoord).y - mapDimensions.cellSize / 2"
            :width="mapDimensions.cellSize - 2"
            :height="mapDimensions.cellSize - 2"
            :class="`cursor-pointer ${getStatusColor(seat.status)}`"
            rx="2"
            @mouseenter="(e) => showTooltip(e, seat)"
            @mouseleave="hideTooltip"
          />
        </g>
      </svg>
    </div>

    <!-- 状态图例 -->
    <div class="mt-4 flex flex-wrap gap-2">
      <div
        v-for="(label, status) in {
          Available: '可出售',
          Sold: '已售出',
          Locked: '已锁定',
          Booked: '已预订',
          Unavailable: '不可用',
          Isolate: '隔离座',
        }" :key="status" class="mr-3 flex items-center"
      >
        <div
          class="mr-1 h-4 w-4"
          :class="getStatusColor(status)"
        />
        <span class="text-sm">{{ label }}</span>
      </div>
    </div>

    <!-- Tooltip -->
    <div
      v-if="tooltipVisible"
      class="fixed z-10 whitespace-pre-line rounded bg-black p-2 text-xs text-white"
      :style="{
        left: `${tooltipPosition.x + 10}px`,
        top: `${tooltipPosition.y + 10}px`,
        pointerEvents: 'none',
      }"
    >
      {{ tooltipContent }}
    </div>
  </div>
</template>

<style scoped>
/* 可以根据需要添加额外样式 */
</style>
