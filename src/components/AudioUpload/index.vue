<script setup lang="ts">
import type { UploadFile, UploadProps } from 'element-plus'
import type { PropType } from 'vue'
import { Upload } from '@element-plus/icons-vue'
import { ElButton, ElMessage, ElUpload } from 'element-plus'
import { ref } from 'vue'
import { uploadAudio } from '@/api/modules/media'

defineProps({
  uploadText: {
    type: String,
    default: '上传音频',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  headers: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({}),
  },
})

const emits = defineEmits(['onSuccess', 'onRemove'])
const fileList = ref<UploadFile[]>([])

function beforeUpload(file: any) {
  const isAudio = file.raw.type.startsWith('audio/')
  if (!isAudio) {
    ElMessage.error('请上传音频文件')
    return false
  }
  return true
}

const handleUpload: UploadProps['httpRequest'] = async (options) => {
  const { file } = options
  const formData = new FormData()
  formData.append('file', file as File)

  try {
    const response = await uploadAudio(formData)
    options.onSuccess(response.data)
    ElMessage.success('上传成功')
  }
  catch (error) {
    options.onError(error as any)
    ElMessage.error('上传失败，请重试')
  }
}

function handleSuccess(response: any, file: UploadFile) {
  emits('onSuccess', response, file)
}

function handleError(err: any, _file: UploadFile) {
  console.error('上传失败:', err)
}

function handleRemove(_file: UploadFile) {
  emits('onRemove', _file)
  return true
};

function handlePreview(file: UploadFile) {
  // 音频预览逻辑
  if (file.raw) {
    const audioUrl = URL.createObjectURL(file.raw)
    const audio = new Audio(audioUrl)
    audio.play().catch(e => console.error('播放失败:', e))
  }
}
</script>

<template>
  <ElUpload
    v-model:file-list="fileList"
    :http-request="handleUpload"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-remove="handleRemove"
    :show-file-list="true"
    :headers="headers"
    accept="audio/*"
    :disabled="disabled"
  >
    <ElButton :disabled="disabled" type="primary">
      <Upload class="mr-2" />
      {{ uploadText }}
    </ElButton>
    <template #file="{ file }">
      <div class="file-item">
        <div class="file-name">
          {{ file.name }}
        </div>
        <div class="file-actions">
          <ElButton
            v-if="file.status === 'success'"
            size="small"
            link
            @click.stop="handlePreview(file)"
          >
            预览
          </ElButton>
          <ElButton size="small" link @click.stop="handleRemove(file)">
            移除
          </ElButton>
        </div>
      </div>
    </template>
  </ElUpload>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.file-actions {
  display: flex;
  gap: 8px;
}
</style>
