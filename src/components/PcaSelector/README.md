# PcaSelector 省市区选择器

一个基于最新数据结构的现代化省市区三级联动选择器组件，支持多种输出格式、搜索功能和灵活配置。

## 🌟 特性

- 🎯 **数据准确**: 基于 `@province-city-china` 最新数据源
- 🔄 **灵活联动**: 支持2级（省市）或3级（省市区）联动
- 📤 **多种格式**: 支持代码、名称、混合三种输出格式
- 🔍 **智能搜索**: 内置搜索功能，支持按层级搜索
- 🎨 **现代UI**: 基于Element Plus，支持响应式设计
- ⚡ **高性能**: 优化的数据处理和渲染机制
- 🛡️ **类型安全**: 完整的TypeScript支持
- 📱 **移动适配**: 完美支持移动端使用

## 📦 安装

组件已集成到项目中，无需额外安装。

```bash
# 如果需要单独使用，安装依赖
npm install @province-city-china/province @province-city-china/city @province-city-china/area
```

## 🚀 基础用法

### 三级联动（默认）

```vue
<template>
  <PcaSelector v-model="selectedArea" @change="handleChange" />
</template>

<script setup>
import { ref } from 'vue'

const selectedArea = ref(['11', '01', '110105'])

function handleChange(value, selectedData) {
  console.log('选中值:', value)
  console.log('详细信息:', selectedData)
}
</script>
```

### 二级联动（省市）

```vue
<template>
  <PcaSelector v-model="selectedArea" :level="2" />
</template>

<script setup>
const selectedArea = ref(['11', '01'])
</script>
```

## 🎛️ 配置选项

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string[]` \| `string` | `[]` | 绑定值 |
| `level` | `2` \| `3` | `3` | 联动级别，2为省市，3为省市区 |
| `placeholder` | `string[]` | `['请选择省份', '请选择城市', '请选择区县']` | 占位符文本 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `clearable` | `boolean` | `true` | 是否可清空 |
| `size` | `'large'` \| `'default'` \| `'small'` | `'default'` | 尺寸 |
| `separator` | `string` | `' / '` | 分隔符 |
| `showAllLevels` | `boolean` | `true` | 是否显示所有层级 |
| `filterable` | `boolean` | `false` | 是否可搜索 |
| `format` | `'code'` \| `'name'` \| `'both'` | `'code'` | 输出格式 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value: string[] \| string` | 值变化时触发 |
| `change` | `value: string[] \| string, selectedData: SelectedItem[]` | 选择变化时触发 |

### Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getCodes` | - | `string[]` | 获取选中的代码数组 |
| `getNames` | - | `string[]` | 获取选中的名称数组 |
| `getFullAddress` | - | `string` | 获取完整地址字符串 |
| `searchByKeyword` | `keyword: string, level?: 1\|2\|3` | `Array` | 搜索地区 |
| `resetSelection` | - | `void` | 重置选择 |

## 📋 输出格式

### 代码格式（默认）

```vue
<PcaSelector v-model="codes" format="code" />
```

**输出**: `['11', '01', '110105']`

### 名称格式

```vue
<PcaSelector v-model="names" format="name" />
```

**输出**: `"北京市 / 北京市 / 朝阳区"`

### 混合格式

```vue
<PcaSelector v-model="both" format="both" />
```

**输出**: `"北京市(11) / 北京市(01) / 朝阳区(110105)"`

## 🔧 高级用法

### 自定义配置

```vue
<template>
  <PcaSelector 
    v-model="area"
    :placeholder="['选择省份', '选择城市', '选择区县']"
    :separator=" | "
    :filterable="true"
    :clearable="true"
    size="large"
    format="name"
    @change="handleChange"
  />
</template>
```

### 表单验证

```vue
<template>
  <el-form ref="formRef" :model="form" :rules="rules">
    <el-form-item label="所在地区" prop="area">
      <PcaSelector v-model="form.area" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'

const formRef = ref()
const form = ref({
  area: []
})

const rules = {
  area: [
    { required: true, message: '请选择所在地区', trigger: 'change' }
  ]
}

function submitForm() {
  formRef.value?.validate((valid) => {
    if (valid) {
      console.log('表单数据:', form.value)
    }
  })
}
</script>
```

### API 方法调用

```vue
<template>
  <div>
    <PcaSelector ref="pcaRef" v-model="area" />
    <div class="buttons">
      <el-button @click="getCodes">获取代码</el-button>
      <el-button @click="getNames">获取名称</el-button>
      <el-button @click="getFullAddress">获取完整地址</el-button>
      <el-button @click="searchArea">搜索</el-button>
      <el-button @click="resetSelection">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const pcaRef = ref()
const area = ref(['11', '01', '110105'])

function getCodes() {
  const codes = pcaRef.value.getCodes()
  console.log('代码数组:', codes) // ['11', '01', '110105']
}

function getNames() {
  const names = pcaRef.value.getNames()
  console.log('名称数组:', names) // ['北京市', '北京市', '朝阳区']
}

function getFullAddress() {
  const address = pcaRef.value.getFullAddress()
  console.log('完整地址:', address) // '北京市北京市朝阳区'
}

function searchArea() {
  const results = pcaRef.value.searchByKeyword('朝阳', 3)
  console.log('搜索结果:', results)
}

function resetSelection() {
  pcaRef.value.resetSelection()
}
</script>
```

### 搜索功能

```vue
<template>
  <div>
    <div class="search-controls">
      <el-input
        v-model="keyword"
        placeholder="搜索地区"
        @input="handleSearch"
      />
      <el-select v-model="searchLevel" placeholder="选择层级">
        <el-option label="全部" :value="0" />
        <el-option label="省份" :value="1" />
        <el-option label="城市" :value="2" />
        <el-option label="区县" :value="3" />
      </el-select>
    </div>
    
    <PcaSelector ref="pcaRef" v-model="area" />
    
    <div v-if="searchResults.length > 0" class="results">
      <h4>搜索结果:</h4>
      <ul>
        <li 
          v-for="item in searchResults" 
          :key="item.code"
          @click="selectItem(item)"
        >
          {{ item.name }} ({{ item.code }})
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const pcaRef = ref()
const area = ref([])
const keyword = ref('')
const searchLevel = ref(0)
const searchResults = ref([])

function handleSearch() {
  if (!keyword.value.trim()) {
    searchResults.value = []
    return
  }
  
  const level = searchLevel.value === 0 ? undefined : searchLevel.value
  searchResults.value = pcaRef.value.searchByKeyword(keyword.value, level)
}

function selectItem(item) {
  // 根据选中项构建完整路径
  if ('area' in item) {
    // 区县
    area.value = [item.province, item.city, item.code]
  } else if ('city' in item) {
    // 城市
    area.value = [item.province, item.code]
  } else {
    // 省份
    area.value = [item.code]
  }
}
</script>
```

## 📊 数据结构

### 省份数据

```typescript
interface ProvinceItem {
  code: string    // 省份代码，如 "11"
  name: string    // 省份名称，如 "北京市"
}
```

### 城市数据

```typescript
interface CityItem {
  code: string      // 城市代码，如 "01"
  name: string      // 城市名称，如 "石家庄市"
  province: string  // 所属省份代码，如 "13"
}
```

### 区县数据

```typescript
interface AreaItem {
  code: string      // 完整区县代码，如 "110101"
  name: string      // 区县名称，如 "东城区"
  province: string  // 所属省份代码，如 "11"
  city: string      // 所属城市代码，如 "01"
  area: string      // 区县代码，如 "01"
}
```

### 选中项数据

```typescript
interface SelectedItem {
  code: string           // 代码
  name: string           // 名称
  level: 1 | 2 | 3      // 层级：1-省份，2-城市，3-区县
  province?: string      // 省份代码
  city?: string         // 城市代码
  area?: string         // 区县代码
}
```

## 🎨 样式定制

### CSS 变量

组件使用 Element Plus 的 CSS 变量，支持主题定制：

```css
:root {
  --el-color-primary: #409eff;
  --el-border-radius-base: 4px;
  --el-font-size-base: 14px;
}
```

### 自定义样式

```vue
<template>
  <PcaSelector class="custom-pca-selector" v-model="area" />
</template>

<style scoped>
.custom-pca-selector {
  --el-select-width: 150px;
}

.custom-pca-selector :deep(.selector-container) {
  gap: 16px;
}

.custom-pca-selector :deep(.selected-result) {
  margin-top: 12px;
}
</style>
```

## 📱 响应式设计

组件内置响应式支持：

- **桌面端**: 水平排列，完整功能
- **平板端**: 自适应布局
- **移动端**: 垂直排列，优化触摸体验

```css
/* 移动端适配 */
@media (max-width: 768px) {
  .selector-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .selector-container .el-select {
    width: 100%;
  }
}
```

## 🔍 常见问题

### Q: 如何设置默认值？

A: 使用 `v-model` 绑定代码数组：

```vue
<PcaSelector v-model="['11', '01', '110105']" />
```

### Q: 如何获取完整的地址字符串？

A: 使用 `format="name"` 或调用 `getFullAddress()` 方法：

```vue
<!-- 方式1: 使用format -->
<PcaSelector v-model="address" format="name" />

<!-- 方式2: 使用方法 -->
<PcaSelector ref="pcaRef" v-model="codes" />
<script>
const address = pcaRef.value.getFullAddress()
</script>
```

### Q: 如何只显示省市两级？

A: 设置 `level` 属性为 `2`：

```vue
<PcaSelector v-model="area" :level="2" />
```

### Q: 如何自定义占位符？

A: 使用 `placeholder` 属性：

```vue
<PcaSelector 
  v-model="area" 
  :placeholder="['选择省份', '选择城市', '选择区县']" 
/>
```

### Q: 如何启用搜索功能？

A: 设置 `filterable` 为 `true`：

```vue
<PcaSelector v-model="area" :filterable="true" />
```

### Q: 如何处理数据加载失败？

A: 组件会自动处理加载失败的情况，你可以监听控制台错误信息：

```javascript
// 组件内部已处理
try {
  // 加载数据
} catch (error) {
  console.error('加载省市区数据失败:', error)
}
```

## 🚀 性能优化

### 数据缓存

组件会自动缓存加载的数据，避免重复请求：

```typescript
// 数据只在首次挂载时加载
onMounted(() => {
  loadData()
})
```

### 计算属性优化

使用计算属性优化数据筛选：

```typescript
const cityList = computed(() => {
  if (!selectedProvince.value) return []
  return filterCitiesByProvince(cities.value, selectedProvince.value)
})
```

### 事件防抖

对于搜索功能，建议添加防抖处理：

```vue
<script setup>
import { debounce } from 'lodash-es'

const handleSearch = debounce((keyword) => {
  // 搜索逻辑
}, 300)
</script>
```

## 🧪 测试

### 单元测试

```typescript
import { mount } from '@vue/test-utils'
import PcaSelector from '@/components/PcaSelector/index.vue'

describe('PcaSelector', () => {
  it('should render correctly', () => {
    const wrapper = mount(PcaSelector, {
      props: {
        modelValue: ['11', '01', '110105']
      }
    })
    expect(wrapper.exists()).toBe(true)
  })

  it('should emit change event', async () => {
    const wrapper = mount(PcaSelector)
    // 测试逻辑
  })
})
```

### E2E 测试

```typescript
// cypress/integration/pca-selector.spec.ts
describe('PcaSelector E2E', () => {
  it('should select area correctly', () => {
    cy.visit('/component_example/extend/pca-selector')
    cy.get('.pca-selector').should('be.visible')
    // 测试交互
  })
})
```

## 📈 更新日志

### v1.0.0 (2024-12)

- ✅ 初始版本发布
- ✅ 支持2级/3级联动
- ✅ 多种输出格式
- ✅ 搜索功能
- ✅ 完整的TypeScript支持
- ✅ 响应式设计
- ✅ 表单验证支持

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境

```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test
```

### 提交规范

```bash
# 功能开发
git commit -m "feat: 添加新功能"

# 问题修复
git commit -m "fix: 修复某个问题"

# 文档更新
git commit -m "docs: 更新文档"
```

## 📄 许可证

MIT License

## 📞 技术支持

如有问题或建议，请：

1. 查看本文档的常见问题部分
2. 检查浏览器控制台错误信息
3. 访问演示页面: `/component_example/extend/pca-selector`
4. 提交 Issue 或联系开发团队

---

**作者**: 开发团队  
**最后更新**: 2024年12月  
**版本**: v1.0.0  
**演示地址**: `/component_example/extend/pca-selector`