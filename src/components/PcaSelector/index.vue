<script setup lang="ts">
import area from '@province-city-china/area'
import city from '@province-city-china/city'
import province from '@province-city-china/province'
import { computed, ref, watch } from 'vue'

defineOptions({
  name: 'PcaSelector',
})

const props = withDefaults(defineProps<Props>(), {
  level: 3,
  placeholder: () => ['请选择省份', '请选择城市', '请选择区县'],
  disabled: false,
  clearable: true,
  size: 'default',
  separator: ' / ',
  showAllLevels: true,
  filterable: false,
  // 是否显示 tag
  showTag: false,
})

const emit = defineEmits<Emits>()

interface Props {
  modelValue?: string
  level?: 2 | 3 // 2级联动(省市) 或 3级联动(省市区)
  placeholder?: string[]
  disabled?: boolean
  clearable?: boolean
  size?: 'large' | 'default' | 'small'
  separator?: string
  showAllLevels?: boolean
  filterable?: boolean
  showTag?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string[] | string): void
  (e: 'change', value: string[] | string, selectedData: any[]): void
}

// 响应式数据
const selectedProvince = ref('')
const selectedCity = ref('')
const selectedArea = ref('')

// 计算属性 - 省份列表
const provinceList = computed(() => {
  // console.log('省份列表:', province)
  return province
})

// 计算属性 - 城市列表
const cityList = computed(() => {
  // eslint-disable-next-line style/max-statements-per-line
  if (!selectedProvince.value) { return [] }
  return city.filter(item => item.province === selectedProvince.value)
})

// 计算属性 - 区县列表
const areaList = computed(() => {
  // console.log(area)
  // eslint-disable-next-line style/max-statements-per-line
  if (!selectedCity.value || props.level === 2) { return [] }
  return area.filter(item => (item.city === selectedCity.value
    && item.province === selectedProvince.value))
})

// 计算属性 - 当前选中的完整数据
const selectedData = computed(() => {
  const result = []

  if (selectedProvince.value) {
    const _province = province.find(item => item.province === selectedProvince.value)
    result.push({
      ..._province,
      level: 1,
    })
  }

  if (selectedCity.value) {
    const _city = city.find(item => (item.province === selectedProvince.value
      && item.city === selectedCity.value
    ))
    result.push({
      ..._city,
      level: 2,
    })
  }

  if (selectedArea.value && props.level === 3) {
    const _area = area.find(item => (item.city === selectedCity.value
      && item.province === selectedProvince.value
      && item.area === selectedArea.value
    ))
    result.push({
      ..._area,
      level: 3,
    })
  }
  console.log('选中的完整数据:', result)
  return result
})

// 计算属性 - 输出值
const outputValue = computed(() => {
  const codes = selectedData.value.map(item => item.code)
  const names = selectedData.value.map(item => item.name)

  if (typeof props.modelValue === 'string') {
    return props.showAllLevels ? names.join(props.separator) : names[names.length - 1] || ''
  }

  return codes
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    resetSelection()
    return
  }

  if (Array.isArray(newValue)) {
    // 数组格式：代码数组
    const [provinceCode, cityCode, areaCode] = newValue
    selectedProvince.value = provinceCode || ''
    selectedCity.value = cityCode || ''
    selectedArea.value = areaCode || ''
  }
  else {
    // 字符串格式：需要解析
    parseStringValue(newValue)
  }
}, { immediate: true })

// 监听选择变化
watch([selectedProvince, selectedCity, selectedArea], () => {
  emit('update:modelValue', outputValue.value)
  emit('change', outputValue.value, selectedData.value)
})

// 省份变化处理
function handleProvinceChange(value: string) {
  selectedProvince.value = value
  selectedCity.value = ''
  selectedArea.value = ''
}

// 城市变化处理
function handleCityChange(value: string) {
  selectedCity.value = value
  selectedArea.value = ''
}

// 区县变化处理
function handleAreaChange(value: string) {
  selectedArea.value = value
}

// 重置选择
function resetSelection() {
  selectedProvince.value = ''
  selectedCity.value = ''
  selectedArea.value = ''
}

// 解析字符串值（用于字符串格式的modelValue）
function parseStringValue(value: string) {
  // 这里可以根据实际需求实现字符串解析逻辑
  // 例如：根据分隔符分割，然后匹配对应的代码
  console.log('解析字符串值:', value)
}

// 清空选择
function handleClear() {
  resetSelection()
}

// 获取完整的地址字符串
function getFullAddress() {
  return selectedData.value.map(item => item.name).join('')
}

// 获取选中的代码数组
function getCodes() {
  return selectedData.value.map(item => item.code)
}

// 获取选中的名称数组
function getNames() {
  return selectedData.value.map(item => item.name)
}

// 暴露方法
defineExpose({
  getFullAddress,
  getCodes,
  getNames,
  resetSelection,
})
</script>

<template>
  <div class="pca-selector">
    <div class="selector-container">
      <!-- 省份选择 -->
      <el-select
        :model-value="selectedProvince"
        :placeholder="placeholder[0]"
        :disabled="disabled"
        :clearable="clearable"
        :size="size"
        :filterable="filterable"
        @update:model-value="handleProvinceChange"
        @clear="handleClear"
      >
        <el-option
          v-for="item in provinceList"
          :key="item.code"
          :label="item.name"
          :value="item.province"
        />
      </el-select>
      <!-- 城市选择 -->
      <el-select
        :model-value="selectedCity"
        :placeholder="placeholder[1]"
        :disabled="disabled || !selectedProvince"
        :clearable="clearable"
        :size="size"
        :filterable="filterable"
        @update:model-value="handleCityChange"
      >
        <el-option
          v-for="item in cityList"
          :key="item.code"
          :label="item.name"
          :value="item.city"
        />
      </el-select>

      <!-- 区县选择 (仅3级联动时显示) -->
      <el-select
        v-if="level === 3"
        :model-value="selectedArea"
        :placeholder="placeholder[2]"
        :disabled="disabled || !selectedCity"
        :clearable="clearable"
        :size="size"
        :filterable="filterable"
        @update:model-value="handleAreaChange"
      >
        <el-option
          v-for="item in areaList"
          :key="item.code"
          :label="item.name"
          :value="item.area"
        />
      </el-select>
    </div>

    <!-- 选中结果显示 -->
    <div v-if="selectedData.length > 0 && props.showTag" class="selected-result">
      <el-tag
        v-for="(item, index) in selectedData"
        :key="item.code"
        :type="index === selectedData.length - 1 ? 'primary' : 'info'"
        size="small"
      >
        {{ item.name }}
      </el-tag>
    </div>
  </div>
</template>

<style scoped>
.pca-selector {
  width: 100%;
}

.selector-container {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.selector-container .el-select {
  flex: 1;
  min-width: 120px;
}

.selected-result {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selector-container {
    flex-direction: column;
    gap: 8px;
  }

  .selector-container .el-select {
    width: 100%;
  }
}
</style>
