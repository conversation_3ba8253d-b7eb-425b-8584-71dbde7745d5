<!--
 * FilmSelector 组件使用示例
 * 展示如何使用重新设计的影片选择器组件
-->
<template>
  <div class="film-selector-example">
    <FaPageHeader title="影片选择器示例" description="展示FilmSelector组件的各种使用方式" />

    <FaPageMain>
      <!-- 基础用法 -->
      <el-card class="example-card">
        <template #header>
          <h3>基础用法</h3>
        </template>

        <div class="example-content">
          <FilmSelector
            v-model="basicSelection"
            @change="handleBasicChange"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(basicSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 多选模式 -->
      <el-card class="example-card">
        <template #header>
          <h3>多选模式（默认）</h3>
        </template>

        <div class="example-content">
          <FilmSelector
            v-model="multipleSelection"
            @change="handleMultipleChange"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(multipleSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 单选模式 -->
      <el-card class="example-card">
        <template #header>
          <h3>单选模式</h3>
        </template>

        <div class="example-content">
          <FilmSelector
            v-model="singleSelection"
            :multiple="false"
            placeholder="请选择一部影片"
            @change="handleSingleChange"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(singleSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 预设选择 -->
      <el-card class="example-card">
        <template #header>
          <h3>预设选择的影片</h3>
        </template>

        <div class="example-content">
          <div class="mb-4">
            <el-button @click="setPresetFilms">设置预设影片</el-button>
            <el-button @click="clearPresetFilms">清空选择</el-button>
          </div>

          <FilmSelector
            v-model="presetSelection"
            @change="handlePresetChange"
          />

          <div class="result-display">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(presetSelection, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </FaPageMain>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FilmSelector from './index.vue'

defineOptions({
  name: 'FilmSelectorExample',
})

// 响应式数据
const basicSelection = ref([])
const multipleSelection = ref([])
const singleSelection = ref([])
const presetSelection = ref([])

// 事件处理函数
function handleBasicChange(films) {
  console.log('基础选择变化:', films)
}

function handleMultipleChange(films) {
  console.log('多选变化:', films)
}

function handleSingleChange(films) {
  console.log('单选变化:', films)
}

function handlePresetChange(films) {
  console.log('预设选择变化:', films)
}

// 设置预设影片
function setPresetFilms() {
  presetSelection.value = [
    {
      filmId: 'film001',
      filmName: '流浪地球3',
      filmCode: '*********',
    },
    {
      filmId: 'film002',
      filmName: '复仇者联盟5',
      filmCode: '*********',
    },
  ]
}

// 清空预设影片
function clearPresetFilms() {
  presetSelection.value = []
}
</script>

<style scoped>
.film-selector-example {
  padding: 20px;
}

.example-card {
  margin-bottom: 24px;
}

.example-content {
  padding: 16px 0;
}

.result-display {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.result-display h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.result-display pre {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .film-selector-example {
    padding: 12px;
  }

  .example-card {
    margin-bottom: 16px;
  }
}
</style>
