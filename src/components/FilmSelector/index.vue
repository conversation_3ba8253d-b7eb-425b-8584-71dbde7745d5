<!--
 * 作者：zhang
 * 时间：2023/10/27 9:55
 * 功能：影片选择器组件 - 双向绑定影片数据
 * 更新：2025/07/30 - 重新设计，保持输入输出数据格式一致
-->
<script setup lang="ts">
import { getCurrentInstance, onMounted, ref } from 'vue'
import { getFilmList } from '@/api/modules/film/index.js'

// 定义选中的影片类型 - 与输入数据格式保持一致
interface SelectedFilm {
  filmId: string | number
  filmName: string
  filmCode?: string
  [key: string]: any // 允许其他属性
}

// 定义组件的Props类型
interface Props {
  // 绑定的影片信息
  modelValue: SelectedFilm[]
  // 是否多选
  multiple?: boolean
  // 占位符
  placeholder?: string
}

// 定义Emits类型
interface Emits {
  (e: 'update:modelValue', value: SelectedFilm[]): void
  (e: 'change', value: SelectedFilm[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  multiple: true,
  placeholder: '请选择影片',
})

const emit = defineEmits<Emits>()
const proxy = getCurrentInstance()

const dialogVisible = ref(false)
const filmDataList = ref([])
const selectedFilms = ref<SelectedFilm[]>([])
const total = ref(0)
const loading = ref(false)

const searchForm = ref({
  filmName: '',
  releaseStatus: '', // 影片上映状态 0即将上映 1热映中 2已下映
  page: 1,
  size: 10,
})

onMounted(() => {
  console.log('FilmSelector mounted, modelValue:', props.modelValue)
  // 初始化选中的影片，保持原有数据格式
  selectedFilms.value = [...props.modelValue]
  query()
})

// 查询影片列表
async function query() {
  loading.value = true
  try {
    const params = {
      ...searchForm.value,
      filmName: searchForm.value.filmName || undefined,
      releaseStatus: searchForm.value.releaseStatus || undefined,
    }
    
    const res = await getFilmList(params)
    const { code, data } = res
    
    if (code === 0) {
      // 将API返回的数据转换为组件内部使用的格式
      filmDataList.value = data.content.map((item: any) => ({
        id: item.id,
        name: item.name,
        code: item.code,
        status: item.releaseStatus,
        lang: item.lang,
        duration: item.duration,
        version: item.version,
        provider: item.provider,
      }))
      total.value = data.total
    } else {
      console.error('获取影片列表失败:', res.msg)
    }
  } catch (error) {
    console.error('查询影片列表出错:', error)
  } finally {
    loading.value = false
  }
}

// 处理表格选择变化
function handleSelectionChange(films: any[]) {
  console.log('选择的影片:', films)
  // 将选中的影片转换为输出格式
  selectedFilms.value = films.map(film => ({
    filmId: film.id,
    filmName: film.name,
    filmCode: film.code,
  }))
}

// 确认选中影片
function confirm() {
  dialogVisible.value = false
  // 输出与输入格式一致的数据
  const result = selectedFilms.value.map(film => ({
    filmId: film.filmId,
    filmName: film.filmName,
    filmCode: film.filmCode,
  }))
  emit('update:modelValue', result)
  emit('change', result)
}

const multipleTableRef = ref()

// 取消选中某一个影片
function cancel(film: any) {
  console.log('取消选中影片:', film)
  selectedFilms.value = selectedFilms.value.filter((item) => item.filmId !== film.filmId)
  // 在表格中取消选中
  const tableFilm = filmDataList.value.find(f => f.id === film.filmId)
  if (tableFilm) {
    multipleTableRef.value?.toggleRowSelection(tableFilm, false)
  }
}

// 移除标签
function removeTag(film: SelectedFilm) {
  selectedFilms.value = selectedFilms.value.filter((item) => item.filmId !== film.filmId)
  // 输出与输入格式一致的数据
  const result = selectedFilms.value.map(f => ({
    filmId: f.filmId,
    filmName: f.filmName,
    filmCode: f.filmCode,
  }))
  emit('update:modelValue', result)
  emit('change', result)
}

// 重置搜索
function resetSearch() {
  searchForm.value.filmName = ''
  searchForm.value.releaseStatus = ''
  query()
}

// 分页变化
function handlePageChange() {
  query()
}
</script>

<template>
  <div class="film-selector">
    <el-row>
      <el-col :span="24">
        <slot name="leftSlot" />
        <el-button type="primary" plain @click="dialogVisible = true">
          选择影片
        </el-button>
        
        <!-- 选中影片显示 -->
        <el-card
          v-show="props.modelValue.length > 0"
          shadow="never"
          class="mt-3"
          :body-style="{ padding: '12px' }"
        >
          <div class="selected-films">
            <div class="mb-2 text-sm text-gray-600">
              已选择 {{ props.modelValue.length }} 部影片：
            </div>
            <div class="flex flex-wrap gap-2">
              <el-tag
                v-for="film in props.modelValue"
                :key="film.filmId"
                size="default"
                closable
                @close="removeTag(film)"
              >
                {{ film.filmName }}
                <span v-if="film.filmCode" class="ml-1 text-xs opacity-70">
                  ({{ film.filmCode }})
                </span>
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 选择对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      title="选择影片"
      width="85%" 
      draggable
      destroy-on-close
    >
      <template #header>
        <div class="dialog-header">
          <h3 class="text-lg font-medium">选择影片</h3>
          <el-divider class="my-3" />
          <el-alert
            title="请选择要添加的影片，选择后点击确定按钮保存"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </template>

      <div v-loading="loading">
        <el-row :gutter="20">
          <el-col :span="16">
            <!-- 搜索表单 -->
            <el-form :model="searchForm" inline class="mb-4">
              <el-form-item label="影片名称">
                <el-input
                  v-model="searchForm.filmName"
                  placeholder="请输入影片名称"
                  style="width: 200px;"
                  clearable
                  @keyup.enter="query"
                />
              </el-form-item>
              <el-form-item label="影片状态">
                <el-select
                  v-model="searchForm.releaseStatus"
                  placeholder="请选择影片状态"
                  style="width: 150px;"
                  clearable
                >
                  <el-option label="即将上映" value="0" />
                  <el-option label="热映中" value="1" />
                  <el-option label="已下映" value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query" :loading="loading">
                  查询
                </el-button>
                <el-button @click="resetSearch">
                  重置
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 影片列表表格 -->
            <el-table
              ref="multipleTableRef"
              :data="filmDataList"
              stripe
              border
              :row-key="row => row.id"
              height="450"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                v-if="props.multiple"
                type="selection"
                align="center"
                width="55"
                :reserve-selection="true"
              />
              <el-table-column label="影片名称" min-width="200">
                <template #default="{ row }">
                  <div class="flex items-center">
                    <div>
                      <div class="font-medium">{{ row.name }}</div>
                      <div v-if="row.lang || row.duration" class="text-xs text-gray-500 mt-1">
                        <span v-if="row.lang">{{ row.lang }}</span>
                        <span v-if="row.lang && row.duration"> · </span>
                        <span v-if="row.duration">{{ row.duration }}分钟</span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="code" label="影片编码" width="120" align="center" />
              <el-table-column label="版本" width="100" align="center">
                <template #default="{ row }">
                  <el-tag v-if="row.version" size="small" type="info">
                    {{ row.version }}
                  </el-tag>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag v-if="row.status === '0'" size="small" type="warning">
                    即将上映
                  </el-tag>
                  <el-tag v-else-if="row.status === '1'" size="small" type="success">
                    热映中
                  </el-tag>
                  <el-tag v-else size="small" type="info">
                    已下映
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="provider" label="提供商" width="100" align="center" />
            </el-table>

            <!-- 分页 -->
            <div class="mt-4 flex justify-end">
              <el-pagination
                v-model:current-page="searchForm.page"
                v-model:page-size="searchForm.size"
                :total="total"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handlePageChange"
                @current-change="handlePageChange"
              />
            </div>
          </el-col>
          
          <!-- 已选择的影片 -->
          <el-col :span="8">
            <div class="selected-panel">
              <div class="panel-header">
                <h4 class="text-base font-medium mb-3">
                  已选择的影片 ({{ selectedFilms.length }})
                </h4>
              </div>
              <el-table 
                :data="selectedFilms" 
                border 
                height="450"
                empty-text="暂未选择影片"
              >
                <el-table-column prop="filmName" label="影片名称" min-width="120">
                  <template #default="{ row }">
                    <div>
                      <div class="font-medium">{{ row.filmName }}</div>
                      <div v-if="row.filmCode" class="text-xs text-gray-500 mt-1">
                        {{ row.filmCode }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80">
                  <template #default="{ row }">
                    <el-button
                      type="danger"
                      size="small"
                      text
                      @click="cancel(row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirm" :disabled="selectedFilms.length === 0">
            确定 ({{ selectedFilms.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.film-selector {
  width: 100%;
}

.selected-films {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.dialog-header {
  padding-bottom: 0;
}

.selected-panel {
  padding-left: 16px;
}

.panel-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
  margin-bottom: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .film-selector :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .selected-panel {
    padding-left: 0;
    margin-top: 20px;
  }
}
</style>
