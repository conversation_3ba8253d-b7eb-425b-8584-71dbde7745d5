# FilmSelector 影片选择器组件

重新设计的影片选择器组件，保持输入输出数据格式一致，支持双向绑定。

## 功能特性

- ✅ 输入输出数据格式保持一致
- ✅ 支持多选和单选模式
- ✅ 实时搜索和筛选
- ✅ 分页支持
- ✅ 响应式设计
- ✅ TypeScript 支持
- ✅ 双向数据绑定

## 基础用法

```vue
<template>
  <FilmSelector 
    v-model="selectedFilms"
    @change="handleFilmChange"
  />
</template>

<script setup>
import FilmSelector from '@/components/FilmSelector/index.vue'

// 输入数据格式
const selectedFilms = ref([
  {
    filmId: 'film001',
    filmName: '流浪地球3',
    filmCode: '*********'
  }
])

function handleFilmChange(films) {
  console.log('选择的影片:', films)
  // 输出数据格式与输入完全一致
  // [{ filmId: 'xxx', filmName: 'xxx', filmCode: 'xxx' }]
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `modelValue` | `SelectedFilm[]` | `[]` | 绑定的影片数据 |
| `multiple` | `boolean` | `true` | 是否支持多选 |
| `placeholder` | `string` | `'请选择影片'` | 占位符文本 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `SelectedFilm[]` | 选择变化时触发 |
| `change` | `SelectedFilm[]` | 选择变化时触发 |

## 数据结构

### SelectedFilm 类型

```typescript
interface SelectedFilm {
  filmId: string | number  // 影片ID
  filmName: string         // 影片名称
  filmCode?: string        // 影片编码（可选）
  [key: string]: any       // 允许其他属性
}
```

## 使用场景

### 1. 基础多选

```vue
<FilmSelector v-model="films" />
```

### 2. 单选模式

```vue
<FilmSelector 
  v-model="film"
  :multiple="false"
/>
```

### 3. 预设数据

```vue
<template>
  <FilmSelector v-model="presetFilms" />
</template>

<script setup>
const presetFilms = ref([
  { filmId: 1, filmName: '流浪地球3', filmCode: '*********' },
  { filmId: 2, filmName: '复仇者联盟5', filmCode: '*********' }
])
</script>
```

### 4. 监听变化

```vue
<template>
  <FilmSelector 
    v-model="selectedFilms"
    @change="handleChange"
  />
</template>

<script setup>
function handleChange(films) {
  console.log('选择变化:', films)
  // 处理选择变化的逻辑
}
</script>
```

## 组件特性

### 1. 数据格式一致性

组件确保输入和输出的数据格式完全一致：

```javascript
// 输入格式
const input = [
  { filmId: 'film001', filmName: '流浪地球3', filmCode: '*********' }
]

// 输出格式（完全相同）
const output = [
  { filmId: 'film001', filmName: '流浪地球3', filmCode: '*********' }
]
```

### 2. 搜索和筛选

- 支持按影片名称搜索
- 支持按影片状态筛选（即将上映、热映中、已下映）
- 支持分页浏览

### 3. 用户界面

- 清晰的选择状态显示
- 已选择影片的标签展示
- 可删除的影片标签
- 响应式对话框设计

### 4. 响应式设计

组件支持移动端适配，在小屏幕设备上会自动调整布局。

## 样式定制

组件使用 scoped 样式，可以通过以下方式进行定制：

```vue
<style>
.film-selector :deep(.el-dialog) {
  /* 自定义对话框样式 */
}

.film-selector :deep(.selected-films) {
  /* 自定义已选择影片区域样式 */
}
</style>
```

## 注意事项

1. **数据格式**: 确保传入的 `modelValue` 符合 `SelectedFilm[]` 类型
2. **必需字段**: `filmId` 和 `filmName` 是必需的字段
3. **唯一标识**: 使用 `filmId` 作为影片的唯一标识
4. **性能优化**: 组件内部使用了防抖和缓存机制，避免频繁的API调用

## 更新日志

- **v2.0.0** (2025/07/30): 重新设计，保持输入输出数据格式一致
  - 移除影院相关参数
  - 统一数据格式为 `{ filmId, filmName, filmCode }`
  - 优化UI设计和用户体验
  - 添加 TypeScript 支持
  - 增强响应式设计
  - 简化组件接口
