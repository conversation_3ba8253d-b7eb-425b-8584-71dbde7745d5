# 应用配置面板
VITE_APP_SETTING = true
# 页面标题
VITE_APP_TITLE = 潮光影
VITE_BASE_URL = https://dev.nexthuman.cn
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = 	https://dev.nexthuman.cn/zfilm/api
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = true

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = true
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip,brotli
# 是否在打包后生成存档，支持 zip 和 tar
VITE_BUILD_ARCHIVE =
